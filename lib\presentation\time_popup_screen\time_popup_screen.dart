import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import '../statistic_expenses_page/controller/statistic_expenses_controller.dart';
import '../statistic_expenses_page/models/statistic_expenses_model.dart';
import '../statistic_income_page/controller/statistic_income_controller.dart';
import '../statistic_income_page/models/statistic_income_model.dart';
import 'controller/time_popup_controller.dart';

// ignore_for_file: must_be_immutable
class TimePopupScreen extends GetWidget<TimePopupController> {
  TimePopupScreen({Key? key})
      : super(
          key: key,
        );

  StatisticIncomeController statisticIncomeController =
      Get.put(StatisticIncomeController(StatisticIncomeModel().obs));

  StatisticExpensesController statisticExpensesController =
      Get.put(StatisticExpensesController(StatisticExpensesModel().obs));

  @override
  Widget build(BuildContext context) {
    return Container(
      width: SizeUtils.width,
      height: 250.v,
      padding: EdgeInsets.symmetric(
        horizontal: 20.h,
        vertical: 16.v,
      ),
      decoration: BoxDecoration(
        color: appTheme.whiteA700,
        borderRadius: BorderRadiusStyle.customBorderTL12,
      ),
      child: _buildPopup(),
    );
  }

  /// Section Widget
  Widget _buildPopup() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            Get.back();
          },
          child: CustomImageView(
            imagePath: ImageConstant.imgCloseFill0Wgh,
            height: 24.adaptSize,
            width: 24.adaptSize,
            alignment: Alignment.centerRight,
          ),
        ),
        SizedBox(height: 2.v),
        GestureDetector(
          onTap: () {
            statisticIncomeController.isSelected.value = "Days";
            statisticIncomeController.update();
            statisticExpensesController.isSelected.value = "Days";
            statisticExpensesController.update();
            Get.back();
          },
          child: Text(
            "lbl_days".tr,
            style: CustomTextStyles.bodyLargeBlack900,
          ),
        ),
        SizedBox(height: 23.v),
        GestureDetector(
          onTap: () {
            statisticIncomeController.isSelected.value = "Week";
            statisticIncomeController.update();
            statisticExpensesController.isSelected.value = "Week";
            statisticExpensesController.update();
            Get.back();
          },
          child: Text(
            "lbl_week".tr,
            style: CustomTextStyles.bodyLargeBlack900,
          ),
        ),
        SizedBox(height: 25.v),
        GestureDetector(
          onTap: () {
            statisticIncomeController.isSelected.value = "Month";
            statisticIncomeController.update();
            statisticExpensesController.isSelected.value = "Month";
            statisticExpensesController.update();
            Get.back();
          },
          child: Text(
            "lbl_month".tr,
            style: CustomTextStyles.bodyLargeBlack900,
          ),
        ),
        SizedBox(height: 25.v),
        GestureDetector(
          onTap: () {
            statisticIncomeController.isSelected.value = "Year";
            statisticIncomeController.update();
            statisticExpensesController.isSelected.value = "Year";
            statisticExpensesController.update();
            Get.back();
          },
          child: Text(
            "lbl_year".tr,
            style: CustomTextStyles.bodyLargeBlack900,
          ),
        ),
        SizedBox(height: 8.v),
      ],
    );
  }
}
