import '../../../core/app_export.dart';import 'notification_item_model.dart';/// This class defines the variables used in the [notification_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class NotificationModel {Rx<List<NotificationItemModel>> notificationItemList = Rx([NotificationItemModel(airplaneModeIs: "Airplane mode is on either system or lo.".obs,justNow: "Just now".obs),NotificationItemModel(airplaneModeIs: "In a laoreet purus Integer turpis laoreet.".obs,justNow: "1 Min ago".obs),NotificationItemModel(airplaneModeIs: "Clicking the allow button online is trouble.".obs,justNow: "2 Min ago".obs),NotificationItemModel(airplaneModeIs: "Dubious websites functionality for bell.".obs,justNow: "4 Min ago".obs),NotificationItemModel(airplaneModeIs: "The trend shows that these attacks are.".obs,justNow: "10 Min ago".obs),NotificationItemModel(airplaneModeIs: "Websites ask for permission upon show.".obs,justNow: "20 Min ago".obs),NotificationItemModel(airplaneModeIs: "How to disable push notification android.".obs,justNow: "30 Min ago".obs),NotificationItemModel(airplaneModeIs: "Detecting if someone reading  messages.".obs,justNow: "40 Min ago".obs),NotificationItemModel(airplaneModeIs: "Airplane mode is on either system or.".obs,justNow: "Just now".obs)]);

 }
