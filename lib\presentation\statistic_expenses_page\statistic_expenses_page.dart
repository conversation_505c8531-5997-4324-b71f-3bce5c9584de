import 'package:syncfusion_flutter_charts/charts.dart';

import '../time_popup_screen/time_popup_screen.dart';
import 'widgets/statisticexpenses_item_widget.dart';
import 'models/statisticexpenses_item_model.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/statistic_expenses_controller.dart';
import 'models/statistic_expenses_model.dart';

// ignore_for_file: must_be_immutable
class StatisticExpensesPage extends StatelessWidget {
  StatisticExpensesPage({Key? key}) : super(key: key);

  StatisticExpensesController controller =
      Get.put(StatisticExpensesController(StatisticExpensesModel().obs));

  List<ChartSampleData> data = <ChartSampleData>[
    ChartSampleData('Sun', 00),
    ChartSampleData('Mon', 00),
    ChartSampleData('Tue', 00),
    ChartSampleData('Wed', 50),
    ChartSampleData('Thu', 00),
    ChartSampleData('Fri', 00),
    ChartSampleData('Sat', 20),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Container(
        width: double.maxFinite,
        decoration: AppDecoration.fillWhiteA,
        child: ListView(
          children: [
            SizedBox(height: 24.v),
            GetBuilder<StatisticExpensesController>(
                init: StatisticExpensesController(StatisticExpensesModel().obs),
                builder: (controller) {
                  return Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.h),
                    child: GestureDetector(
                      onTap: () {
                        Get.bottomSheet(
                          TimePopupScreen(),
                          isScrollControlled: true,
                          backgroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(40.h),
                              topRight: Radius.circular(40.h),
                            ),
                          ),
                        );
                      },
                      child: Container(
                        width: double.infinity,
                        height: 56.v,
                        decoration: AppDecoration.fillGray.copyWith(
                          borderRadius: BorderRadiusStyle.roundedBorder12,
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              vertical: 12.0.v, horizontal: 16.h),
                          child: Row(
                            children: [
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.only(left: 0.0),
                                  child: Text(
                                    controller.isSelected.value,
                                    style: theme.textTheme.bodyLarge,
                                  ),
                                ),
                              ),
                              CustomImageView(
                                imagePath: ImageConstant.arrowUpBlack,
                                height: 20.adaptSize,
                                width: 20.adaptSize,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }),
            SizedBox(height: 24.v),
            _buildStatistic(),
            SizedBox(height: 26.v),
            _buildTransactions(),
            SizedBox(height: 24.v),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildStatistic() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.h),
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 18.v),
      decoration: AppDecoration.fillIndigo
          .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("lbl_spending".tr,
                  style: CustomTextStyles.titleLargeBlack900),
              Padding(
                padding: EdgeInsets.only(top: 2.v, bottom: 3.v),
                child: Text(
                  "lbl_10_0002".tr,
                  style: CustomTextStyles.bodyLargeBlack900,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.v),
          Container(
            height: 175.v,
            width: double.infinity,
            child: SfCartesianChart(
              plotAreaBorderWidth: 0,
              primaryXAxis: CategoryAxis(
                majorGridLines: MajorGridLines(width: 0),
                axisLine: AxisLine(width: 0),
                labelStyle: TextStyle(
                  color: appTheme.black900,
                  fontSize: 13.fSize,
                  fontWeight: FontWeight.w400,
                ),
              ),
              margin: EdgeInsets.zero,
              primaryYAxis: NumericAxis(
                isVisible: true,
                majorGridLines: MajorGridLines(width: 0),
                axisLine: AxisLine(width: 0),
                labelStyle: TextStyle(
                  color: appTheme.black900,
                  fontSize: 13.fSize,
                  fontWeight: FontWeight.w400,
                ),
              ),
              palette: <Color>[
                theme.colorScheme.primary,
                // appTheme.whiteA700,
              ],
              series: <CartesianSeries>[
                ColumnSeries<ChartSampleData, String>(
                  isTrackVisible: true,
                  trackColor: theme.colorScheme.primary.withOpacity(0.15),
                  borderRadius: BorderRadius.all(Radius.circular(20.v)),
                  dataSource: data,
                  xValueMapper: (ChartSampleData data, _) => data.country,
                  yValueMapper: (ChartSampleData data, _) => data.one,
                  width: 0.25,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildTransactions() {
    return SizedBox(
      width: double.maxFinite,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              Text("lbl_this_week".tr,
                  style: CustomTextStyles.titleLargeBlack900_1),
              GestureDetector(
                  onTap: () {
                    onTapTxtViewAll();
                  },
                  child:
                      Text("lbl_view_all".tr, style: theme.textTheme.bodyLarge))
            ]),
            SizedBox(height: 19.v),
            Obx(
              () => ListView.separated(
                physics: NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                separatorBuilder: (context, index) {
                  return SizedBox(height: 16.v);
                },
                itemCount: controller.statisticExpensesModelObj.value
                    .statisticexpensesItemList.value.length,
                itemBuilder: (context, index) {
                  StatisticexpensesItemModel model = controller
                      .statisticExpensesModelObj
                      .value
                      .statisticexpensesItemList
                      .value[index];
                  return StatisticexpensesItemWidget(model);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Navigates to the thisWeekScreen when the action is triggered.
  onTapTxtViewAll() {
    Get.toNamed(
      AppRoutes.thisWeekScreen,
    );
  }

  /// Navigates to the homeScreen when the action is triggered.
  onTapIcon() {
    Get.toNamed(
      AppRoutes.homeScreen,
    );
  }

  /// Navigates to the depositsCurrentDepositeTabContainerScreen when the action is triggered.
  onTapIcon1() {
    Get.toNamed(
      AppRoutes.depositsCurrentDepositeTabContainerScreen,
    );
  }

  /// Navigates to the loansContainerScreen when the action is triggered.
  onTapIcon2() {
    Get.toNamed(
      AppRoutes.loansContainerScreen,
    );
  }
}

class ChartSampleData {
  ChartSampleData(
    this.country,
    this.one,
  );

  final String country;
  final num one;
}
