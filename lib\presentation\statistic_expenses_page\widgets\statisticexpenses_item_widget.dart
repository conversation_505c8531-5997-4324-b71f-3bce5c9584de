import '../models/statisticexpenses_item_model.dart';
import '../controller/statistic_expenses_controller.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class StatisticexpensesItemWidget extends StatelessWidget {
  StatisticexpensesItemWidget(
    this.statisticexpensesItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  StatisticexpensesItemModel statisticexpensesItemModelObj;

  var controller = Get.find<StatisticExpensesController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 16.h,
        vertical: 15.v,
      ),
      decoration: AppDecoration.outlineBlack.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder12,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomIconButton(
                height: 48.adaptSize,
                width: 48.adaptSize,
                padding: EdgeInsets.all(12.h),
                child: CustomImageView(
                  imagePath: statisticexpensesItemModelObj.image!.value,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 12.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(
                      () => Text(
                        statisticexpensesItemModelObj
                            .financialtransaction!.value,
                        style: theme.textTheme.titleMedium,
                      ),
                    ),
                    SizedBox(height: 6.v),
                    Obx(
                      () => Text(
                        statisticexpensesItemModelObj.duration!.value,
                        style: theme.textTheme.bodyLarge,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 12.v),
            child: Obx(
              () => Text(
                statisticexpensesItemModelObj.price!.value,
                style: theme.textTheme.titleLarge,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
