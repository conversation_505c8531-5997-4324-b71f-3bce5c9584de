// ignore_for_file: deprecated_member_use

import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'models/contryofresidence_item_model.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/contry_of_residence_controller.dart';

class ContryOfResidenceScreen extends GetWidget<ContryOfResidenceController> {
  const ContryOfResidenceScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: Scaffold(
          appBar: _buildAppBar(),
          body: Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
              child: Column(children: [
                SizedBox(
                    width: 388.h,
                    child: Text("msg_the_terms_and_services".tr,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style:
                            theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
                SizedBox(height: 37.v),
                _buildContryOfResidence(),
              ])),
          bottomNavigationBar: _buildButtons()),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        title: AppbarSubtitle(
            text: "msg_contry_of_residence".tr,
            margin: EdgeInsets.only(left: 20.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildContryOfResidence() {
    return GetBuilder<ContryOfResidenceController>(
        init: ContryOfResidenceController(),
        builder: (controller) {
          return ListView.separated(
              physics: AlwaysScrollableScrollPhysics(),
              shrinkWrap: true,
              separatorBuilder: (context, index) {
                return SizedBox(height: 16.v);
              },
              itemCount: controller.countryOfResidenceList.length,
              itemBuilder: (context, index) {
                ContryofresidenceItemModel model =
                    controller.countryOfResidenceList[index];
                return GestureDetector(
                  onTap: () {
                    controller.selected.value = index;
                    controller.update();
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.h,
                      vertical: 17.v,
                    ),
                    decoration: AppDecoration.outlineBlack.copyWith(
                      borderRadius: BorderRadiusStyle.roundedBorder12,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CustomImageView(
                          imagePath: controller.selected.value == index
                              ? ImageConstant.selectedRadio
                              : ImageConstant.unSelectedRadio,
                          height: 24.v,
                          width: 25.h,
                        ),
                        SizedBox(
                          width: 12.v,
                        ),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(top: 3.v),
                            child: Text(
                              model.title!,
                              style: CustomTextStyles.bodyLargeBlack900,
                            ),
                          ),
                        ),
                        CustomImageView(
                          imagePath: model.image,
                          height: 30.v,
                          width: 41.h,
                        ),
                      ],
                    ),
                  ),
                );
              });
        });
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
        margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
        decoration: AppDecoration.fillWhiteA,
        child: CustomElevatedButton(
            text: "lbl_continue".tr,
            onPressed: () {
              onTapContinue();
            }));
  }

  /// Navigates to the createPinScreen when the action is triggered.
  onTapContinue() {
    Get.toNamed(
      AppRoutes.createPinScreen,
    );
  }
}
