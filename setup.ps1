# KojaPay Flutter App Setup Script
Write-Host "========================================" -ForegroundColor Green
Write-Host "KojaPay Flutter App Setup" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check Flutter installation
Write-Host "Checking Flutter installation..." -ForegroundColor Yellow
try {
    $flutterVersion = flutter --version
    Write-Host "Flutter is installed:" -ForegroundColor Green
    Write-Host $flutterVersion -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: Flutter is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Flutter from https://flutter.dev/docs/get-started/install" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Installing dependencies..." -ForegroundColor Yellow
try {
    flutter pub get
    Write-Host "Dependencies installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Checking Android setup..." -ForegroundColor Yellow
try {
    flutter doctor
} catch {
    Write-Host "WARNING: Some Flutter doctor checks failed" -ForegroundColor Yellow
    Write-Host "Please review the output above and fix any issues" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Setting up Android project..." -ForegroundColor Yellow
try {
    Set-Location android
    ./gradlew clean
    Set-Location ..
    Write-Host "Android project setup complete!" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Android project setup had issues" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "To run the app:" -ForegroundColor Cyan
Write-Host "1. Connect your device or start an emulator" -ForegroundColor White
Write-Host "2. Run: flutter run" -ForegroundColor White
Write-Host ""
Write-Host "To open in Android Studio:" -ForegroundColor Cyan
Write-Host "1. Open Android Studio" -ForegroundColor White
Write-Host "2. File -> Open" -ForegroundColor White
Write-Host "3. Select this project folder" -ForegroundColor White
Write-Host ""
Write-Host "To customize themes:" -ForegroundColor Cyan
Write-Host "1. Run the app" -ForegroundColor White
Write-Host "2. Go to Settings -> Theme Settings" -ForegroundColor White
Write-Host "3. Choose from 8 beautiful color schemes" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to continue" 