import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_drop_down.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:payway/widgets/custom_switch.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/open_deposits_controller.dart';
import 'package:payway/presentation/deposit_created_popup_dialog/deposit_created_popup_dialog.dart';
import 'package:payway/presentation/deposit_created_popup_dialog/controller/deposit_created_popup_controller.dart';

// ignore_for_file: must_be_immutable
class OpenDepositsScreen extends GetWidget<OpenDepositsController> {
  OpenDepositsScreen({Key? key}) : super(key: key);

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: SizedBox(
        width: SizeUtils.width,
        child: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Form(
            key: _formKey,
            child: Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(horizontal: 10.h, vertical: 16.v),
              child: Column(
                children: [
                  CustomDropDown(
                      icon: Container(
                          margin: EdgeInsets.fromLTRB(0.h, 0.v, 16.h, 0.v),
                          child: CustomImageView(
                              imagePath: ImageConstant.imgArrowdown,
                              height: 20.adaptSize,
                              width: 20.adaptSize)),
                      hintText: "lbl_select_currency".tr,
                      items: controller
                          .openDepositsModelObj.value.dropdownItemList.value),
                  SizedBox(height: 24.v),
                  CustomDropDown(
                      icon: Container(
                          margin: EdgeInsets.fromLTRB(0.h, 0.v, 16.h, 0.v),
                          child: CustomImageView(
                              imagePath: ImageConstant.imgArrowdown,
                              height: 20.adaptSize,
                              width: 20.adaptSize)),
                      hintText: "msg_select_deposite".tr,
                      items: controller
                          .openDepositsModelObj.value.dropdownItemList1.value),
                  SizedBox(height: 24.v),
                  // CustomDropDown(
                  //     icon: Container(
                  //         margin: EdgeInsets.fromLTRB(0.h, 0.v, 16.h, 0.v),
                  //         child: CustomImageView(
                  //             imagePath: ImageConstant.imgDbnhguieofyuike,
                  //             height: 20.adaptSize,
                  //             width: 20.adaptSize)),
                  //     hintText: "lbl_add_card".tr,
                  //     items: controller
                  //         .openDepositsModelObj.value.dropdownItemList2.value),
                  GestureDetector(
                    onTap: () {
                      Get.toNamed(
                        AppRoutes.addCardScreen,
                      );
                    },
                    child: Container(
                      width: double.infinity,
                      // height: 56.v,
                      decoration: AppDecoration.fillGray.copyWith(
                        borderRadius: BorderRadiusStyle.roundedBorder12,
                      ),
                      margin: EdgeInsets.symmetric(horizontal: 20.h),
                      padding: EdgeInsets.only(
                          left: 16.h, top: 20.v, bottom: 20.v, right: 16.v),
                      child: Row(
                        children: [
                          Expanded(
                              child: Text("Add card".tr,
                                  style: theme.textTheme.bodyLarge)),
                          CustomImageView(
                            imagePath: ImageConstant.imgDbnhguieofyuike,
                            height: 20.adaptSize,
                            width: 20.adaptSize,
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 24.v),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.h),
                    child: CustomTextFormField(
                        controller: controller.defaultController,
                        hintText: "lbl_enter_amout".tr,
                        hintStyle: theme.textTheme.bodyLarge!,
                        textInputAction: TextInputAction.done,
                        textInputType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return "Please enter amount";
                          }
                          return null;
                        },
                        obscureText: false),
                  ),
                  SizedBox(height: 16.v),
                  _buildFrame(),
                  SizedBox(height: 48.v),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.h),
                    child: CustomElevatedButton(
                        text: "lbl_open_deposit".tr,
                        onPressed: () {
                          onTapOpenDeposit();
                        }),
                  ),
                  SizedBox(height: 5.v),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_open_deposits".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildFrame() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
              padding: EdgeInsets.only(top: 5.v, bottom: 3.v),
              child: Text("msg_early_deposit_withdrawal".tr,
                  style: theme.textTheme.titleMedium)),
          Obx(
            () => CustomSwitch(
              value: controller.isSelectedSwitch.value,
              onChange: (value) {
                controller.isSelectedSwitch.value = value;
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Navigates to the depositsCurrentDepositeTabContainerScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Displays a dialog with the [DepositCreatedPopupDialog] content.
  onTapOpenDeposit() {
    controller.clearText();
    Get.dialog(AlertDialog(
      backgroundColor: Colors.transparent,
      contentPadding: EdgeInsets.zero,
      insetPadding: const EdgeInsets.only(left: 0),
      content: DepositCreatedPopupDialog(
        Get.put(
          DepositCreatedPopupController(),
        ),
      ),
    ));
  }
}
