import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_outlined_button.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/selfie_with_id_card_controller.dart';

class SelfieWithIdCardScreen extends GetWidget<SelfieWithIdCardController> {
  const SelfieWithIdCardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _buildAppBar(),
        body: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text("msg_please_look_at_the".tr,
                  style: theme.textTheme.bodyLarge),
              SizedBox(height: 24.v),
              CustomImageView(
                  imagePath: ImageConstant.imgImage,
                  height: 530.v,
                  width: double.infinity,
                  fit: BoxFit.fill,
                  radius: BorderRadius.circular(12.h)),
              SizedBox(height: 5.v)
            ])),
        bottomNavigationBar: _buildButtons());
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 18.v, bottom: 16.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "msg_selfie_with_id_card".tr,
            margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
        margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
        decoration: AppDecoration.fillWhiteA,
        child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomOutlinedButton(
                  width: 186.h,
                  text: "lbl_retake".tr,
                  buttonStyle: CustomButtonStyles.outlinePrimary,
                  onPressed: () {
                    onTapRetake();
                  }),
              CustomElevatedButton(
                  width: 186.h,
                  text: "lbl_submit".tr,
                  margin: EdgeInsets.only(left: 16.h),
                  onPressed: () {
                    onTapSubmit();
                  })
            ]));
  }

  /// Navigates to the photoIdCardScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the photoIdCardScreen when the action is triggered.
  onTapRetake() {
    Get.back();
  }

  /// Navigates to the homeScreen when the action is triggered.
  onTapSubmit() {
    PrefUtils.setLogin(false);
    Get.toNamed(
      AppRoutes.homeScreen,
    );
  }
}
