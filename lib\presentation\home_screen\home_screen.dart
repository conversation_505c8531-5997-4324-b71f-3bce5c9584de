// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/Constant/constants.dart';
import '../../core/utils/size_utils.dart';
import '../../routes/app_routes.dart';
import '../../theme/app_decoration.dart';
import '../../theme/custom_button_style.dart';
import '../../theme/custom_text_style.dart';
import '../../theme/theme_helper.dart';
import '../../widgets/custom_bottom_bar.dart';
import '../../widgets/custom_elevated_button.dart';
import '../../widgets/custom_outlined_button.dart';
import '../deposits_current_deposite_tab_container_screen/deposits_current_deposite_tab_container_screen.dart';
import '../home_card_slider_screen/home_card_slider_screen.dart';
import '../loans_page/loans_page.dart';
import '../profile_screen/profile_screen.dart';
import '../statistic_income_tab_container_screen/statistic_income_tab_container_screen.dart';
import 'controller/home_controller.dart';

//ignore: must_be_immutable
class HomeContainerScreen extends GetWidget<HomeController> {
  HomeContainerScreen({Key? key}) : super(key: key);

  List<Widget> screen = [
    HomeCardSliderScreen(),
    DepositsCurrentDepositeTabContainerScreen(),
    StatisticIncomeTabContainerScreen(),
    LoansPage(),
    ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    mediaQueryData = MediaQuery.of(context);
    return GetBuilder<CustomBottomBarController>(
      init: CustomBottomBarController(),
      builder: (controller) => WillPopScope(
        onWillPop: () async {
          if (controller.selectedIndex == 0) {
            showDialog(
              barrierDismissible: false,
              context: context,
              builder: (context) {
                return AlertDialog(
                  insetPadding: EdgeInsets.all(16.h),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.h)),
                  contentPadding: EdgeInsets.zero,
                  content: Container(
                    width: 374.h,
                    padding: EdgeInsets.all(30.h),
                    decoration: BoxDecoration(
                      color: appTheme.whiteA700,
                      borderRadius: BorderRadiusStyle.roundedBorder16,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text("Are you sure want to exit?".tr,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                            style: theme.textTheme.titleMedium),
                        Padding(
                          padding: EdgeInsets.only(top: 32),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                  child: CustomOutlinedButton(
                                      height: 56.v,
                                      text: "No".tr,
                                      margin: EdgeInsets.only(right: 10),
                                      buttonStyle: CustomButtonStyles
                                          .fillOnPrimaryContainer
                                          .copyWith(
                                              backgroundColor:
                                                  MaterialStatePropertyAll(
                                        Colors.transparent,
                                      )),
                                      onPressed: () {
                                        Get.back();
                                      })),
                              Expanded(
                                child: CustomElevatedButton(
                                  text: "Yes".tr,
                                  margin: EdgeInsets.only(left: 10),
                                  buttonTextStyle: CustomTextStyles
                                      .titleMediumOnPrimaryContainerMedium,
                                  onPressed: () {
                                    if (controller.selectedIndex == 0) {
                                      Constant.closeApp();
                                    } else {
                                      controller.getIndex(0);
                                      Get.back();
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          } else {
            controller.getIndex(0);
          }
          return false;
        },
        child: Scaffold(
          backgroundColor: appTheme.whiteA700,
          resizeToAvoidBottomInset: false,
          extendBodyBehindAppBar: true,
          extendBody: true,
          appBar: getInVisibleAppBar(
            statusBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.dark,
          ),
          body: screen[controller.selectedIndex],
          bottomNavigationBar: CustomBottomBar(onChanged: (BottomBarEnum type) {
            Get.toNamed(getCurrentRoute(type), id: 1);
          }),
        ),
      ),
    );
  }

  ///Handling route based on bottom click actions
  String getCurrentRoute(BottomBarEnum type) {
    switch (type) {
      case BottomBarEnum.Home:
        return AppRoutes.homeCardSliderScreen;
      default:
        return "/";
    }
  }
}
