import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/app_navigation_controller.dart';

// ignore_for_file: must_be_immutable
class AppNavigationScreen extends GetWidget<AppNavigationController> {
  const AppNavigationScreen({Key? key})
      : super(
          key: key,
        );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Color(0XFFFFFFFF),
        body: SizedBox(
          width: 375.h,
          child: Column(
            children: [
              _buildAppNavigation(),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(0XFFFFFFFF),
                    ),
                    child: Column(
                      children: [
                        _buildScreenTitle(
                          screenTitle: "Splace screen".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.splaceScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Onboarding One".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.onboardingOneScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Onboarding Two".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.onboardingTwoScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Onboarding Three".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.onboardingThreeScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Login screen".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.loginScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Login screen error - Tab Container".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.loginScreenErrorTabContainerScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Login screen filled".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.loginScreenFilledScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Sign up".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.signUpScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Forgot password?".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.forgotPasswordScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Verify code".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.verifyCodeScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Reaset password One".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.reasetPasswordOneScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Contry of residence".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.contryOfResidenceScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Create pin".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.createPinScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: " Identity verify popup".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.identityVerifyPopupScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Proof of Residency".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.proofOfResidencyScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Photo ID Card".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.photoIdCardScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Selfie with ID Card".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.selfieWithIdCardScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Home".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.homeScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Home card slider".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.homeCardSliderScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Add card".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.addCardScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm payment Two".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.confirmPaymentTwoScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Notification empty".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.notificationEmptyScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Notification ".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.notificationScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Scan QR to ride".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.scanQrToRideScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Transfer details Two".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.transferDetailsTwoScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Select bank popup Three".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.selectBankPopupThreeScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Transfer details fill".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.transferDetailsFillScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm Two".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.confirmTwoScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm payment One".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.confirmPaymentOneScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Details One".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.detailsOneScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Share popup".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.sharePopupScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Top up".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.topUpScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Select bank popup Two".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.selectBankPopupTwoScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm One".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.confirmOneScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm payment".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.confirmPaymentScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Withdraw".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.withdrawScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Select bank popup One".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.selectBankPopupOneScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Transfer details One".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.transferDetailsOneScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.confirmScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Enter your pin".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.enterYourPinScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Details".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.detailsScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Transfer".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.transferScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Transfer details Three".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.transferDetailsThreeScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Select bank popup".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.selectBankPopupScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Transfer details".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.transferDetailsScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm Three".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.confirmThreeScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Enter your pin One".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.enterYourPinOneScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Details Two".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.detailsTwoScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Transactions".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.transactionsScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Transactions details".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.transactionsDetailsScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm payment Three".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.confirmPaymentThreeScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle:
                              "Deposits current deposite - Tab Container".tr,
                          onTapScreenTitle: () => onTapScreenTitle(AppRoutes
                              .depositsCurrentDepositeTabContainerScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Open deposits".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.openDepositsScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Open money bank".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.openMoneyBankScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Add card One".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.addCardOneScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm payment Six".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.confirmPaymentSixScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Statistic / Income - Tab Container".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.statisticIncomeTabContainerScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Time popup".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.timePopupScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "This week".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.thisWeekScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Details Three".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.detailsThreeScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Loans - Container".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.loansContainerScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "New loan".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.newLoanScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Repay".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.repayScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Select bank popup Four".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.selectBankPopupFourScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm Four".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.confirmFourScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Confirm payment Four".tr,
                          onTapScreenTitle: () => onTapScreenTitle(
                              AppRoutes.confirmPaymentFourScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Profile".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.profileScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "My profile".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.myProfileScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Edit profile".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.editProfileScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Security".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.securityScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Change password".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.changePasswordScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Settings".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.settingsScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "About us".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.aboutUsScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Help".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.helpScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Feedback".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.feedbackScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Privacy policy".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.privacyPolicyScreen),
                        ),
                        _buildScreenTitle(
                          screenTitle: "Terms & conditions".tr,
                          onTapScreenTitle: () =>
                              onTapScreenTitle(AppRoutes.termsConditionsScreen),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildAppNavigation() {
    return Container(
      decoration: BoxDecoration(
        color: Color(0XFFFFFFFF),
      ),
      child: Column(
        children: [
          SizedBox(height: 10.v),
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.h),
              child: Text(
                "App Navigation".tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0XFF000000),
                  fontSize: 20.fSize,
                  fontFamily: 'Roboto',
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
          SizedBox(height: 10.v),
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: EdgeInsets.only(left: 20.h),
              child: Text(
                "Check your app's UI from the below demo screens of your app."
                    .tr,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0XFF888888),
                  fontSize: 16.fSize,
                  fontFamily: 'Roboto',
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
          SizedBox(height: 5.v),
          Divider(
            height: 1.v,
            thickness: 1.v,
            color: Color(0XFF000000),
          ),
        ],
      ),
    );
  }

  /// Common widget
  Widget _buildScreenTitle({
    required String screenTitle,
    Function? onTapScreenTitle,
  }) {
    return GestureDetector(
      onTap: () {
        onTapScreenTitle!.call();
      },
      child: Container(
        decoration: BoxDecoration(
          color: Color(0XFFFFFFFF),
        ),
        child: Column(
          children: [
            SizedBox(height: 10.v),
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.h),
                child: Text(
                  screenTitle,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Color(0XFF000000),
                    fontSize: 20.fSize,
                    fontFamily: 'Roboto',
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
            SizedBox(height: 10.v),
            SizedBox(height: 5.v),
            Divider(
              height: 1.v,
              thickness: 1.v,
              color: Color(0XFF888888),
            ),
          ],
        ),
      ),
    );
  }

  /// Common click event
  void onTapScreenTitle(String routeName) {
    Get.toNamed(routeName);
  }
}
