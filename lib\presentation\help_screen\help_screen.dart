import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/help_controller.dart';
import 'models/help_model.dart';

class HelpScreen extends GetWidget<HelpController> {
  const HelpScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 28.v),
        child: Expanded(
          child: ListView.separated(
            physics: const AlwaysScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return Theme(
                data: Theme.of(context).copyWith(
                    dividerColor: Colors.transparent,
                    expansionTileTheme: ExpansionTileThemeData(
                      iconColor: appTheme.black900,
                      collapsedIconColor: appTheme.black900,
                    )),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.h),
                    color: appTheme.textfeild,
                  ),
                  child: ExpansionTile(
                    backgroundColor: appTheme.textfeild,
                    title: Text(helpDataList[index].que!,
                        style: CustomTextStyles.titleMedium18),
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16.0),
                        child: Text(
                            "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
                                .tr,
                            maxLines: 3,
                            textAlign: TextAlign.start,
                            overflow: TextOverflow.ellipsis,
                            style: theme.textTheme.bodyLarge!
                                .copyWith(height: 1.50)),
                      ),
                      SizedBox(height: 16.h),
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) => SizedBox(height: 16.h),
            itemCount: helpDataList.length,
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_help".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Navigates to the settingsScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }
}
