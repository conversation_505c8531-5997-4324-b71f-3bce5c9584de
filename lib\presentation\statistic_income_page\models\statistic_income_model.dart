import 'package:payway/core/app_export.dart';
import 'statisticincome_item_model.dart';

/// This class defines the variables used in the [statistic_income_page],
/// and is typically used to hold data that is passed between different parts of the application.
class StatisticIncomeModel {
  Rx<List<SelectionPopupModel>> dropdownItemList = Rx([
    SelectionPopupModel(
      id: 1,
      title: "Item One",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "Item Two",
    ),
    SelectionPopupModel(
      id: 3,
      title: "Item Three",
    )
  ]);

  Rx<List<StatisticincomeItemModel>> statisticincomeItemList = Rx([
    StatisticincomeItemModel(
      restlessness: "Restlessness".obs,
      time: "4 January, 4:00 Am".obs,
      price: "\$200.00".obs,
      image: ImageConstant.imgCallReceivedF.obs,
    ),
    StatisticincomeItemModel(
      restlessness: "Trouble".obs,
      time: "4 January, 4:00 Am".obs,
      price: "\$123.00".obs,
      image: ImageConstant.imgCallReceivedF.obs,
    ),
    StatisticincomeItemModel(
      restlessness: "Sleeping".obs,
      time: "4 January, 4:00 Am".obs,
      price: "\$450.00".obs,
      image: ImageConstant.imgCallReceivedF.obs,
    ),
    StatisticincomeItemModel(
      restlessness: "Mood swings".obs,
      time: "4 January, 4:00 Am".obs,
      price: "\$213.00".obs,
      image: ImageConstant.imgCallReceivedF.obs,
    ),
  ]);
}
