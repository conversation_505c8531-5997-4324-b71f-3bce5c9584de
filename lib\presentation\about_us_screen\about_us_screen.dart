import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/about_us_controller.dart';

class AboutUsScreen extends GetWidget<AboutUsController> {
  const AboutUsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _buildAppBar(),
        body: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              CustomImageView(
                  imagePath: ImageConstant.imgRectangle712,
                  height: 180.v,
                  width: 388.h,
                  radius: BorderRadius.circular(12.h)),
              <PERSON><PERSON><PERSON><PERSON>(height: 17.v),
              Text("msg_how_do_i_add_money".tr,
                  style: theme.textTheme.titleMedium),
              <PERSON><PERSON><PERSON><PERSON>(height: 15.v),
              <PERSON><PERSON><PERSON><PERSON>(
                  width: 388.h,
                  child: Text("msg_lorem_ipsum_dolor".tr,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style:
                          theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
              SizedBox(height: 7.v),
              SizedBox(
                  width: 386.h,
                  child: Text("msg_amet_minim_mollit".tr,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style:
                          theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
              SizedBox(height: 8.v),
              Container(
                  width: 366.h,
                  margin: EdgeInsets.only(right: 21.h),
                  child: Text("msg_in_a_laoreet_purus2".tr,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      style:
                          theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
              SizedBox(height: 6.v),
              SizedBox(
                  width: 386.h,
                  child: Text("msg_ivorem_ipsum_dolor".tr,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style:
                          theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
              SizedBox(height: 7.v),
              Container(
                  width: 374.h,
                  margin: EdgeInsets.only(right: 13.h),
                  child: Text("msg_amet_minim_mollit2".tr,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style:
                          theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
              SizedBox(height: 8.v),
              SizedBox(
                  width: 386.h,
                  child: Text("msg_ivorem_ipsum_dolor".tr,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style:
                          theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
              SizedBox(height: 5.v)
            ])));
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_about_us".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Navigates to the settingsScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }
}
