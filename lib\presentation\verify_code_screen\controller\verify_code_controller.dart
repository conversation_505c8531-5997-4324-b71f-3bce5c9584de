import '../../../core/app_export.dart';
import '../models/verify_code_model.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter/material.dart';

/// A controller class for the VerifyCodeScreen.
///
/// This class manages the state of the VerifyCodeScreen, including the
/// current verifyCodeModelObj
class VerifyCodeController extends GetxController with CodeAutoFill {
  TextEditingController otpController = TextEditingController();

  Rx<VerifyCodeModel> verifyCodeModelObj = VerifyCodeModel().obs;


  @override
  void codeUpdated() {
    otpController.text = code ?? '';
  }

  void clearText() {
    otpController = TextEditingController(text: "");
  }

  @override
  void onInit() {
    super.onInit();
    listenForCode();
  }
}
