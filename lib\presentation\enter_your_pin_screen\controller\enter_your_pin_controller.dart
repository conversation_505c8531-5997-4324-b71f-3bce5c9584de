import '../../../core/app_export.dart';
import '../models/enter_your_pin_model.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter/material.dart';

/// A controller class for the EnterYourPinScreen.
///
/// This class manages the state of the EnterYourPinScreen, including the
/// current enterYourPinModelObj
class EnterYourPinController extends GetxController with CodeAutoFill {
 Rx<TextEditingController> otpController = TextEditingController().obs;

 Rx<EnterYourPinModel> enterYourPinModelObj = EnterYourPinModel().obs;

 @override
 void codeUpdated() {
  otpController.value.text = code ?? '';
 }


 void clearText() {
  otpController = TextEditingController(text: "").obs;
 }

 @override
 void onInit() {
  super.onInit();
  listenForCode();
 }
}
