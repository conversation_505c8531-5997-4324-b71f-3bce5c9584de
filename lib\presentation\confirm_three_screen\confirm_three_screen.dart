import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/confirm_three_controller.dart';

class ConfirmThreeScreen extends GetWidget<ConfirmThreeController> {
  const ConfirmThreeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.all(20.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("lbl_recipient".tr,
                style: CustomTextStyles.titleLargeBlack900_1),
            SizedBox(height: 15.v),
            _buildWajihTaysirHandal(),
            SizedBox(height: 27.v),
            Text("lbl_transfer_to".tr,
                style: CustomTextStyles.titleLargeBlack900_1),
            SizedBox(height: 19.v),
            _buildMasterCard(),
            SizedBox(height: 24.v),
            _buildTransferAmout(),
            SizedBox(height: 5.v),
          ],
        ),
      ),
      bottomNavigationBar: _buildButtons(),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_confirm".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildWajihTaysirHandal() {
    return Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(children: [
          CustomImageView(
              imagePath: ImageConstant.imgEllipse135556x56,
              height: 56.adaptSize,
              width: 56.adaptSize,
              radius: BorderRadius.circular(28.h)),
          Padding(
              padding: EdgeInsets.only(left: 12.h, top: 3.v),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("msg_wajih_taysir_handal".tr,
                        style: theme.textTheme.titleMedium),
                    SizedBox(height: 8.v),
                    Text("lbl_02_9378_5922".tr,
                        style: theme.textTheme.bodyLarge)
                  ]))
        ]));
  }

  /// Section Widget
  Widget _buildMasterCard() {
    return Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(children: [
          CustomIconButton(
              height: 55.adaptSize,
              width: 55.adaptSize,
              padding: EdgeInsets.all(7.h),
              decoration: IconButtonStyleHelper.fillGrayTL12,
              child: CustomImageView(imagePath: ImageConstant.imgMasterCard)),
          Padding(
              padding: EdgeInsets.only(left: 12.h, top: 16.v, bottom: 17.v),
              child: Text("msg_2541".tr, style: theme.textTheme.bodyLarge))
        ]));
  }

  /// Section Widget
  Widget _buildTransferAmout() {
    return Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Padding(
              padding: EdgeInsets.only(top: 2.v, bottom: 1.v),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("lbl_transfer_amout".tr,
                        style: CustomTextStyles.titleSmallBlack900),
                    SizedBox(height: 21.v),
                    Text("lbl_total".tr,
                        style: CustomTextStyles.titleSmallBlack900)
                  ])),
          Column(children: [
            Text("lbl_100_00".tr, style: CustomTextStyles.bodyLargeBlack900),
            SizedBox(height: 18.v),
            Text("lbl_100_00".tr, style: CustomTextStyles.bodyLargeBlack900)
          ])
        ]));
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
        margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
        decoration: AppDecoration.fillWhiteA,
        child: CustomElevatedButton(
            text: "lbl_continue".tr,
            onPressed: () {
              onTapContinue();
            }));
  }

  /// Navigates to the transferDetailsScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the enterYourPinOneScreen when the action is triggered.
  onTapContinue() {
    Get.toNamed(
      AppRoutes.enterYourPinOneScreen,
    );
  }
}
