import 'package:payway/widgets/app_bar/custom_app_bar.dart';import 'package:payway/widgets/app_bar/appbar_leading_image.dart';import 'package:payway/widgets/app_bar/appbar_subtitle.dart';import 'package:payway/widgets/custom_drop_down.dart';import 'package:payway/widgets/custom_elevated_button.dart';import 'package:flutter/material.dart';import 'package:payway/core/app_export.dart';import 'controller/transfer_details_one_controller.dart';class TransferDetailsOneScreen extends GetWidget<TransferDetailsOneController> {const TransferDetailsOneScreen({Key? key}) : super(key: key);

@override Widget build(BuildContext context) { return SafeArea(child: Scaffold(appBar: _buildAppBar(), body: Container(width: double.maxFinite, padding: EdgeInsets.symmetric(vertical: 16.v), child: Column(children: [Padding(padding: EdgeInsets.symmetric(horizontal: 20.h), child: CustomDropDown(icon: Container(margin: EdgeInsets.fromLTRB(30.h, 18.v, 16.h, 18.v), child: CustomImageView(imagePath: ImageConstant.imgArrowdown, height: 20.adaptSize, width: 20.adaptSize)), hintText: "msg_2541".tr, items: controller.transferDetailsOneModelObj.value.dropdownItemList.value, prefix: Container(margin: EdgeInsets.fromLTRB(8.h, 8.v, 16.h, 8.v), child: CustomImageView(imagePath: ImageConstant.imgMasterCard, height: 40.adaptSize, width: 40.adaptSize)), prefixConstraints: BoxConstraints(maxHeight: 56.v), contentPadding: EdgeInsets.symmetric(vertical: 17.v))), SizedBox(height: 57.v), RichText(text: TextSpan(children: [TextSpan(text: "lbl_10".tr, style: CustomTextStyles.headlineMediumff000000), TextSpan(text: "lbl_00".tr, style: CustomTextStyles.headlineMediumff000000Regular)]), textAlign: TextAlign.left), SizedBox(height: 22.v), CustomElevatedButton(height: 48.v, width: 183.h, text: "lbl_add_note".tr, buttonStyle: CustomButtonStyles.fillGray, buttonTextStyle: theme.textTheme.bodyLarge!), Spacer(flex: 32), _buildButtons(), Spacer(flex: 67)])))); }
/// Section Widget
PreferredSizeWidget _buildAppBar() { return CustomAppBar(leadingWidth: 52.h, leading: AppbarLeadingImage(imagePath: ImageConstant.imgExpandMoreFil, margin: EdgeInsets.only(left: 20.h, top: 17.v, bottom: 25.v), onTap: () {onTapExpandMoreFIL();}), title: AppbarSubtitle(text: "msg_transfer_details".tr, margin: EdgeInsets.only(left: 16.h)), styleType: Style.bgFill); } 
/// Section Widget
Widget _buildButtons() { return Container(padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v), decoration: AppDecoration.fillWhiteA, child: Column(children: [SizedBox(height: 16.v), CustomElevatedButton(text: "lbl_continue".tr, onPressed: () {onTapContinue();})])); } 
/// Navigates to the withdrawScreen when the action is triggered.
onTapExpandMoreFIL() { Get.toNamed(AppRoutes.withdrawScreen, ); } 
/// Navigates to the confirmScreen when the action is triggered.
onTapContinue() { Get.toNamed(AppRoutes.confirmScreen, ); } 
 }
