import '../../../core/app_export.dart';
import '../models/select_bank_popup_three_model.dart';

/// A controller class for the SelectBankPopupThreeScreen.
///
/// This class manages the state of the SelectBankPopupThreeScreen, including the
/// current selectBankPopupThreeModelObj
class SelectBankPopupThreeController extends GetxController {
  Rx<SelectBankPopupThreeModel> selectBankPopupThreeModelObj =
      SelectBankPopupThreeModel().obs;

  RxInt isSelected = 0.obs;
}
