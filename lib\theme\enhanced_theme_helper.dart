import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:payway/core/utils/pref_utils.dart';
import 'package:payway/core/utils/size_utils.dart';

/// Enhanced Theme Helper with multiple color schemes and advanced features
class EnhancedThemeHelper {
  static const String _themeKey = 'selected_theme';
  static const String _colorSchemeKey = 'selected_color_scheme';
  
  // Available themes
  static const List<String> availableThemes = [
    'light',
    'dark',
    'auto'
  ];
  
  // Available color schemes
  static const List<String> availableColorSchemes = [
    'kojapay_blue',
    'primary',
    'ocean',
    'sunset',
    'forest',
    'purple',
    'golden',
    'coral',
    'emerald'
  ];

  // Get current theme
  String get currentTheme => PrefUtils().getThemeData();
  
  // Get current color scheme
  String get currentColorScheme => PrefUtils().getString(_colorSchemeKey) ?? 'kojapay_blue';

  /// Change app theme
  void changeTheme(String newTheme) {
    if (availableThemes.contains(newTheme)) {
      PrefUtils().setThemeData(newTheme);
      Get.forceAppUpdate();
    }
  }

  /// Change color scheme
  void changeColorScheme(String newColorScheme) {
    if (availableColorSchemes.contains(newColorScheme)) {
      PrefUtils().setString(_colorSchemeKey, newColorScheme);
      Get.forceAppUpdate();
    }
  }

  /// Get theme data based on current settings
  ThemeData getThemeData() {
    final theme = currentTheme;
    final colorScheme = currentColorScheme;
    
    if (theme == 'dark') {
      return _getDarkTheme(colorScheme);
    } else {
      return _getLightTheme(colorScheme);
    }
  }

  /// Get light theme
  ThemeData _getLightTheme(String colorScheme) {
    final colors = _getColorScheme(colorScheme);
    
    return ThemeData(
      brightness: Brightness.light,
      visualDensity: VisualDensity.standard,
      colorScheme: colors,
      textTheme: _getTextTheme(colors, false),
      scaffoldBackgroundColor: colors.surface,
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colors.primary,
          foregroundColor: colors.onPrimary,
          elevation: 2,
          shadowColor: colors.primary.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.h),
          ),
          visualDensity: const VisualDensity(
            vertical: -4,
            horizontal: -4,
          ),
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: colors.primary,
          side: BorderSide(
            color: colors.primary,
            width: 1.5.h,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.h),
          ),
          visualDensity: const VisualDensity(
            vertical: -4,
            horizontal: -4,
          ),
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colors.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.h),
          borderSide: BorderSide(color: colors.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.h),
          borderSide: BorderSide(color: colors.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.h),
          borderSide: BorderSide(color: colors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.h),
          borderSide: BorderSide(color: colors.error),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      ),
      cardTheme: CardTheme(
        color: colors.surface,
        elevation: 4,
        shadowColor: colors.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.h),
        ),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: colors.surface,
        foregroundColor: colors.onSurface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: colors.onSurface,
          fontSize: 20.fSize,
          fontFamily: 'SF Pro Display',
          fontWeight: FontWeight.w600,
        ),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colors.surface,
        selectedItemColor: colors.primary,
        unselectedItemColor: colors.onSurface.withOpacity(0.6),
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
    );
  }

  /// Get dark theme
  ThemeData _getDarkTheme(String colorScheme) {
    final colors = _getColorScheme(colorScheme);
    
    return ThemeData(
      brightness: Brightness.dark,
      visualDensity: VisualDensity.standard,
      colorScheme: colors,
      textTheme: _getTextTheme(colors, true),
      scaffoldBackgroundColor: colors.surface,
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colors.primary,
          foregroundColor: colors.onPrimary,
          elevation: 2,
          shadowColor: colors.primary.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.h),
          ),
          visualDensity: const VisualDensity(
            vertical: -4,
            horizontal: -4,
          ),
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: colors.primary,
          side: BorderSide(
            color: colors.primary,
            width: 1.5.h,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.h),
          ),
          visualDensity: const VisualDensity(
            vertical: -4,
            horizontal: -4,
          ),
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colors.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.h),
          borderSide: BorderSide(color: colors.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.h),
          borderSide: BorderSide(color: colors.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.h),
          borderSide: BorderSide(color: colors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.h),
          borderSide: BorderSide(color: colors.error),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      ),
      cardTheme: CardTheme(
        color: colors.surface,
        elevation: 4,
        shadowColor: colors.shadow,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.h),
        ),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: colors.surface,
        foregroundColor: colors.onSurface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: colors.onSurface,
          fontSize: 20.fSize,
          fontFamily: 'SF Pro Display',
          fontWeight: FontWeight.w600,
        ),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: colors.surface,
        selectedItemColor: colors.primary,
        unselectedItemColor: colors.onSurface.withOpacity(0.6),
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
    );
  }

  /// Get color scheme based on selection
  ColorScheme _getColorScheme(String scheme) {
    switch (scheme) {
      case 'kojapay_blue':
        return ColorScheme.light(
          primary: Color(0xFF1231B8),
          onPrimary: Colors.white,
          secondary: Color(0xFF4A90E2),
          onSecondary: Colors.white,
          surface: Color(0xFFF8FAFF),
          onSurface: Color(0xFF1A1A1A),
          background: Colors.white,
          onBackground: Color(0xFF1A1A1A),
          error: Color(0xFFE53935),
          onError: Colors.white,
          outline: Color(0xFFE0E0E0),
          shadow: Color(0xFF1231B8).withOpacity(0.1),
        );
      case 'ocean':
        return ColorScheme.light(
          primary: Color(0xFF1E88E5),
          onPrimary: Colors.white,
          secondary: Color(0xFF26C6DA),
          onSecondary: Colors.white,
          surface: Color(0xFFF5F9FF),
          onSurface: Color(0xFF1A1A1A),
          background: Colors.white,
          onBackground: Color(0xFF1A1A1A),
          error: Color(0xFFE53935),
          onError: Colors.white,
          outline: Color(0xFFE0E0E0),
          shadow: Color(0xFF1E88E5).withOpacity(0.1),
        );
      case 'sunset':
        return ColorScheme.light(
          primary: Color(0xFFFF7043),
          onPrimary: Colors.white,
          secondary: Color(0xFFFFB74D),
          onSecondary: Colors.white,
          surface: Color(0xFFFFF8E1),
          onSurface: Color(0xFF1A1A1A),
          background: Colors.white,
          onBackground: Color(0xFF1A1A1A),
          error: Color(0xFFE53935),
          onError: Colors.white,
          outline: Color(0xFFE0E0E0),
          shadow: Color(0xFFFF7043).withOpacity(0.1),
        );
      case 'forest':
        return ColorScheme.light(
          primary: Color(0xFF4CAF50),
          onPrimary: Colors.white,
          secondary: Color(0xFF8BC34A),
          onSecondary: Colors.white,
          surface: Color(0xFFF1F8E9),
          onSurface: Color(0xFF1A1A1A),
          background: Colors.white,
          onBackground: Color(0xFF1A1A1A),
          error: Color(0xFFE53935),
          onError: Colors.white,
          outline: Color(0xFFE0E0E0),
          shadow: Color(0xFF4CAF50).withOpacity(0.1),
        );
      case 'purple':
        return ColorScheme.light(
          primary: Color(0xFF9C27B0),
          onPrimary: Colors.white,
          secondary: Color(0xFFE1BEE7),
          onSecondary: Colors.white,
          surface: Color(0xFFF3E5F5),
          onSurface: Color(0xFF1A1A1A),
          background: Colors.white,
          onBackground: Color(0xFF1A1A1A),
          error: Color(0xFFE53935),
          onError: Colors.white,
          outline: Color(0xFFE0E0E0),
          shadow: Color(0xFF9C27B0).withOpacity(0.1),
        );
      case 'golden':
        return ColorScheme.light(
          primary: Color(0xFFFFC107),
          onPrimary: Colors.white,
          secondary: Color(0xFFFFD54F),
          onSecondary: Colors.white,
          surface: Color(0xFFFFFDE7),
          onSurface: Color(0xFF1A1A1A),
          background: Colors.white,
          onBackground: Color(0xFF1A1A1A),
          error: Color(0xFFE53935),
          onError: Colors.white,
          outline: Color(0xFFE0E0E0),
          shadow: Color(0xFFFFC107).withOpacity(0.1),
        );
      case 'coral':
        return ColorScheme.light(
          primary: Color(0xFFFF5722),
          onPrimary: Colors.white,
          secondary: Color(0xFFFF8A65),
          onSecondary: Colors.white,
          surface: Color(0xFFFFF3E0),
          onSurface: Color(0xFF1A1A1A),
          background: Colors.white,
          onBackground: Color(0xFF1A1A1A),
          error: Color(0xFFE53935),
          onError: Colors.white,
          outline: Color(0xFFE0E0E0),
          shadow: Color(0xFFFF5722).withOpacity(0.1),
        );
      case 'emerald':
        return ColorScheme.light(
          primary: Color(0xFF00BCD4),
          onPrimary: Colors.white,
          secondary: Color(0xFF80DEEA),
          onSecondary: Colors.white,
          surface: Color(0xFFE0F7FA),
          onSurface: Color(0xFF1A1A1A),
          background: Colors.white,
          onBackground: Color(0xFF1A1A1A),
          error: Color(0xFFE53935),
          onError: Colors.white,
          outline: Color(0xFFE0E0E0),
          shadow: Color(0xFF00BCD4).withOpacity(0.1),
        );
      default: // primary
        return ColorScheme.light(
          primary: Color(0xFF5486E9),
          onPrimary: Colors.white,
          secondary: Color(0xFF58B15C),
          onSecondary: Colors.white,
          surface: Color(0xFFF5F8FB),
          onSurface: Color(0xFF1A1A1A),
          background: Colors.white,
          onBackground: Color(0xFF1A1A1A),
          error: Color(0xFFE53935),
          onError: Colors.white,
          outline: Color(0xFFE0E0E0),
          shadow: Color(0xFF5486E9).withOpacity(0.1),
        );
    }
  }

  /// Get text theme
  TextTheme _getTextTheme(ColorScheme colors, bool isDark) {
    return TextTheme(
      displayLarge: TextStyle(
        color: colors.onSurface,
        fontSize: 32.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w700,
      ),
      displayMedium: TextStyle(
        color: colors.onSurface,
        fontSize: 28.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w700,
      ),
      displaySmall: TextStyle(
        color: colors.onSurface,
        fontSize: 24.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w600,
      ),
      headlineLarge: TextStyle(
        color: colors.onSurface,
        fontSize: 22.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w600,
      ),
      headlineMedium: TextStyle(
        color: colors.onSurface,
        fontSize: 20.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w600,
      ),
      headlineSmall: TextStyle(
        color: colors.onSurface,
        fontSize: 18.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w500,
      ),
      titleLarge: TextStyle(
        color: colors.primary,
        fontSize: 16.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w600,
      ),
      titleMedium: TextStyle(
        color: colors.onSurface,
        fontSize: 15.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w500,
      ),
      titleSmall: TextStyle(
        color: colors.primary,
        fontSize: 14.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w500,
      ),
      bodyLarge: TextStyle(
        color: colors.onSurface,
        fontSize: 16.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w400,
      ),
      bodyMedium: TextStyle(
        color: colors.onSurface.withOpacity(0.8),
        fontSize: 14.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w400,
      ),
      bodySmall: TextStyle(
        color: colors.onSurface.withOpacity(0.6),
        fontSize: 12.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w400,
      ),
      labelLarge: TextStyle(
        color: colors.primary,
        fontSize: 14.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w600,
      ),
      labelMedium: TextStyle(
        color: colors.onSurface.withOpacity(0.7),
        fontSize: 12.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w500,
      ),
      labelSmall: TextStyle(
        color: colors.onSurface.withOpacity(0.5),
        fontSize: 10.fSize,
        fontFamily: 'SF Pro Display',
        fontWeight: FontWeight.w400,
      ),
    );
  }
}

// Global instance
final enhancedThemeHelper = EnhancedThemeHelper(); 
