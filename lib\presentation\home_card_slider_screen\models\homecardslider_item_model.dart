import '../../../core/app_export.dart';

/// This class is used in the [homecardslider_item_widget] screen.
class HomecardsliderItemModel {
  HomecardsliderItemModel({
    this.financialtransaction,
    this.duration,
    this.price,
    this.image,
    this.id,
  }) {
    financialtransaction = financialtransaction ?? Rx("Financial transaction");
    duration = duration ?? Rx("Today, 1:20 Pm");
    price = price ?? Rx("52.00");
    image = image ?? Rx(ImageConstant.imgCallReceivedF);
    id = id ?? Rx("");
  }

  Rx<String>? financialtransaction;

  Rx<String>? duration;

  Rx<String>? price;

  Rx<String>? id;

  Rx<String>? image;
}
