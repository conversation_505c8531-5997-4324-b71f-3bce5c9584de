import '../../../core/app_export.dart';
import 'transactions_item_model.dart';

/// This class defines the variables used in the [transactions_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class TransactionsModel {
  Rx<List<TransactionsItemModel>> transactionsItemList = Rx([
    TransactionsItemModel(
        financialtransaction: "Financial transaction".obs,
        duration: "Today, 1:20 Pm".obs,
        image: ImageConstant.imgCallReceivedF.obs,
        price: "\$52.00".obs),
    TransactionsItemModel(
        financialtransaction: "Loan deposite".obs,
        duration: "20 March, 2:10 Am".obs,
        image: ImageConstant.imgCallDialingF.obs,
        price: "\$120.00".obs),
    TransactionsItemModel(
        financialtransaction: "Withdraw ATM".obs,
        duration: "4 January, 4:00 Am".obs,
        image: ImageConstant.imgCallDialingF.obs,
        price: "\$100.00".obs),
    TransactionsItemModel(
        financialtransaction: "Nicotine cravings".obs,
        duration: "4 January, 4:00 Am".obs,
        image: ImageConstant.imgCallReceivedF.obs,
        price: "\$20.00".obs),
    TransactionsItemModel(
        financialtransaction: "Restlessness".obs,
        duration: "4 January, 4:00 Am".obs,
        image: ImageConstant.imgCallDialingF.obs,
        price: "\$200.00".obs),
    TransactionsItemModel(
        financialtransaction: "Trouble".obs,
        duration: "4 January, 4:00 Am".obs,
        image: ImageConstant.imgCallReceivedF.obs,
        price: "\$123.00".obs),
    TransactionsItemModel(
        financialtransaction: "Sleeping".obs,
        duration: "4 January, 4:00 Am".obs,
        image: ImageConstant.imgCallDialingF.obs,
        price: "\$450.00".obs),
    TransactionsItemModel(
        financialtransaction: "Mood swings".obs,
        duration: "4 January, 4:00 Am".obs,
        image: ImageConstant.imgCallReceivedF.obs,
        price: "\$213.00".obs),
  ]);
}
