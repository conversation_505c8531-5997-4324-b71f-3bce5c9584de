import '../../../core/app_export.dart';
import '../models/statistic_income_model.dart';
import 'package:flutter/material.dart';

/// A controller class for the StatisticIncomePage.
///
/// This class manages the state of the StatisticIncomePage, including the
/// current statisticIncomeModelObj
class StatisticIncomeController extends GetxController {
  StatisticIncomeController(this.statisticIncomeModelObj);

  TextEditingController editTextController = TextEditingController();

  Rx<StatisticIncomeModel> statisticIncomeModelObj;

  SelectionPopupModel? selectedDropDownValue;

  RxString isSelected = "Week".obs;

  @override
  void onClose() {
    super.onClose();
    editTextController.dispose();
  }

  onSelected(dynamic value) {
    for (var element in statisticIncomeModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    statisticIncomeModelObj.value.dropdownItemList.refresh();
  }
}
