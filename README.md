# KojaPay - Mobile Payment App

A modern, feature-rich mobile payment application built with Flutter and GetX architecture.

## 🚀 Features

### Core Features
- **User Authentication**: Secure login/signup with PIN verification
- **Payment Processing**: Send and receive money with real-time transactions
- **Card Management**: Add and manage multiple payment cards
- **Transaction History**: Detailed transaction tracking and history
- **QR Code Payments**: Scan QR codes for instant payments
- **Money Transfers**: Bank-to-bank and peer-to-peer transfers
- **Deposits & Withdrawals**: Manage your account balance
- **Loans**: Apply for and manage personal loans
- **Notifications**: Real-time payment and security notifications

### Enhanced Features
- **Multi-Theme Support**: 8 beautiful color schemes
- **Dark/Light Mode**: Automatic theme switching
- **Customizable UI**: Personalize your app experience
- **Responsive Design**: Optimized for all screen sizes
- **Offline Support**: Core features work without internet
- **Security**: Biometric authentication and encryption

## 🎨 Color Schemes

The app includes 8 stunning color schemes:

1. **Primary** - Classic blue and green
2. **Ocean** - Deep blue and cyan
3. **Sunset** - Warm orange and yellow
4. **Forest** - Natural green tones
5. **Purple** - Royal purple and lavender
6. **Golden** - Luxurious gold and yellow
7. **Coral** - Vibrant coral and orange
8. **Emerald** - Fresh emerald and teal

## 📱 Screenshots

### Main Screens
- Home Dashboard
- Payment Processing
- Transaction History
- User Profile
- Settings & Theme Customization

## 🛠️ Setup Instructions

### Prerequisites
- Flutter SDK (3.1.0 or higher)
- Android Studio / VS Code
- Android SDK (API level 21+)
- iOS SDK (for iOS development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kojaPay-v1.0
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   flutter run
   ```

### Android Studio Setup

1. **Open in Android Studio**
   - Open Android Studio
   - Select "Open an existing project"
   - Navigate to the project folder
   - Click "OK"

2. **Configure Android SDK**
   - Go to File → Project Structure
   - Set minimum SDK to API 21
   - Set target SDK to API 34

3. **Run on Device/Emulator**
   - Connect your device or start an emulator
   - Click the "Run" button (green play icon)

## 🏗️ Project Structure

```
lib/
├── core/                    # Core utilities and configurations
│   ├── app_export.dart     # Main exports
│   ├── Constant/           # App constants
│   ├── errors/             # Error handling
│   ├── network/            # Network utilities
│   └── utils/              # Utility functions
├── data/                   # Data layer
│   ├── apiClient/          # API client
│   ├── data_file.dart      # Data management
│   └── models/             # Data models
├── localization/           # Internationalization
├── presentation/           # UI screens
│   ├── home_screen/        # Home dashboard
│   ├── login_screen/       # Authentication
│   ├── settings_screen/    # App settings
│   └── [other screens]/    # Feature screens
├── routes/                 # Navigation routes
├── theme/                  # App theming
│   ├── enhanced_theme_helper.dart  # Enhanced theme system
│   ├── app_decoration.dart # UI decorations
│   └── custom_text_style.dart # Text styles
└── widgets/               # Reusable widgets
```

## 🎯 Key Dependencies

```yaml
dependencies:
  flutter:
    sdk: flutter
  get: ^4.6.6                    # State management
  connectivity_plus: ^6.0.3      # Network connectivity
  shared_preferences: ^2.2.3     # Local storage
  cached_network_image: ^3.3.1   # Image caching
  flutter_svg: ^2.0.10+1        # SVG support
  pin_code_fields: ^8.0.1       # PIN input
  carousel_slider: ^4.2.1       # Carousel widgets
  smooth_page_indicator: ^1.1.0  # Page indicators
  sms_autofill: ^2.4.0          # SMS auto-fill
  intl: ^0.19.0                 # Internationalization
  pinput: ^5.0.0                # PIN input
  flutter_staggered_animations: ^1.1.1  # Animations
  syncfusion_flutter_charts: ^26.1.40   # Charts
```

## 🎨 Theme Customization

### Changing Colors
1. Navigate to Settings → Theme Settings
2. Select your preferred color scheme
3. Choose between Light/Dark/Auto themes
4. Preview changes in real-time

### Adding New Color Schemes
1. Edit `lib/theme/enhanced_theme_helper.dart`
2. Add new color scheme in `_getColorScheme()` method
3. Update `availableColorSchemes` list
4. Add gradient colors in `_getSchemeColors()`

## 🔧 Development

### Code Style
- Follow Flutter/Dart conventions
- Use meaningful variable names
- Add comments for complex logic
- Keep functions small and focused

### State Management
- Use GetX for state management
- Separate business logic in controllers
- Use reactive programming patterns

### Testing
```bash
# Run unit tests
flutter test

# Run widget tests
flutter test test/widget_test.dart
```

## 📦 Building for Production

### Android
```bash
# Build APK
flutter build apk --release

# Build App Bundle
flutter build appbundle --release
```

### iOS
```bash
# Build for iOS
flutter build ios --release
```

## 🔒 Security Features

- PIN-based authentication
- Biometric authentication support
- Encrypted data storage
- Secure API communication
- Session management

## 📊 Performance

- Optimized image loading
- Efficient state management
- Minimal memory usage
- Fast app startup
- Smooth animations

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation

## 🚀 Getting Started with Android Studio

1. **Open Android Studio**
2. **Import Project**
   - File → Open
   - Select the project folder
   - Wait for Gradle sync

3. **Configure SDK**
   - File → Project Structure
   - Set Android SDK path
   - Configure build tools

4. **Run the App**
   - Connect device/emulator
   - Click Run button
   - Enjoy the app!

## 🎯 Next Steps

- [ ] Add more payment methods
- [ ] Implement push notifications
- [ ] Add multi-language support
- [ ] Enhance security features
- [ ] Add analytics tracking
- [ ] Implement advanced charts
- [ ] Add social features

---

**Happy Coding! 🎉**
