// ignore_for_file: must_be_immutable

import 'package:payway/widgets/custom_bottom_bar.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/repay_success_popup_controller.dart';

class RepaySuccessPopupDialog extends StatelessWidget {
  RepaySuccessPopupDialog(
    this.controller, {
    Key? key,
  }) : super(
          key: key,
        );

  RepaySuccessPopupController controller;

  CustomBottomBarController customBottomBarController =
      Get.put(CustomBottomBarController());

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 388.h,
      padding: EdgeInsets.symmetric(
        horizontal: 24.h,
        vertical: 32.v,
      ),
      decoration: AppDecoration.fillWhiteA.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder12,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomImageView(
              imagePath: ImageConstant.passwordChanged,
              height: 116.v,
              width: 116.h,
              fit: BoxFit.contain,
              alignment: Alignment.center),
          SizedBox(height: 21.v),
          Text(
            "lbl_repay_success".tr,
            style: CustomTextStyles.titleLargeBlack900,
          ),
          SizedBox(height: 6.v),
          Text(
            "msg_has_been_added_to2".tr,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: theme.textTheme.bodyLarge!.copyWith(
              height: 1.29,
            ),
          ),
          SizedBox(height: 39.v),
          CustomElevatedButton(
            text: "lbl_done".tr,
            onPressed: () {
              customBottomBarController.getIndex(3);
              Get.toNamed(
                AppRoutes.homeScreen,
              );
            },
          ),
        ],
      ),
    );
  }
}
