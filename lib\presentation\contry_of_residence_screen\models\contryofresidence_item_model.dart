/// This class is used in the [contryofresidence_item_widget] screen.
// class ContryofresidenceItemModel {
//   ContryofresidenceItemModel({
//     this.england,
//     this.england1,
//     this.england2,
//     this.id,
//   }) {
//     england = england ?? Rx(ImageConstant.imgContrast);
//     england1 = england1 ?? Rx("England");
//     england2 = england2 ?? Rx(ImageConstant.imgEngland);
//     id = id ?? Rx("");
//   }
//
//   Rx<String>? england;
//
//   Rx<String>? england1;
//
//   Rx<String>? england2;
//
//   Rx<String>? id;
// }

class ContryofresidenceItemModel {
  String? image;
  String? title;

  ContryofresidenceItemModel(
    this.image,
    this.title,
  );
}
