import '../../../core/app_export.dart';
import '../models/login_model.dart';
import 'package:flutter/material.dart';

/// A controller class for the LoginScreen.
///
/// This class manages the state of the LoginScreen, including the
/// current loginModelObj
class LoginController extends GetxController {
  TextEditingController emailController = TextEditingController();

  TextEditingController passwordController = TextEditingController();

  TextEditingController firstNameController = TextEditingController();

  TextEditingController lastNameController = TextEditingController();

  TextEditingController signupEmailController = TextEditingController();

  TextEditingController signupPasswordController = TextEditingController();

  TabController? tabController;

  Rx<LoginModel> loginModelObj = LoginModel().obs;

  Rx<bool> isShowPassword = true.obs;

  String? emailValidator(String? value) {
    if (value!.isNotEmpty) {
      if (!RegExp(r'^.+@[a-zA-Z]+\.{1}[a-zA-Z]+(\.{0,1}[a-zA-Z]+)$')
          .hasMatch(value)) {
        return "Please enter a valid email address";
      }
      return null;
    }
    return "Please enter email address.";
  }

  String? passwordValidator(String? value) {
    if (value == null || value.isEmpty) {
      return "Please enter a valid Password";
    }
    return null;
  }

  void clearText() {
    emailController = TextEditingController(text: "");
    passwordController = TextEditingController(text: '');
    update();
  }

  void signUpClearText() {
    firstNameController = TextEditingController(text: "");
    lastNameController = TextEditingController(text: '');
    signupEmailController = TextEditingController(text: "");
    signupPasswordController = TextEditingController(text: "");
    update();
  }

  @override
  void onClose() {
    super.onClose();
    emailController.dispose();
    passwordController.dispose();
  }
}
