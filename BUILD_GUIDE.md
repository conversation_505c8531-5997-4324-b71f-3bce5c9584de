# KojaPay Flutter App - Build Guide

## 🚀 Updated Features

### ✅ Dependencies Updated
- All dependencies updated to latest versions
- Added security dependencies (local_auth, flutter_secure_storage)
- Added database support (sqflite)
- Added advanced HTTP client (dio)

### 🎨 Theme Updated
- **Primary Color**: #1231B8 (KojaPay Blue)
- **New Logo**: Added KojaPay logo with blue theme
- **Default Color Scheme**: Set to 'kojapay_blue'

### 🏗️ Build Configuration Updated
- **Android Gradle**: 7.5 → 8.11.1
- **Android Gradle Plugin**: 7.2.0 → 8.7.3
- **Kotlin**: 1.8.0 → 2.1.0
- **Compile SDK**: 34 → 35
- **Target SDK**: 34 → 35
- **Min SDK**: 21 → 24

## 📱 Building with Android Studio

### Prerequisites
1. **Flutter SDK**: 3.24.0 or higher
2. **Android Studio**: Latest version
3. **Android SDK**: API 35
4. **Java**: JDK 17 or higher

### Step 1: Open Project
```bash
# Open Android Studio
# File → Open
# Select: kojaPay v1.0 folder
```

### Step 2: Install Dependencies
```bash
flutter clean
flutter pub get
```

### Step 3: Configure Android SDK
1. Open Android Studio
2. Go to File → Settings → Appearance & Behavior → System Settings → Android SDK
3. Install Android API 35
4. Install Android SDK Build-Tools 35.0.0

### Step 4: Build APK
```bash
# Debug build
flutter build apk --debug

# Release build
flutter build apk --release

# Split APKs by ABI (recommended for Play Store)
flutter build apk --split-per-abi --release
```

### Step 5: Build App Bundle (for Play Store)
```bash
flutter build appbundle --release
```

## 🎯 Running the App

### Development Mode
```bash
# Connect device or start emulator
flutter devices

# Run app
flutter run

# Run with specific device
flutter run -d <device_id>

# Hot reload enabled automatically
```

### Release Mode
```bash
flutter run --release
```

## 🔧 Troubleshooting

### Common Issues

1. **Gradle Build Failed**
   ```bash
   cd android
   ./gradlew clean
   cd ..
   flutter clean
   flutter pub get
   ```

2. **SDK Version Issues**
   - Update `android/local.properties`
   - Ensure Android SDK 35 is installed

3. **Dependency Conflicts**
   ```bash
   flutter pub deps
   flutter pub upgrade --major-versions
   ```

4. **Build Tools Issues**
   - Update Android Studio
   - Update Flutter: `flutter upgrade`

## 📁 Project Structure

```
kojaPay v1.0/
├── android/           # Android-specific code
├── ios/              # iOS-specific code
├── lib/              # Flutter Dart code
│   ├── core/         # Core utilities
│   ├── data/         # Data models & API
│   ├── presentation/ # UI screens
│   ├── theme/        # Theme & styling
│   └── main.dart     # App entry point
├── assets/           # Images, fonts, etc.
└── pubspec.yaml      # Dependencies
```

## 🎨 Theme Customization

The app now uses KojaPay Blue (#1231B8) as the primary color. To change themes:

1. Go to Settings → Theme Settings in the app
2. Choose from 9 available color schemes:
   - KojaPay Blue (default)
   - Ocean, Sunset, Forest, Purple, Golden, Coral, Emerald

## 🔐 Security Features

New security features added:
- Biometric authentication (fingerprint/face ID)
- Secure storage for sensitive data
- Enhanced encryption
- Local database for offline functionality

## 📦 Build Outputs

After successful build, find files in:
- **APK**: `build/app/outputs/flutter-apk/`
- **App Bundle**: `build/app/outputs/bundle/release/`

## 🚀 Deployment

### Google Play Store
1. Build app bundle: `flutter build appbundle --release`
2. Upload `app-release.aab` to Play Console
3. Follow Play Store guidelines

### Direct Installation
1. Build APK: `flutter build apk --release`
2. Install: `adb install app-release.apk`

## 📊 Performance Tips

1. **Use Release Mode** for testing performance
2. **Enable R8/ProGuard** for smaller APK size
3. **Split APKs** by ABI for Play Store
4. **Test on real devices** for accurate performance

## 🔄 Continuous Integration

For automated builds, use:
```yaml
# GitHub Actions example
- uses: actions/checkout@v3
- uses: subosito/flutter-action@v2
  with:
    flutter-version: '3.24.0'
- run: flutter pub get
- run: flutter build apk --release
```

## 📞 Support

For build issues:
1. Check Flutter doctor: `flutter doctor`
2. Update dependencies: `flutter pub upgrade`
3. Clean and rebuild: `flutter clean && flutter pub get`
