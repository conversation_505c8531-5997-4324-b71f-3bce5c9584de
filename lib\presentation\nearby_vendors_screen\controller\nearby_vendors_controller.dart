import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/data/models/business_account_model.dart';

class NearbyVendorsController extends GetxController {
  // Controllers
  final searchController = TextEditingController();

  // Observable variables
  var isLoading = false.obs;
  var currentLatitude = 37.7749.obs; // Default to San Francisco
  var currentLongitude = -122.4194.obs;
  var selectedCategory = 'All'.obs;
  var searchRadius = 10.0.obs; // km

  // Lists
  var allVendors = <BusinessAccount>[].obs;
  var filteredVendors = <BusinessAccount>[].obs;
  var markers = <Marker>{}.obs;

  // Google Maps controller
  GoogleMapController? mapController;

  @override
  void onInit() {
    super.onInit();
    getCurrentLocation();
    loadMockVendors();
  }

  @override
  void onClose() {
    searchController.dispose();
    mapController?.dispose();
    super.onClose();
  }

  /// Get current location
  Future<void> getCurrentLocation() async {
    try {
      isLoading.value = true;
      
      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          Get.snackbar(
            'Location Permission',
            'Location permission is required to find nearby vendors',
            snackPosition: SnackPosition.BOTTOM,
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        Get.snackbar(
          'Location Permission',
          'Location permissions are permanently denied. Please enable in settings.',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      currentLatitude.value = position.latitude;
      currentLongitude.value = position.longitude;

      // Update map camera
      if (mapController != null) {
        mapController!.animateCamera(
          CameraUpdate.newLatLng(
            LatLng(position.latitude, position.longitude),
          ),
        );
      }

      // Filter vendors by distance
      filterVendorsByDistance();

    } catch (e) {
      Get.snackbar(
        'Location Error',
        'Failed to get current location: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Load mock vendor data
  void loadMockVendors() {
    allVendors.value = [
      BusinessAccount(
        id: '1',
        businessName: 'TechMart Electronics',
        businessType: 'Electronics Store',
        ownerName: 'John Smith',
        email: '<EMAIL>',
        phone: '******-0123',
        address: '123 Main St',
        city: 'San Francisco',
        state: 'CA',
        country: 'United States',
        postalCode: '94102',
        latitude: 37.7749,
        longitude: -122.4194,
        businessLicense: 'BL123456',
        taxId: 'TAX123456',
        bankAccountNumber: '**********',
        routingNumber: '*********',
        balance: 50000.0,
        categories: ['Electronics', 'Technology'],
        products: ['phones', 'laptops', 'accessories'],
        childAccounts: [],
        isVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      BusinessAccount(
        id: '2',
        businessName: 'Fresh Foods Market',
        businessType: 'Grocery Store',
        ownerName: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '******-0124',
        address: '456 Market St',
        city: 'San Francisco',
        state: 'CA',
        country: 'United States',
        postalCode: '94105',
        latitude: 37.7849,
        longitude: -122.4094,
        businessLicense: 'BL123457',
        taxId: 'TAX123457',
        bankAccountNumber: '**********',
        routingNumber: '*********',
        balance: 75000.0,
        categories: ['Food', 'Grocery'],
        products: ['fruits', 'vegetables', 'dairy'],
        childAccounts: [],
        isVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      BusinessAccount(
        id: '3',
        businessName: 'Fashion Forward',
        businessType: 'Clothing Store',
        ownerName: 'Mike Davis',
        email: '<EMAIL>',
        phone: '******-0125',
        address: '789 Mission St',
        city: 'San Francisco',
        state: 'CA',
        country: 'United States',
        postalCode: '94103',
        latitude: 37.7649,
        longitude: -122.4294,
        businessLicense: 'BL123458',
        taxId: 'TAX123458',
        bankAccountNumber: '**********',
        routingNumber: '*********',
        balance: 30000.0,
        categories: ['Fashion', 'Clothing'],
        products: ['shirts', 'pants', 'shoes'],
        childAccounts: [],
        isVerified: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    updateMarkers();
    filterVendorsByDistance();
  }

  /// Update map markers
  void updateMarkers() {
    markers.clear();
    
    for (var vendor in allVendors) {
      markers.add(
        Marker(
          markerId: MarkerId(vendor.id),
          position: LatLng(vendor.latitude, vendor.longitude),
          infoWindow: InfoWindow(
            title: vendor.businessName,
            snippet: vendor.businessType,
            onTap: () => selectVendor(vendor),
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            vendor.isVerified ? BitmapDescriptor.hueGreen : BitmapDescriptor.hueRed,
          ),
        ),
      );
    }
  }

  /// Filter vendors by distance
  void filterVendorsByDistance() {
    filteredVendors.value = allVendors.where((vendor) {
      double distance = Geolocator.distanceBetween(
        currentLatitude.value,
        currentLongitude.value,
        vendor.latitude,
        vendor.longitude,
      ) / 1000; // Convert to km
      
      return distance <= searchRadius.value;
    }).toList();
  }

  /// Search vendors
  void searchVendors(String query) {
    if (query.isEmpty) {
      filterVendorsByDistance();
    } else {
      filteredVendors.value = allVendors.where((vendor) {
        bool matchesSearch = vendor.businessName.toLowerCase().contains(query.toLowerCase()) ||
                           vendor.businessType.toLowerCase().contains(query.toLowerCase()) ||
                           vendor.categories.any((category) => category.toLowerCase().contains(query.toLowerCase()));
        
        double distance = Geolocator.distanceBetween(
          currentLatitude.value,
          currentLongitude.value,
          vendor.latitude,
          vendor.longitude,
        ) / 1000;
        
        return matchesSearch && distance <= searchRadius.value;
      }).toList();
    }
  }

  /// Select a vendor
  void selectVendor(BusinessAccount vendor) {
    // Navigate to vendor details or products
    Get.toNamed('/vendor_details', arguments: vendor);
  }

  /// Show filter dialog
  void showFilterDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('Filter Vendors'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Search Radius: ${searchRadius.value.toInt()} km'),
            Slider(
              value: searchRadius.value,
              min: 1.0,
              max: 50.0,
              divisions: 49,
              onChanged: (value) {
                searchRadius.value = value;
                filterVendorsByDistance();
              },
            ),
            SizedBox(height: 16.h),
            DropdownButtonFormField<String>(
              value: selectedCategory.value,
              decoration: InputDecoration(
                labelText: 'Category',
                border: OutlineInputBorder(),
              ),
              items: ['All', 'Electronics', 'Food', 'Fashion', 'Technology', 'Grocery', 'Clothing']
                  .map((category) => DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      ))
                  .toList(),
              onChanged: (value) {
                selectedCategory.value = value!;
                _filterByCategory();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              filterVendorsByDistance();
            },
            child: Text('Apply'),
          ),
        ],
      ),
    );
  }

  /// Filter by category
  void _filterByCategory() {
    if (selectedCategory.value == 'All') {
      filterVendorsByDistance();
    } else {
      filteredVendors.value = allVendors.where((vendor) {
        bool matchesCategory = vendor.categories.contains(selectedCategory.value);
        
        double distance = Geolocator.distanceBetween(
          currentLatitude.value,
          currentLongitude.value,
          vendor.latitude,
          vendor.longitude,
        ) / 1000;
        
        return matchesCategory && distance <= searchRadius.value;
      }).toList();
    }
  }

  /// Map created callback
  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
  }

  /// Camera move callback
  void onCameraMove(CameraPosition position) {
    // Update current position if needed
  }

  /// Map tap callback
  void onMapTap(LatLng position) {
    // Handle map tap if needed
  }
} 