import '../../../core/app_export.dart';

/// This class is used in the [transfer_item_widget] screen.
class TransferItemModel {
  TransferItemModel({
    this.wajihTaysirHandal,
    this.wajihTaysirHandal1,
    this.twoHundredNinetyThreeMillionSe,
    this.id,
  }) {
    wajihTaysirHandal =
        wajihTaysirHandal ?? Rx(ImageConstant.imgEllipse135556x56);
    wajihTaysirHandal1 = wajihTaysirHandal1 ?? Rx("Wajih Taysir Handal");
    twoHundredNinetyThreeMillionSe =
        twoHundredNinetyThreeMillionSe ?? Rx("(02) 9378 5922");
    id = id ?? Rx("");
  }

  Rx<String>? wajihTaysirHandal;

  Rx<String>? wajihTaysirHandal1;

  Rx<String>? twoHundredNinetyThreeMillionSe;

  Rx<String>? id;
}
