enum ChildAccountStatus {
  pending,
  active,
  suspended,
  disabled
}

class ChildAccountModel {
  final String id;
  final String parentId;
  final String childName;
  final DateTime dateOfBirth;
  final String email;
  final String phone;
  final ChildAccountStatus status;
  final double balance;
  final double spendingLimit;
  final List<String> allowedCategories;
  final List<String> blockedCategories;
  final Map<String, dynamic> parentalControls;
  final DateTime createdAt;
  final DateTime updatedAt;

  ChildAccountModel({
    required this.id,
    required this.parentId,
    required this.childName,
    required this.dateOfBirth,
    required this.email,
    required this.phone,
    required this.status,
    required this.balance,
    required this.spendingLimit,
    required this.allowedCategories,
    required this.blockedCategories,
    required this.parentalControls,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ChildAccountModel.fromJson(Map<String, dynamic> json) {
    return ChildAccountModel(
      id: json['id'] ?? '',
      parentId: json['parentId'] ?? '',
      childName: json['childName'] ?? '',
      dateOfBirth: DateTime.parse(json['dateOfBirth'] ?? DateTime.now().toIso8601String()),
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      status: ChildAccountStatus.values.firstWhere(
        (e) => e.toString() == 'ChildAccountStatus.${json['status']}',
        orElse: () => ChildAccountStatus.pending,
      ),
      balance: json['balance']?.toDouble() ?? 0.0,
      spendingLimit: json['spendingLimit']?.toDouble() ?? 0.0,
      allowedCategories: List<String>.from(json['allowedCategories'] ?? []),
      blockedCategories: List<String>.from(json['blockedCategories'] ?? []),
      parentalControls: Map<String, dynamic>.from(json['parentalControls'] ?? {}),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parentId': parentId,
      'childName': childName,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'email': email,
      'phone': phone,
      'status': status.toString().split('.').last,
      'balance': balance,
      'spendingLimit': spendingLimit,
      'allowedCategories': allowedCategories,
      'blockedCategories': blockedCategories,
      'parentalControls': parentalControls,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month || 
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  String get statusText {
    switch (status) {
      case ChildAccountStatus.pending:
        return 'Pending';
      case ChildAccountStatus.active:
        return 'Active';
      case ChildAccountStatus.suspended:
        return 'Suspended';
      case ChildAccountStatus.disabled:
        return 'Disabled';
    }
  }

  bool get isActive => status == ChildAccountStatus.active;
  bool get isPending => status == ChildAccountStatus.pending;
  bool get isSuspended => status == ChildAccountStatus.suspended;
  bool get isDisabled => status == ChildAccountStatus.disabled;

  // Parental control getters
  bool get spendingLimitEnabled => parentalControls['spendingLimitEnabled'] ?? false;
  bool get categoryRestrictionsEnabled => parentalControls['categoryRestrictionsEnabled'] ?? false;
  bool get timeRestrictionsEnabled => parentalControls['timeRestrictionsEnabled'] ?? false;
  bool get locationRestrictionsEnabled => parentalControls['locationRestrictionsEnabled'] ?? false;
  bool get notificationEnabled => parentalControls['notificationEnabled'] ?? true;
  
  String get allowedTimeStart => parentalControls['allowedTimeStart'] ?? '08:00';
  String get allowedTimeEnd => parentalControls['allowedTimeEnd'] ?? '22:00';
  double get maxTransactionAmount => parentalControls['maxTransactionAmount']?.toDouble() ?? 100.0;
  int get dailySpendingLimit => parentalControls['dailySpendingLimit'] ?? 50;
} 