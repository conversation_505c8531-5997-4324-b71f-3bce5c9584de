import '../../../core/app_export.dart';import '../models/enter_your_pin_one_model.dart';import 'package:sms_autofill/sms_autofill.dart';import 'package:flutter/material.dart';/// A controller class for the EnterYourPinOneScreen.
///
/// This class manages the state of the EnterYourPinOneScreen, including the
/// current enterYourPinOneModelObj
class EnterYourPinOneController extends GetxController with  CodeAutoFill {Rx<TextEditingController> otpController = TextEditingController().obs;

Rx<EnterYourPinOneModel> enterYourPinOneModelObj = EnterYourPinOneModel().obs;

@override void codeUpdated() { otpController.value.text = code ?? ''; } 
@override void onInit() { super.onInit(); listenForCode(); } 
 }
