import 'package:get/get.dart';
import '../presentation/splace_screen/splace_screen.dart';
import '../presentation/splace_screen/binding/splace_binding.dart';
import '../presentation/onboarding_one_screen/onboarding_one_screen.dart';
import '../presentation/onboarding_one_screen/binding/onboarding_one_binding.dart';
import '../presentation/onboarding_two_screen/onboarding_two_screen.dart';
import '../presentation/onboarding_two_screen/binding/onboarding_two_binding.dart';
import '../presentation/onboarding_three_screen/onboarding_three_screen.dart';
import '../presentation/onboarding_three_screen/binding/onboarding_three_binding.dart';
import '../presentation/login_screen/login_screen.dart';
import '../presentation/login_screen/binding/login_binding.dart';
import '../presentation/sign_up_screen/sign_up_screen.dart';
import '../presentation/sign_up_screen/binding/sign_up_binding.dart';
import '../presentation/forgot_password_screen/forgot_password_screen.dart';
import '../presentation/forgot_password_screen/binding/forgot_password_binding.dart';
import '../presentation/verify_code_screen/verify_code_screen.dart';
import '../presentation/verify_code_screen/binding/verify_code_binding.dart';
import '../presentation/reaset_password_one_screen/reaset_password_one_screen.dart';
import '../presentation/reaset_password_one_screen/binding/reaset_password_one_binding.dart';
import '../presentation/contry_of_residence_screen/contry_of_residence_screen.dart';
import '../presentation/contry_of_residence_screen/binding/contry_of_residence_binding.dart';
import '../presentation/create_pin_screen/create_pin_screen.dart';
import '../presentation/create_pin_screen/binding/create_pin_binding.dart';
import '../presentation/identity_verify_popup_screen/identity_verify_popup_screen.dart';
import '../presentation/identity_verify_popup_screen/binding/identity_verify_popup_binding.dart';
import '../presentation/proof_of_residency_screen/proof_of_residency_screen.dart';
import '../presentation/proof_of_residency_screen/binding/proof_of_residency_binding.dart';
import '../presentation/photo_id_card_screen/photo_id_card_screen.dart';
import '../presentation/photo_id_card_screen/binding/photo_id_card_binding.dart';
import '../presentation/selfie_with_id_card_screen/selfie_with_id_card_screen.dart';
import '../presentation/selfie_with_id_card_screen/binding/selfie_with_id_card_binding.dart';
import '../presentation/home_screen/home_screen.dart';
import '../presentation/home_screen/binding/home_binding.dart';
import '../presentation/home_card_slider_screen/home_card_slider_screen.dart';
import '../presentation/home_card_slider_screen/binding/home_card_slider_binding.dart';
import '../presentation/add_card_screen/add_card_screen.dart';
import '../presentation/add_card_screen/binding/add_card_binding.dart';
import '../presentation/confirm_payment_two_screen/confirm_payment_two_screen.dart';
import '../presentation/confirm_payment_two_screen/binding/confirm_payment_two_binding.dart';
import '../presentation/notification_empty_screen/notification_empty_screen.dart';
import '../presentation/notification_empty_screen/binding/notification_empty_binding.dart';
import '../presentation/notification_screen/notification_screen.dart';
import '../presentation/notification_screen/binding/notification_binding.dart';
import '../presentation/scan_qr_to_ride_screen/scan_qr_to_ride_screen.dart';
import '../presentation/scan_qr_to_ride_screen/binding/scan_qr_to_ride_binding.dart';
import '../presentation/transfer_details_two_screen/transfer_details_two_screen.dart';
import '../presentation/transfer_details_two_screen/binding/transfer_details_two_binding.dart';
import '../presentation/select_bank_popup_three_screen/select_bank_popup_three_screen.dart';
import '../presentation/select_bank_popup_three_screen/binding/select_bank_popup_three_binding.dart';
import '../presentation/transfer_details_fill_screen/transfer_details_fill_screen.dart';
import '../presentation/transfer_details_fill_screen/binding/transfer_details_fill_binding.dart';
import '../presentation/confirm_two_screen/confirm_two_screen.dart';
import '../presentation/confirm_two_screen/binding/confirm_two_binding.dart';
import '../presentation/confirm_payment_one_screen/confirm_payment_one_screen.dart';
import '../presentation/confirm_payment_one_screen/binding/confirm_payment_one_binding.dart';
import '../presentation/details_one_screen/details_one_screen.dart';
import '../presentation/details_one_screen/binding/details_one_binding.dart';
import '../presentation/share_popup_screen/share_popup_screen.dart';
import '../presentation/share_popup_screen/binding/share_popup_binding.dart';
import '../presentation/top_up_screen/top_up_screen.dart';
import '../presentation/top_up_screen/binding/top_up_binding.dart';
import '../presentation/select_bank_popup_two_screen/select_bank_popup_two_screen.dart';
import '../presentation/select_bank_popup_two_screen/binding/select_bank_popup_two_binding.dart';
import '../presentation/confirm_one_screen/confirm_one_screen.dart';
import '../presentation/confirm_one_screen/binding/confirm_one_binding.dart';
import '../presentation/confirm_payment_screen/confirm_payment_screen.dart';
import '../presentation/confirm_payment_screen/binding/confirm_payment_binding.dart';
import '../presentation/withdraw_screen/withdraw_screen.dart';
import '../presentation/withdraw_screen/binding/withdraw_binding.dart';
import '../presentation/select_bank_popup_one_screen/select_bank_popup_one_screen.dart';
import '../presentation/select_bank_popup_one_screen/binding/select_bank_popup_one_binding.dart';
import '../presentation/transfer_details_one_screen/transfer_details_one_screen.dart';
import '../presentation/transfer_details_one_screen/binding/transfer_details_one_binding.dart';
import '../presentation/confirm_screen/confirm_screen.dart';
import '../presentation/confirm_screen/binding/confirm_binding.dart';
import '../presentation/enter_your_pin_screen/enter_your_pin_screen.dart';
import '../presentation/enter_your_pin_screen/binding/enter_your_pin_binding.dart';
import '../presentation/details_screen/details_screen.dart';
import '../presentation/details_screen/binding/details_binding.dart';
import '../presentation/transfer_screen/transfer_screen.dart';
import '../presentation/transfer_screen/binding/transfer_binding.dart';
import '../presentation/transfer_details_three_screen/transfer_details_three_screen.dart';
import '../presentation/transfer_details_three_screen/binding/transfer_details_three_binding.dart';
import '../presentation/select_bank_popup_screen/select_bank_popup_screen.dart';
import '../presentation/select_bank_popup_screen/binding/select_bank_popup_binding.dart';
import '../presentation/transfer_details_screen/transfer_details_screen.dart';
import '../presentation/transfer_details_screen/binding/transfer_details_binding.dart';
import '../presentation/confirm_three_screen/confirm_three_screen.dart';
import '../presentation/confirm_three_screen/binding/confirm_three_binding.dart';
import '../presentation/enter_your_pin_one_screen/enter_your_pin_one_screen.dart';
import '../presentation/enter_your_pin_one_screen/binding/enter_your_pin_one_binding.dart';
import '../presentation/details_two_screen/details_two_screen.dart';
import '../presentation/details_two_screen/binding/details_two_binding.dart';
import '../presentation/transactions_screen/transactions_screen.dart';
import '../presentation/transactions_screen/binding/transactions_binding.dart';
import '../presentation/transactions_details_screen/transactions_details_screen.dart';
import '../presentation/transactions_details_screen/binding/transactions_details_binding.dart';
import '../presentation/confirm_payment_three_screen/confirm_payment_three_screen.dart';
import '../presentation/confirm_payment_three_screen/binding/confirm_payment_three_binding.dart';
import '../presentation/deposits_current_deposite_tab_container_screen/deposits_current_deposite_tab_container_screen.dart';
import '../presentation/deposits_current_deposite_tab_container_screen/binding/deposits_current_deposite_tab_container_binding.dart';
import '../presentation/open_deposits_screen/open_deposits_screen.dart';
import '../presentation/open_deposits_screen/binding/open_deposits_binding.dart';
import '../presentation/open_money_bank_screen/open_money_bank_screen.dart';
import '../presentation/open_money_bank_screen/binding/open_money_bank_binding.dart';
import '../presentation/add_card_one_screen/add_card_one_screen.dart';
import '../presentation/add_card_one_screen/binding/add_card_one_binding.dart';
import '../presentation/confirm_payment_six_screen/confirm_payment_six_screen.dart';
import '../presentation/confirm_payment_six_screen/binding/confirm_payment_six_binding.dart';
import '../presentation/statistic_income_tab_container_screen/statistic_income_tab_container_screen.dart';
import '../presentation/statistic_income_tab_container_screen/binding/statistic_income_tab_container_binding.dart';
import '../presentation/time_popup_screen/time_popup_screen.dart';
import '../presentation/time_popup_screen/binding/time_popup_binding.dart';
import '../presentation/this_week_screen/this_week_screen.dart';
import '../presentation/this_week_screen/binding/this_week_binding.dart';
import '../presentation/details_three_screen/details_three_screen.dart';
import '../presentation/details_three_screen/binding/details_three_binding.dart';
import '../presentation/loans_container_screen/loans_container_screen.dart';
import '../presentation/loans_container_screen/binding/loans_container_binding.dart';
import '../presentation/new_loan_screen/new_loan_screen.dart';
import '../presentation/new_loan_screen/binding/new_loan_binding.dart';
import '../presentation/repay_screen/repay_screen.dart';
import '../presentation/repay_screen/binding/repay_binding.dart';
import '../presentation/select_bank_popup_four_screen/select_bank_popup_four_screen.dart';
import '../presentation/select_bank_popup_four_screen/binding/select_bank_popup_four_binding.dart';
import '../presentation/confirm_four_screen/confirm_four_screen.dart';
import '../presentation/confirm_four_screen/binding/confirm_four_binding.dart';
import '../presentation/confirm_payment_four_screen/confirm_payment_four_screen.dart';
import '../presentation/confirm_payment_four_screen/binding/confirm_payment_four_binding.dart';
import '../presentation/profile_screen/profile_screen.dart';
import '../presentation/profile_screen/binding/profile_binding.dart';
import '../presentation/my_profile_screen/my_profile_screen.dart';
import '../presentation/my_profile_screen/binding/my_profile_binding.dart';
import '../presentation/edit_profile_screen/edit_profile_screen.dart';
import '../presentation/edit_profile_screen/binding/edit_profile_binding.dart';
import '../presentation/security_screen/security_screen.dart';
import '../presentation/security_screen/binding/security_binding.dart';
import '../presentation/change_password_screen/change_password_screen.dart';
import '../presentation/change_password_screen/binding/change_password_binding.dart';
import '../presentation/settings_screen/settings_screen.dart';
import '../presentation/settings_screen/binding/settings_binding.dart';
import '../presentation/about_us_screen/about_us_screen.dart';
import '../presentation/about_us_screen/binding/about_us_binding.dart';
import '../presentation/help_screen/help_screen.dart';
import '../presentation/help_screen/binding/help_binding.dart';
import '../presentation/feedback_screen/feedback_screen.dart';
import '../presentation/feedback_screen/binding/feedback_binding.dart';
import '../presentation/privacy_policy_screen/privacy_policy_screen.dart';
import '../presentation/privacy_policy_screen/binding/privacy_policy_binding.dart';
import '../presentation/terms_conditions_screen/terms_conditions_screen.dart';
import '../presentation/terms_conditions_screen/binding/terms_conditions_binding.dart';
import '../presentation/account_upgrade_screen/account_upgrade_screen.dart';
import '../presentation/account_upgrade_screen/binding/account_upgrade_binding.dart';
import '../presentation/account_switcher_screen/account_switcher_screen.dart';
import '../presentation/account_switcher_screen/binding/account_switcher_binding.dart';
import '../presentation/business_verification_screen/business_verification_screen.dart';
import '../presentation/business_verification_screen/binding/business_verification_binding.dart';
import '../presentation/child_account_screen/child_account_screen.dart';
import '../presentation/child_account_screen/binding/child_account_binding.dart';
import '../presentation/business_account_screen/business_account_screen.dart';
import '../presentation/business_account_screen/binding/business_account_binding.dart';
import '../presentation/nearby_vendors_screen/nearby_vendors_screen.dart';
import '../presentation/nearby_vendors_screen/binding/nearby_vendors_binding.dart';
import '../presentation/product_management_screen/product_management_screen.dart';
import '../presentation/product_management_screen/binding/product_management_binding.dart';
import '../presentation/business_dashboard_screen/business_dashboard_screen.dart';
import '../presentation/business_dashboard_screen/binding/business_dashboard_binding.dart';
import '../presentation/app_navigation_screen/app_navigation_screen.dart';
import '../presentation/app_navigation_screen/binding/app_navigation_binding.dart';

class AppRoutes {
  static const String splaceScreen = '/splace_screen';

  static const String onboardingOneScreen = '/onboarding_one_screen';

  static const String onboardingTwoScreen = '/onboarding_two_screen';

  static const String onboardingThreeScreen = '/onboarding_three_screen';

  static const String loginScreen = '/login_screen';

  static const String loginScreenErrorPage = '/login_screen_error_page';

  static const String loginScreenErrorTabContainerScreen =
      '/login_screen_error_tab_container_screen';

  static const String loginScreenFilledScreen = '/login_screen_filled_screen';

  static const String signUpScreen = '/sign_up_screen';

  static const String forgotPasswordScreen = '/forgot_password_screen';

  static const String verifyCodeScreen = '/verify_code_screen';

  static const String reasetPasswordOneScreen = '/reaset_password_one_screen';

  static const String contryOfResidenceScreen = '/contry_of_residence_screen';

  static const String createPinScreen = '/create_pin_screen';

  static const String identityVerifyPopupScreen =
      '/identity_verify_popup_screen';

  static const String proofOfResidencyScreen = '/proof_of_residency_screen';

  static const String photoIdCardScreen = '/photo_id_card_screen';

  static const String selfieWithIdCardScreen = '/selfie_with_id_card_screen';

  static const String homeScreen = '/home_screen';

  static const String homeCardSliderScreen = '/home_card_slider_screen';

  static const String addCardScreen = '/add_card_screen';

  static const String confirmPaymentTwoScreen = '/confirm_payment_two_screen';

  static const String notificationEmptyScreen = '/notification_empty_screen';

  static const String notificationScreen = '/notification_screen';

  static const String scanQrToRideScreen = '/scan_qr_to_ride_screen';

  static const String transferDetailsTwoScreen = '/transfer_details_two_screen';

  static const String selectBankPopupThreeScreen =
      '/select_bank_popup_three_screen';

  static const String transferDetailsFillScreen =
      '/transfer_details_fill_screen';

  static const String confirmTwoScreen = '/confirm_two_screen';

  static const String confirmPaymentOneScreen = '/confirm_payment_one_screen';

  static const String detailsOneScreen = '/details_one_screen';

  static const String sharePopupScreen = '/share_popup_screen';

  static const String topUpScreen = '/top_up_screen';

  static const String selectBankPopupTwoScreen =
      '/select_bank_popup_two_screen';

  static const String confirmOneScreen = '/confirm_one_screen';

  static const String confirmPaymentScreen = '/confirm_payment_screen';

  static const String withdrawScreen = '/withdraw_screen';

  static const String selectBankPopupOneScreen =
      '/select_bank_popup_one_screen';

  static const String transferDetailsOneScreen = '/transfer_details_one_screen';

  static const String confirmScreen = '/confirm_screen';

  static const String enterYourPinScreen = '/enter_your_pin_screen';

  static const String detailsScreen = '/details_screen';

  static const String transferScreen = '/transfer_screen';

  static const String transferDetailsThreeScreen =
      '/transfer_details_three_screen';

  static const String selectBankPopupScreen = '/select_bank_popup_screen';

  static const String transferDetailsScreen = '/transfer_details_screen';

  static const String confirmThreeScreen = '/confirm_three_screen';

  static const String enterYourPinOneScreen = '/enter_your_pin_one_screen';

  static const String detailsTwoScreen = '/details_two_screen';

  static const String transactionsScreen = '/transactions_screen';

  static const String transactionsDetailsScreen =
      '/transactions_details_screen';

  static const String confirmPaymentThreeScreen =
      '/confirm_payment_three_screen';

  static const String depositsCurrentDepositePage =
      '/deposits_current_deposite_page';

  static const String depositsCurrentDepositeTabContainerScreen =
      '/deposits_current_deposite_tab_container_screen';

  static const String depositsCurrentMoneyPage = '/deposits_current_money_page';

  static const String openDepositsScreen = '/open_deposits_screen';

  static const String openMoneyBankScreen = '/open_money_bank_screen';

  static const String addCardOneScreen = '/add_card_one_screen';

  static const String confirmPaymentSixScreen = '/confirm_payment_six_screen';

  static const String statisticIncomePage = '/statistic_income_page';

  static const String statisticIncomeTabContainerScreen =
      '/statistic_income_tab_container_screen';

  static const String statisticExpensesPage = '/statistic_expenses_page';

  static const String timePopupScreen = '/time_popup_screen';

  static const String thisWeekScreen = '/this_week_screen';

  static const String detailsThreeScreen = '/details_three_screen';

  static const String loansContainerScreen = '/loans_container_screen';

  static const String loansPage = '/loans_page';

  static const String newLoanScreen = '/new_loan_screen';

  static const String repayScreen = '/repay_screen';

  static const String selectBankPopupFourScreen =
      '/select_bank_popup_four_screen';

  static const String confirmFourScreen = '/confirm_four_screen';

  static const String confirmPaymentFourScreen = '/confirm_payment_four_screen';

  static const String guestProfilePage = '/guest_profile_page';

  static const String profileScreen = '/profile_screen';

  static const String myProfileScreen = '/my_profile_screen';

  static const String editProfileScreen = '/edit_profile_screen';

  static const String securityScreen = '/security_screen';

  static const String changePasswordScreen = '/change_password_screen';

  static const String settingsScreen = '/settings_screen';

  static const String aboutUsScreen = '/about_us_screen';

  static const String helpScreen = '/help_screen';

  static const String feedbackScreen = '/feedback_screen';

  static const String privacyPolicyScreen = '/privacy_policy_screen';

  static const String termsConditionsScreen = '/terms_conditions_screen';

  static const String accountUpgradeScreen = '/account_upgrade_screen';

  static const String accountSwitcherScreen = '/account_switcher_screen';

  static const String businessVerificationScreen = '/business_verification_screen';

  static const String childAccountScreen = '/child_account_screen';

  static const String businessAccountScreen = '/business_account_screen';

  static const String nearbyVendorsScreen = '/nearby_vendors_screen';

  static const String productManagementScreen = '/product_management_screen';

  static const String businessDashboardScreen = '/business_dashboard_screen';

  static const String themeSettingsScreen = '/theme_settings_screen';

  static const String appNavigationScreen = '/app_navigation_screen';

  static const String initialRoute = '/initialRoute';

  static List<GetPage> pages = [
    GetPage(
      name: splaceScreen,
      transition: Transition.rightToLeft,
      page: () => SplashScreen(),
      bindings: [
        SplaceBinding(),
      ],
    ),
    GetPage(
      name: onboardingOneScreen,
      transition: Transition.rightToLeft,
      page: () => OnboardingOneScreen(),
      bindings: [
        OnboardingOneBinding(),
      ],
    ),
    GetPage(
      name: onboardingTwoScreen,
      transition: Transition.rightToLeft,
      page: () => OnboardingTwoScreen(),
      bindings: [
        OnboardingTwoBinding(),
      ],
    ),
    GetPage(
      name: onboardingThreeScreen,
      transition: Transition.rightToLeft,
      page: () => OnboardingThreeScreen(),
      bindings: [
        OnboardingThreeBinding(),
      ],
    ),
    GetPage(
      name: loginScreen,
      transition: Transition.rightToLeft,
      page: () => LoginScreen(),
      bindings: [
        LoginBinding(),
      ],
    ),
    // GetPage(
    //   name: loginScreenErrorTabContainerScreen,
    //   transition: Transition.rightToLeft,
    //   page: () => LoginScreenErrorTabContainerScreen(),
    //   bindings: [
    //     LoginScreenErrorTabContainerBinding(),
    //   ],
    // ),
    // GetPage(
    //   name: loginScreenFilledScreen,
    //   transition: Transition.rightToLeft,
    //   page: () => LoginScreenFilledScreen(),
    //   bindings: [
    //     LoginScreenFilledBinding(),
    //   ],
    // ),
    GetPage(
      name: signUpScreen,
      transition: Transition.rightToLeft,
      page: () => SignUpScreen(),
      bindings: [
        SignUpBinding(),
      ],
    ),
    GetPage(
      name: forgotPasswordScreen,
      transition: Transition.rightToLeft,
      page: () => ForgotPasswordScreen(),
      bindings: [
        ForgotPasswordBinding(),
      ],
    ),
    GetPage(
      name: verifyCodeScreen,
      transition: Transition.rightToLeft,
      page: () => VerifyCodeScreen(),
      bindings: [
        VerifyCodeBinding(),
      ],
    ),
    GetPage(
      name: reasetPasswordOneScreen,
      transition: Transition.rightToLeft,
      page: () => ResetPasswordOneScreen(),
      bindings: [
        ReasetPasswordOneBinding(),
      ],
    ),
    GetPage(
      name: contryOfResidenceScreen,
      transition: Transition.rightToLeft,
      page: () => ContryOfResidenceScreen(),
      bindings: [
        ContryOfResidenceBinding(),
      ],
    ),
    GetPage(
      name: createPinScreen,
      transition: Transition.rightToLeft,
      page: () => CreatePinScreen(),
      bindings: [
        CreatePinBinding(),
      ],
    ),
    GetPage(
      name: identityVerifyPopupScreen,
      transition: Transition.rightToLeft,
      page: () => IdentityVerifyPopupScreen(),
      bindings: [
        IdentityVerifyPopupBinding(),
      ],
    ),
    GetPage(
      name: proofOfResidencyScreen,
      transition: Transition.rightToLeft,
      page: () => ProofOfResidencyScreen(),
      bindings: [
        ProofOfResidencyBinding(),
      ],
    ),
    GetPage(
      name: photoIdCardScreen,
      transition: Transition.rightToLeft,
      page: () => PhotoIdCardScreen(),
      bindings: [
        PhotoIdCardBinding(),
      ],
    ),
    GetPage(
      name: selfieWithIdCardScreen,
      transition: Transition.rightToLeft,
      page: () => SelfieWithIdCardScreen(),
      bindings: [
        SelfieWithIdCardBinding(),
      ],
    ),
    GetPage(
      name: homeScreen,
      transition: Transition.rightToLeft,
      page: () => HomeContainerScreen(),
      bindings: [
        HomeBinding(),
      ],
    ),
    GetPage(
      name: homeCardSliderScreen,
      transition: Transition.rightToLeft,
      page: () => HomeCardSliderScreen(),
      bindings: [
        HomeCardSliderBinding(),
      ],
    ),
    GetPage(
      name: addCardScreen,
      transition: Transition.rightToLeft,
      page: () => AddCardScreen(),
      bindings: [
        AddCardBinding(),
      ],
    ),
    GetPage(
      name: confirmPaymentTwoScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmPaymentTwoScreen(),
      bindings: [
        ConfirmPaymentTwoBinding(),
      ],
    ),
    GetPage(
      name: notificationEmptyScreen,
      transition: Transition.rightToLeft,
      page: () => NotificationEmptyScreen(),
      bindings: [
        NotificationEmptyBinding(),
      ],
    ),
    GetPage(
      name: notificationScreen,
      transition: Transition.rightToLeft,
      page: () => NotificationScreen(),
      bindings: [
        NotificationBinding(),
      ],
    ),
    GetPage(
      name: scanQrToRideScreen,
      transition: Transition.rightToLeft,
      page: () => ScanQrToRideScreen(),
      bindings: [
        ScanQrToRideBinding(),
      ],
    ),
    GetPage(
      name: transferDetailsTwoScreen,
      transition: Transition.rightToLeft,
      page: () => TransferDetailsTwoScreen(),
      bindings: [
        TransferDetailsTwoBinding(),
      ],
    ),
    GetPage(
      name: selectBankPopupThreeScreen,
      transition: Transition.rightToLeft,
      page: () => SelectBankPopupThreeScreen(),
      bindings: [
        SelectBankPopupThreeBinding(),
      ],
    ),
    GetPage(
      name: transferDetailsFillScreen,
      transition: Transition.rightToLeft,
      page: () => TransferDetailsFillScreen(),
      bindings: [
        TransferDetailsFillBinding(),
      ],
    ),
    GetPage(
      name: confirmTwoScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmTwoScreen(),
      bindings: [
        ConfirmTwoBinding(),
      ],
    ),
    GetPage(
      name: confirmPaymentOneScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmPaymentOneScreen(),
      bindings: [
        ConfirmPaymentOneBinding(),
      ],
    ),
    GetPage(
      name: detailsOneScreen,
      transition: Transition.rightToLeft,
      page: () => DetailsOneScreen(),
      bindings: [
        DetailsOneBinding(),
      ],
    ),
    GetPage(
      name: sharePopupScreen,
      transition: Transition.rightToLeft,
      page: () => SharePopupScreen(),
      bindings: [
        SharePopupBinding(),
      ],
    ),
    GetPage(
      name: topUpScreen,
      transition: Transition.rightToLeft,
      page: () => TopUpScreen(),
      bindings: [
        TopUpBinding(),
      ],
    ),
    GetPage(
      name: selectBankPopupTwoScreen,
      transition: Transition.rightToLeft,
      page: () => SelectBankPopupTwoScreen(),
      bindings: [
        SelectBankPopupTwoBinding(),
      ],
    ),
    GetPage(
      name: confirmOneScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmOneScreen(),
      bindings: [
        ConfirmOneBinding(),
      ],
    ),
    GetPage(
      name: confirmPaymentScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmPaymentScreen(),
      bindings: [
        ConfirmPaymentBinding(),
      ],
    ),
    GetPage(
      name: withdrawScreen,
      transition: Transition.rightToLeft,
      page: () => WithdrawScreen(),
      bindings: [
        WithdrawBinding(),
      ],
    ),
    GetPage(
      name: selectBankPopupOneScreen,
      transition: Transition.rightToLeft,
      page: () => SelectBankPopupOneScreen(),
      bindings: [
        SelectBankPopupOneBinding(),
      ],
    ),
    GetPage(
      name: transferDetailsOneScreen,
      transition: Transition.rightToLeft,
      page: () => TransferDetailsOneScreen(),
      bindings: [
        TransferDetailsOneBinding(),
      ],
    ),
    GetPage(
      name: confirmScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmScreen(),
      bindings: [
        ConfirmBinding(),
      ],
    ),
    GetPage(
      name: enterYourPinScreen,
      transition: Transition.rightToLeft,
      page: () => EnterYourPinScreen(),
      bindings: [
        EnterYourPinBinding(),
      ],
    ),
    GetPage(
      name: detailsScreen,
      transition: Transition.rightToLeft,
      page: () => DetailsScreen(),
      bindings: [
        DetailsBinding(),
      ],
    ),
    GetPage(
      name: transferScreen,
      transition: Transition.rightToLeft,
      page: () => TransferScreen(),
      bindings: [
        TransferBinding(),
      ],
    ),
    GetPage(
      name: transferDetailsThreeScreen,
      transition: Transition.rightToLeft,
      page: () => TransferDetailsThreeScreen(),
      bindings: [
        TransferDetailsThreeBinding(),
      ],
    ),
    GetPage(
      name: selectBankPopupScreen,
      transition: Transition.rightToLeft,
      page: () => SelectBankPopupScreen(),
      bindings: [
        SelectBankPopupBinding(),
      ],
    ),
    GetPage(
      name: transferDetailsScreen,
      transition: Transition.rightToLeft,
      page: () => TransferDetailsScreen(),
      bindings: [
        TransferDetailsBinding(),
      ],
    ),
    GetPage(
      name: confirmThreeScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmThreeScreen(),
      bindings: [
        ConfirmThreeBinding(),
      ],
    ),
    GetPage(
      name: enterYourPinOneScreen,
      transition: Transition.rightToLeft,
      page: () => EnterYourPinOneScreen(),
      bindings: [
        EnterYourPinOneBinding(),
      ],
    ),
    GetPage(
      name: detailsTwoScreen,
      transition: Transition.rightToLeft,
      page: () => DetailsTwoScreen(),
      bindings: [
        DetailsTwoBinding(),
      ],
    ),
    GetPage(
      name: transactionsScreen,
      transition: Transition.rightToLeft,
      page: () => TransactionsScreen(),
      bindings: [
        TransactionsBinding(),
      ],
    ),
    GetPage(
      name: transactionsDetailsScreen,
      transition: Transition.rightToLeft,
      page: () => TransactionsDetailsScreen(),
      bindings: [
        TransactionsDetailsBinding(),
      ],
    ),
    GetPage(
      name: confirmPaymentThreeScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmPaymentThreeScreen(),
      bindings: [
        ConfirmPaymentThreeBinding(),
      ],
    ),
    GetPage(
      name: depositsCurrentDepositeTabContainerScreen,
      transition: Transition.rightToLeft,
      page: () => DepositsCurrentDepositeTabContainerScreen(),
      bindings: [
        DepositsCurrentDepositeTabContainerBinding(),
      ],
    ),
    GetPage(
      name: openDepositsScreen,
      transition: Transition.rightToLeft,
      page: () => OpenDepositsScreen(),
      bindings: [
        OpenDepositsBinding(),
      ],
    ),
    GetPage(
      name: openMoneyBankScreen,
      transition: Transition.rightToLeft,
      page: () => OpenMoneyBankScreen(),
      bindings: [
        OpenMoneyBankBinding(),
      ],
    ),
    GetPage(
      name: addCardOneScreen,
      transition: Transition.rightToLeft,
      page: () => AddCardOneScreen(),
      bindings: [
        AddCardOneBinding(),
      ],
    ),
    GetPage(
      name: confirmPaymentSixScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmPaymentSixScreen(),
      bindings: [
        ConfirmPaymentSixBinding(),
      ],
    ),
    GetPage(
      name: statisticIncomeTabContainerScreen,
      transition: Transition.rightToLeft,
      page: () => StatisticIncomeTabContainerScreen(),
      bindings: [
        StatisticIncomeTabContainerBinding(),
      ],
    ),
    GetPage(
      name: timePopupScreen,
      transition: Transition.rightToLeft,
      page: () => TimePopupScreen(),
      bindings: [
        TimePopupBinding(),
      ],
    ),
    GetPage(
      name: thisWeekScreen,
      transition: Transition.rightToLeft,
      page: () => ThisWeekScreen(),
      bindings: [
        ThisWeekBinding(),
      ],
    ),
    GetPage(
      name: detailsThreeScreen,
      transition: Transition.rightToLeft,
      page: () => DetailsThreeScreen(),
      bindings: [
        DetailsThreeBinding(),
      ],
    ),
    GetPage(
      name: loansContainerScreen,
      transition: Transition.rightToLeft,
      page: () => LoansContainerScreen(),
      bindings: [
        LoansContainerBinding(),
      ],
    ),
    GetPage(
      name: newLoanScreen,
      transition: Transition.rightToLeft,
      page: () => NewLoanScreen(),
      bindings: [
        NewLoanBinding(),
      ],
    ),
    GetPage(
      name: repayScreen,
      transition: Transition.rightToLeft,
      page: () => RepayScreen(),
      bindings: [
        RepayBinding(),
      ],
    ),
    GetPage(
      name: selectBankPopupFourScreen,
      transition: Transition.rightToLeft,
      page: () => SelectBankPopupFourScreen(),
      bindings: [
        SelectBankPopupFourBinding(),
      ],
    ),
    GetPage(
      name: confirmFourScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmFourScreen(),
      bindings: [
        ConfirmFourBinding(),
      ],
    ),
    GetPage(
      name: confirmPaymentFourScreen,
      transition: Transition.rightToLeft,
      page: () => ConfirmPaymentFourScreen(),
      bindings: [
        ConfirmPaymentFourBinding(),
      ],
    ),
    GetPage(
      name: profileScreen,
      transition: Transition.rightToLeft,
      page: () => ProfileScreen(),
      bindings: [
        ProfileBinding(),
      ],
    ),
    GetPage(
      name: myProfileScreen,
      transition: Transition.rightToLeft,
      page: () => MyProfileScreen(),
      bindings: [
        MyProfileBinding(),
      ],
    ),
    GetPage(
      name: editProfileScreen,
      transition: Transition.rightToLeft,
      page: () => EditProfileScreen(),
      bindings: [
        EditProfileBinding(),
      ],
    ),
    GetPage(
      name: securityScreen,
      transition: Transition.rightToLeft,
      page: () => SecurityScreen(),
      bindings: [
        SecurityBinding(),
      ],
    ),
    GetPage(
      name: changePasswordScreen,
      transition: Transition.rightToLeft,
      page: () => ChangePasswordScreen(),
      bindings: [
        ChangePasswordBinding(),
      ],
    ),
    GetPage(
      name: settingsScreen,
      transition: Transition.rightToLeft,
      page: () => SettingsScreen(),
      bindings: [
        SettingsBinding(),
      ],
    ),
    GetPage(
      name: aboutUsScreen,
      transition: Transition.rightToLeft,
      page: () => AboutUsScreen(),
      bindings: [
        AboutUsBinding(),
      ],
    ),
    GetPage(
      name: helpScreen,
      transition: Transition.rightToLeft,
      page: () => HelpScreen(),
      bindings: [
        HelpBinding(),
      ],
    ),
    GetPage(
      name: feedbackScreen,
      transition: Transition.rightToLeft,
      page: () => FeedbackScreen(),
      bindings: [
        FeedbackBinding(),
      ],
    ),
    GetPage(
      name: privacyPolicyScreen,
      transition: Transition.rightToLeft,
      page: () => PrivacyPolicyScreen(),
      bindings: [
        PrivacyPolicyBinding(),
      ],
    ),
    GetPage(
      name: termsConditionsScreen,
      transition: Transition.rightToLeft,
      page: () => TermsConditionsScreen(),
      bindings: [
        TermsConditionsBinding(),
      ],
    ),
    GetPage(
      name: appNavigationScreen,
      transition: Transition.rightToLeft,
      page: () => AppNavigationScreen(),
      bindings: [
        AppNavigationBinding(),
      ],
    ),
    GetPage(
      name: initialRoute,
      transition: Transition.rightToLeft,
      page: () => SplashScreen(),
      bindings: [
        SplaceBinding(),
      ],
    )
  ];
}
