import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'widgets/thisweek_item_widget.dart';
import 'models/thisweek_item_model.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/this_week_controller.dart';

class ThisWeekScreen extends GetWidget<ThisWeekController> {
  const ThisWeekScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Padding(
        padding: EdgeInsets.only(left: 20.h, top: 16.v, right: 20.h),
        child: Obx(
          () => ListView.separated(
            physics: BouncingScrollPhysics(),
            shrinkWrap: true,
            separatorBuilder: (context, index) {
              return SizedBox(height: 16.v);
            },
            itemCount:
                controller.thisWeekModelObj.value.thisweekItemList.value.length,
            itemBuilder: (context, index) {
              ThisweekItemModel model = controller
                  .thisWeekModelObj.value.thisweekItemList.value[index];
              return ThisweekItemWidget(
                model,
                onTapTransactions: () {
                  onTapTransactions();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 17.v, bottom: 20.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_this_week".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Navigates to the statisticIncomeTabContainerScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the detailsThreeScreen when the action is triggered.
  onTapTransactions() {
    Get.toNamed(
      AppRoutes.detailsThreeScreen,
    );
  }
}
