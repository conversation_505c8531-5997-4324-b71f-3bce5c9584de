import '../models/transfer_item_model.dart';
import '../controller/transfer_controller.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class TransferItemWidget extends StatelessWidget {
  TransferItemWidget(
    this.transferItemModelObj, {
    Key? key,
    this.onTapWajihTaysirHandal,
  }) : super(
          key: key,
        );

  TransferItemModel transferItemModelObj;

  var controller = Get.find<TransferController>();

  VoidCallback? onTapWajihTaysirHandal;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTapWajihTaysirHandal!.call();
      },
      child: Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder12,
        ),
        child: Row(
          children: [
            Obx(
              () => CustomImageView(
                imagePath: transferItemModelObj.wajihTaysirHandal!.value,
                height: 56.adaptSize,
                width: 56.adaptSize,
                radius: BorderRadius.circular(
                  28.h,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 12.h,
                top: 3.v,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(
                    () => Text(
                      transferItemModelObj.wajihTaysirHandal1!.value,
                      style: theme.textTheme.titleMedium,
                    ),
                  ),
                  SizedBox(height: 8.v),
                  Obx(
                    () => Text(
                      transferItemModelObj
                          .twoHundredNinetyThreeMillionSe!.value,
                      style: theme.textTheme.bodyLarge,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
