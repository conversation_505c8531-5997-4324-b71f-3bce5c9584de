import 'package:payway/core/app_export.dart';
import 'statisticexpenses_item_model.dart';

/// This class defines the variables used in the [statistic_expenses_page],
/// and is typically used to hold data that is passed between different parts of the application.
class StatisticExpensesModel {
  Rx<List<SelectionPopupModel>> dropdownItemList = Rx([
    SelectionPopupModel(
      id: 1,
      title: "Item One",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "Item Two",
    ),
    SelectionPopupModel(
      id: 3,
      title: "Item Three",
    )
  ]);

  Rx<List<StatisticexpensesItemModel>> statisticexpensesItemList = Rx([
    StatisticexpensesItemModel(
      financialtransaction: "Financial transaction".obs,
      duration: "Today, 1:20 Pm".obs,
      price: "52.00".obs,
      image: ImageConstant.imgCallDialingF.obs,
    ),
    StatisticexpensesItemModel(
      financialtransaction: "Loan deposit".obs,
      duration: "20 March, 2:10 Am".obs,
      price: "120.00".obs,
      image: ImageConstant.imgCallDialingF.obs,
    ),
    StatisticexpensesItemModel(
      financialtransaction: "Withdraw ATM".obs,
      duration: "4 January, 4:00 Am".obs,
      price: "\$100.00".obs,
      image: ImageConstant.imgCallDialingF.obs,
    ),
    StatisticexpensesItemModel(
      financialtransaction: "Nicotine cravings".obs,
      duration: "4 January, 4:00 Am".obs,
      price: "\$20.00".obs,
      image: ImageConstant.imgCallDialingF.obs,
    ),
  ]);
}
