import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/data/models/account_tier_model.dart';
import 'controller/account_upgrade_controller.dart';

class AccountUpgradeScreen extends GetWidget<AccountUpgradeController> {
  const AccountUpgradeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Upgrade Account',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrentAccountInfo(),
            <PERSON>zed<PERSON><PERSON>(height: 24.h),
            _buildAccountTypes(),
            Si<PERSON><PERSON><PERSON>(height: 24.h),
            _buildTierComparison(),
            <PERSON><PERSON><PERSON><PERSON>(height: 32.h),
            _buildUpgradeButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentAccountInfo() {
    return Obx(() => Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_circle,
                  color: Theme.of(context).colorScheme.primary,
                  size: 32.w,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Account',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        controller.currentAccount.value?.tierDisplayName ?? 'Basic',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Text(
              'Account Type: ${controller.currentAccount.value?.type.toString().split('.').last ?? 'Personal'}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildAccountTypes() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Account Type',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildAccountTypeCard(
                title: 'Personal',
                description: 'Individual account for personal use',
                icon: Icons.person,
                color: Colors.blue,
                isSelected: controller.selectedAccountType.value == AccountType.personal,
                onTap: () => controller.selectAccountType(AccountType.personal),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildAccountTypeCard(
                title: 'Business',
                description: 'Business account with advanced features',
                icon: Icons.business,
                color: Colors.green,
                isSelected: controller.selectedAccountType.value == AccountType.business,
                onTap: () => controller.selectAccountType(AccountType.business),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildAccountTypeCard(
                title: 'Child',
                description: 'Child account with parental controls',
                icon: Icons.child_care,
                color: Colors.orange,
                isSelected: controller.selectedAccountType.value == AccountType.child,
                onTap: () => controller.selectAccountType(AccountType.child),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAccountTypeCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.h),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(12.h),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32.w,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isSelected ? color : null,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 4.h),
            Text(
              description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTierComparison() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Tier',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        SizedBox(height: 16.h),
        ...AccountTier.values.map((tier) => _buildTierCard(tier)),
      ],
    );
  }

  Widget _buildTierCard(AccountTier tier) {
    final isSelected = controller.selectedTier.value == tier;
    final isCurrentTier = controller.currentAccount.value?.tier == tier;
    
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: isCurrentTier ? null : () => controller.selectTier(tier),
        child: Container(
          padding: EdgeInsets.all(16.h),
          decoration: BoxDecoration(
            color: isSelected ? Theme.of(context).colorScheme.primary.withOpacity(0.1) : null,
            borderRadius: BorderRadius.circular(12.h),
            border: isSelected ? Border.all(
              color: Theme.of(context).colorScheme.primary,
              width: 2,
            ) : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tier.tierDisplayName,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: isSelected ? Theme.of(context).colorScheme.primary : null,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          tier.tierDescription,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '\$${tier.monthlyFee.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected ? Theme.of(context).colorScheme.primary : null,
                        ),
                      ),
                      Text(
                        'per month',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              if (isCurrentTier)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.h),
                  ),
                  child: Text(
                    'Current Plan',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12.fSize,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                )
              else if (isSelected)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.h),
                  ),
                  child: Text(
                    'Selected',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12.fSize,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              SizedBox(height: 12.h),
              Text(
                'Features:',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              ...tier.tierFeatures.map((feature) => Padding(
                padding: EdgeInsets.only(bottom: 4.h),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 16.w,
                      color: Colors.green,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        feature,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUpgradeButton() {
    return Obx(() => SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: controller.canUpgrade.value ? () => controller.upgradeAccount() : null,
        child: controller.isLoading.value
          ? CircularProgressIndicator(color: Colors.white)
          : Text(
              controller.getUpgradeButtonText(),
              style: TextStyle(fontSize: 16.fSize, fontWeight: FontWeight.w600),
            ),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          backgroundColor: controller.canUpgrade.value 
            ? Theme.of(context).colorScheme.primary 
            : Colors.grey,
        ),
      ),
    ));
  }
} 