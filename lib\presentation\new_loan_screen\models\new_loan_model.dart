import 'package:payway/core/app_export.dart';

/// This class defines the variables used in the [new_loan_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class NewLoanModel {
  Rx<List<SelectionPopupModel>> dropdownItemList = Rx([
    SelectionPopupModel(
      id: 1,
      title: "Rupee",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "Dollar",
    ),
    SelectionPopupModel(
      id: 3,
      title: "Pound",
    )
  ]);

  Rx<List<SelectionPopupModel>> dropdownItemList1 = Rx([
    SelectionPopupModel(
      id: 1,
      title: "1 Year",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "2 Year",
    ),
    SelectionPopupModel(
      id: 3,
      title: "3 Three",
    )
  ]);
}
