import '../../../core/app_export.dart';import '../models/transfer_details_fill_model.dart';/// A controller class for the TransferDetailsFillScreen.
///
/// This class manages the state of the TransferDetailsFillScreen, including the
/// current transferDetailsFillModelObj
class TransferDetailsFillController extends GetxController {Rx<TransferDetailsFillModel> transferDetailsFillModelObj = TransferDetailsFillModel().obs;

SelectionPopupModel? selectedDropDownValue;

onSelected(dynamic value) { for (var element in transferDetailsFillModelObj.value.dropdownItemList.value) {element.isSelected = false; if (element.id == value.id) {element.isSelected = true;}} transferDetailsFillModelObj.value.dropdownItemList.refresh(); } 
 }
