import 'package:carousel_slider/carousel_slider.dart';import 'widgets/seventythree_item_widget.dart';import 'models/seventythree_item_model.dart';import 'package:smooth_page_indicator/smooth_page_indicator.dart';import 'package:payway/widgets/custom_elevated_button.dart';import 'package:flutter/material.dart';import 'package:payway/core/app_export.dart';import 'controller/onboarding_two_controller.dart';class OnboardingTwoScreen extends GetWidget<OnboardingTwoController> {const OnboardingTwoScreen({Key? key}) : super(key: key);

@override Widget build(BuildContext context) { return SafeArea(child: Scaffold(body: Container(width: double.maxFinite, padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 24.v), child: Column(children: [CustomImageView(imagePath: ImageConstant.imgRectangle34624256474x388, height: 474.v, width: 388.h, radius: BorderRadius.circular(12.h)), SizedBox(height: 43.v), _buildOnboardingTitle(), SizedBox(height: 48.v), CustomElevatedButton(text: "lbl_next".tr, onPressed: () {onTapNext();}), SizedBox(height: 17.v), GestureDetector(onTap: () {onTapTxtSkip();}, child: Text("lbl_skip".tr, style: theme.textTheme.bodyLarge)), SizedBox(height: 5.v)])))); } 
/// Section Widget
Widget _buildOnboardingTitle() { return Padding(padding: EdgeInsets.symmetric(horizontal: 9.h), child: Column(children: [Obx(() => CarouselSlider.builder(options: CarouselOptions(height: 99.v, initialPage: 0, autoPlay: true, viewportFraction: 1.0, enableInfiniteScroll: false, scrollDirection: Axis.horizontal, onPageChanged: (index, reason) {controller.sliderIndex.value = index;}), itemCount: controller.onboardingTwoModelObj.value.seventythreeItemList.value.length, itemBuilder: (context, index, realIndex) {SeventythreeItemModel model = controller.onboardingTwoModelObj.value.seventythreeItemList.value[index]; return SeventythreeItemWidget(model);})), SizedBox(height: 45.v), Obx(() => SizedBox(height: 8.v, child: AnimatedSmoothIndicator(activeIndex: controller.sliderIndex.value, count: controller.onboardingTwoModelObj.value.seventythreeItemList.value.length, axisDirection: Axis.horizontal, effect: ScrollingDotsEffect(spacing: 8, activeDotColor: theme.colorScheme.primary, dotColor: theme.colorScheme.primary.withOpacity(0.5), dotHeight: 8.v, dotWidth: 8.h))))])); } 
/// Navigates to the onboardingThreeScreen when the action is triggered.
onTapNext() { Get.toNamed(AppRoutes.onboardingThreeScreen, ); } 
/// Navigates to the onboardingThreeScreen when the action is triggered.
onTapTxtSkip() { Get.toNamed(AppRoutes.onboardingThreeScreen, ); } 
 }
