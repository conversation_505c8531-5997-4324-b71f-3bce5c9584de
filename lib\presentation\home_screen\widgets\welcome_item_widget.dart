import '../models/welcome_item_model.dart';
import '../controller/home_controller.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class WelcomeItemWidget extends StatelessWidget {
  WelcomeItemWidget(
    this.welcomeItemModelObj, {
    Key? key,
    this.onTapBtnBellSixtyOne,
  }) : super(
          key: key,
        );

  WelcomeItemModel welcomeItemModelObj;

  var controller = Get.find<HomeController>();

  VoidCallback? onTapBtnBellSixtyOne;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 20.h,
          vertical: 16.v,
        ),
        decoration: AppDecoration.fillPrimary.copyWith(
          borderRadius: BorderRadiusStyle.customBorderBL24,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(
                      () => Text(
                        welcomeItemModelObj.welcome!.value,
                        style: CustomTextStyles.headlineMediumWhiteA700,
                      ),
                    ),
                    Obx(
                      () => Text(
                        welcomeItemModelObj.johnAbram!.value,
                        style: CustomTextStyles.bodyLargeGray100,
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 3.v),
                  child: Obx(
                    () => CustomIconButton(
                      height: 48.adaptSize,
                      width: 48.adaptSize,
                      padding: EdgeInsets.all(12.h),
                      decoration: IconButtonStyleHelper.fillWhiteA,
                      onTap: () {
                        onTapBtnBellSixtyOne!.call();
                      },
                      child: CustomImageView(
                        imagePath: welcomeItemModelObj.bellSixtyOne!.value,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 180.v),
            SizedBox(
              height: 8.v,
              child: AnimatedSmoothIndicator(
                activeIndex: 0,
                count: 3,
                effect: ScrollingDotsEffect(
                  spacing: 8,
                  activeDotColor: appTheme.whiteA700,
                  dotColor: appTheme.whiteA700.withOpacity(0.42),
                  dotHeight: 8.v,
                  dotWidth: 8.h,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
