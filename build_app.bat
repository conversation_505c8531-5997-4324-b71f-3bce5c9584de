@echo off
echo ========================================
echo KojaPay Flutter App - Build Script
echo ========================================
echo.

echo Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter from https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo.
echo Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo ERROR: Failed to clean project
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Running Flutter doctor...
flutter doctor

echo.
echo ========================================
echo Choose build type:
echo 1. Debug APK
echo 2. Release APK
echo 3. Release App Bundle (for Play Store)
echo 4. Run on connected device
echo ========================================
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo.
    echo Building Debug APK...
    flutter build apk --debug
    echo.
    echo Debug APK built successfully!
    echo Location: build\app\outputs\flutter-apk\app-debug.apk
) else if "%choice%"=="2" (
    echo.
    echo Building Release APK...
    flutter build apk --release --split-per-abi
    echo.
    echo Release APK built successfully!
    echo Location: build\app\outputs\flutter-apk\
) else if "%choice%"=="3" (
    echo.
    echo Building Release App Bundle...
    flutter build appbundle --release
    echo.
    echo App Bundle built successfully!
    echo Location: build\app\outputs\bundle\release\app-release.aab
) else if "%choice%"=="4" (
    echo.
    echo Checking connected devices...
    flutter devices
    echo.
    echo Running app on connected device...
    flutter run
) else (
    echo Invalid choice. Please run the script again.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build Complete!
echo ========================================
echo.
echo To open in Android Studio:
echo 1. Open Android Studio
echo 2. File -^> Open
echo 3. Select this project folder
echo.
pause
