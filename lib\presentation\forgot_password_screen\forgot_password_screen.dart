// ignore_for_file: deprecated_member_use

import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/core/utils/validation_functions.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import '../../core/Constant/constants.dart';
import 'controller/forgot_password_controller.dart';

// ignore_for_file: must_be_immutable
class ForgotPasswordScreen extends GetWidget<ForgotPasswordController> {
  ForgotPasswordScreen({Key? key}) : super(key: key);

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: CustomAppBar(
            leadingWidth: 52.h,
            leading: AppbarLeadingImage(
                imagePath: ImageConstant.imgExpandMoreFil,
                margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
                onTap: () {
                  onTapExpandMoreFIL();
                }),
            title: AppbarSubtitle(
                text: "lbl_forgot_password".tr,
                margin: EdgeInsets.only(left: 16.h)),
            styleType: Style.bgFill),
        body: SizedBox(
          width: SizeUtils.width,
          child: SingleChildScrollView(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Form(
              key: _formKey,
              child: Container(
                width: double.maxFinite,
                padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 17.v),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                        width: 346.h,
                        margin: EdgeInsets.only(right: 74.h),
                        child: Text("msg_type_your_email".tr,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: CustomTextStyles.bodyLargeBlack900
                                .copyWith(height: 1.29))),
                    SizedBox(height: 40.v),
                    CustomTextFormField(
                        controller: controller.emailController,
                        hintText: "lbl_email_address".tr,
                        hintStyle: theme.textTheme.bodyLarge!,
                        textInputAction: TextInputAction.done,
                        textInputType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null ||
                              (!isValidEmail(value, isRequired: true))) {
                            return "err_msg_please_enter_valid_email".tr;
                          }
                          return null;
                        }),
                    SizedBox(height: 48.v),
                    CustomElevatedButton(
                        text: "lbl_continue".tr,
                        onPressed: () {
                          if (_formKey.currentState!.validate()) {
                            onTapContinue();
                          }
                        }),
                    SizedBox(height: 5.v)
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Navigates to the loginScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the verifyCodeScreen when the action is triggered.
  onTapContinue() {
    Get.toNamed(
      AppRoutes.verifyCodeScreen,
    );
  }
}
