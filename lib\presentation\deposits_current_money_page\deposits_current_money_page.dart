import 'package:payway/widgets/custom_outlined_button.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import '../../data/data_file.dart';
import '../deposits_current_deposite_tab_container_screen/models/deposits_current_deposite_tab_container_model.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore_for_file: must_be_immutable
class DepositsCurrentMoneyPage extends StatelessWidget {
  DepositsCurrentMoneyPage({Key? key}) : super(key: key);

  // DepositsCurrentMoneyController controller =
  // Get.put(DepositsCurrentMoneyController(DepositsCurrentMoneyModel().obs));

  final List<DepositsCurrentMoneyTabContainerModel> currentMoneyList =
      DataFile.getAllCurrentMoney();

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      separatorBuilder: (context, index) => SizedBox(height: 16.v),
      itemCount: currentMoneyList.length,
      itemBuilder: (context, index) {
        DepositsCurrentMoneyTabContainerModel model = currentMoneyList[index];

        return Container(
          padding: EdgeInsets.all(16.h),
          decoration: AppDecoration.outlineBlack
              .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLaptopNothingPowar(
                  laptopNothingPowar: model.title, price: model.price),
              SizedBox(height: 23.v),
              Container(
                height: 6.v,
                width: 356.h,
                decoration: BoxDecoration(
                    color: appTheme.gray100,
                    borderRadius: BorderRadius.circular(3.h)),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(3.h),
                  child: LinearProgressIndicator(
                    value: 0.21,
                    backgroundColor: appTheme.gray100,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
              ),
              SizedBox(height: 8.v),
              Text(model.subPrice, style: CustomTextStyles.bodyLargeBlack900),
              SizedBox(height: 16.v),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildWithdrawal(),
                  _buildTopUp(),
                ],
              ),
            ],
          ),
        );
      },
      shrinkWrap: true,
      padding:
          EdgeInsets.only(left: 20.h, top: 16.h, right: 20.h, bottom: 240.v),
    );
  }

  /// Section Widget
  Widget _buildWithdrawal() {
    return Expanded(
        child: CustomOutlinedButton(
            height: 40.v,
            text: "lbl_withdrawal".tr,
            margin: EdgeInsets.only(right: 8.h),
            buttonTextStyle: CustomTextStyles.titleSmallGray700,
            onPressed: () {
              onTapWithdrawal();
            }));
  }

  /// Section Widget
  Widget _buildTopUp() {
    return Expanded(
        child: CustomElevatedButton(
            height: 40.v,
            text: "lbl_top_up".tr,
            margin: EdgeInsets.only(left: 8.h),
            buttonStyle: CustomButtonStyles.fillIndigo,
            buttonTextStyle: CustomTextStyles.titleSmallSemiBold14,
            onPressed: () {
              onTapTopUp();
            }));
  }

  /// Common widget
  Widget _buildLaptopNothingPowar({
    required String laptopNothingPowar,
    required String price,
  }) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Text(laptopNothingPowar,
          style:
              theme.textTheme.titleMedium!.copyWith(color: appTheme.black900)),
      Text(price,
          style: CustomTextStyles.bodyLargeBlack900
              .copyWith(color: appTheme.black900))
    ]);
  }

  /// Navigates to the withdrawScreen when the action is triggered.
  onTapWithdrawal() {
    Get.toNamed(
      AppRoutes.withdrawScreen,
    );
  }

  /// Navigates to the topUpScreen when the action is triggered.
  onTapTopUp() {
    Get.toNamed(
      AppRoutes.topUpScreen,
    );
  }

  /// Navigates to the homeScreen when the action is triggered.
  onTapIcon() {
    Get.toNamed(
      AppRoutes.homeScreen,
    );
  }

  /// Navigates to the statisticIncomeTabContainerScreen when the action is triggered.
  onTapIcon1() {
    Get.toNamed(
      AppRoutes.statisticIncomeTabContainerScreen,
    );
  }

  /// Navigates to the loansContainerScreen when the action is triggered.
  onTapIcon2() {
    Get.toNamed(
      AppRoutes.loansContainerScreen,
    );
  }
}
