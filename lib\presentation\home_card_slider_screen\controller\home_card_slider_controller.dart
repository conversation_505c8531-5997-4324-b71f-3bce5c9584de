import '../../../core/app_export.dart';
import '../../home_screen/models/home_model.dart';
import '../models/card_item_model.dart';
import '../models/categories_model.dart';
import '../models/home_card_slider_model.dart';

/// A controller class for the HomeCardSliderScreen.
///
/// This class manages the state of the HomeCardSliderScreen, including the
/// current homeCardSliderModelObj
class HomeCardSliderController extends GetxController {
  Rx<HomeCardSliderModel> homeCardSliderModelObj = HomeCardSliderModel().obs;

  Rx<int> sliderIndex = 0.obs;

  Rx<int> sliderIndex1 = 0.obs;

  List<SliderItemModel> slider = HomeModel.slidercarItemList();

  List<CategorieModel> categoryList = CategoriesModel.getCategories();
}
