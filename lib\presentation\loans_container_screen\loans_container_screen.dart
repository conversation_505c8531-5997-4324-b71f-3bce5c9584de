import 'package:payway/presentation/loans_page/loans_page.dart';
import 'package:payway/presentation/guest_profile_page/guest_profile_page.dart';
import 'package:payway/widgets/custom_bottom_bar.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/loans_container_controller.dart';

class LoansContainerScreen extends GetWidget<LoansContainerController> {
  const LoansContainerScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Navigator(
            key: Get.nestedKey(1),
            initialRoute: AppRoutes.loansPage,
            onGenerateRoute: (routeSetting) => GetPageRoute(
                page: () => getCurrentPage(routeSetting.name!),
                transition: Transition.noTransition)),
        bottomNavigationBar: _buildBottomBar());
  }

  /// Section Widget
  Widget _buildBottomBar() {
    return CustomBottomBar(onChanged: (BottomBarEnum type) {
      Get.toNamed(getCurrentRoute(type), id: 1);
    });
  }

  ///Handling route based on bottom click actions
  String getCurrentRoute(BottomBarEnum type) {
    switch (type) {
      case BottomBarEnum.Home:
        return "/";
      case BottomBarEnum.Deposits:
        return "/";
      case BottomBarEnum.Statistic:
        return "/";
      case BottomBarEnum.Loan:
        return AppRoutes.loansPage;
      case BottomBarEnum.Profile:
        return AppRoutes.guestProfilePage;
      default:
        return "/";
    }
  }

  ///Handling page based on route
  Widget getCurrentPage(String currentRoute) {
    switch (currentRoute) {
      case AppRoutes.loansPage:
        return LoansPage();
      case AppRoutes.guestProfilePage:
        return GuestProfilePage();
      default:
        return DefaultWidget();
    }
  }
}
