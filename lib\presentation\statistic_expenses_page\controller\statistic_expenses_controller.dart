import '../../../core/app_export.dart';
import '../models/statistic_expenses_model.dart';
import 'package:flutter/material.dart';

/// A controller class for the StatisticExpensesPage.
///
/// This class manages the state of the StatisticExpensesPage, including the
/// current statisticExpensesModelObj
class StatisticExpensesController extends GetxController {
  StatisticExpensesController(this.statisticExpensesModelObj);

  TextEditingController editTextController = TextEditingController();

  Rx<StatisticExpensesModel> statisticExpensesModelObj;

  SelectionPopupModel? selectedDropDownValue;

  RxString isSelected = "Week".obs;

  @override
  void onClose() {
    super.onClose();
    editTextController.dispose();
  }

  onSelected(dynamic value) {
    for (var element
        in statisticExpensesModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    statisticExpensesModelObj.value.dropdownItemList.refresh();
  }
}
