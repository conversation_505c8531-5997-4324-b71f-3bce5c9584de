import '../models/frame_item_model.dart';
import '../controller/home_controller.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class FrameItemWidget extends StatelessWidget {
  FrameItemWidget(
    this.frameItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  FrameItemModel frameItemModelObj;

  var controller = Get.find<HomeController>();

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: EdgeInsets.only(right: 74.h),
        decoration: AppDecoration.fillWhiteA.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder12,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(
                top: 19.v,
                bottom: 21.v,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "lbl_razor_bank".tr,
                    style: CustomTextStyles.titleLargeBlack900_1,
                  ),
                  SizedBox(height: 37.v),
                  Text(
                    "lbl_total_balance".tr,
                    style: theme.textTheme.bodyMedium,
                  ),
                  SizedBox(height: 3.v),
                  Text(
                    "lbl_32_000".tr,
                    style: CustomTextStyles.titleSmallSemiBold,
                  ),
                ],
              ),
            ),
            Container(
              height: 82.v,
              width: 134.h,
              margin: EdgeInsets.only(
                left: 82.h,
                bottom: 58.v,
              ),
              child: Stack(
                alignment: Alignment.topLeft,
                children: [
                  Opacity(
                    opacity: 0.2,
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Container(
                        height: 82.v,
                        width: 91.h,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.42),
                          borderRadius: BorderRadius.circular(
                            45.h,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Opacity(
                    opacity: 0.1,
                    child: Align(
                      alignment: Alignment.topLeft,
                      child: Container(
                        height: 48.v,
                        width: 74.h,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary.withOpacity(0.39),
                          borderRadius: BorderRadius.circular(
                            37.h,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
