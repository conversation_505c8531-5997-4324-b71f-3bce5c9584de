import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import '../../widgets/custom_text_form_field.dart';
import '../select_bank_popup_four_screen/select_bank_popup_four_screen.dart';
import 'controller/transfer_details_three_controller.dart';

class TransferDetailsThreeScreen
    extends GetWidget<TransferDetailsThreeController> {
  const TransferDetailsThreeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
        child: Column(
          children: [
            GestureDetector(
              onTap: () {
                Get.bottomSheet(
                  SelectBankPopupFourScreen(),
                  isScrollControlled: true,
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(40.h),
                      topRight: Radius.circular(40.h),
                    ),
                  ),
                );
              },
              child: Container(
                width: double.infinity,
                decoration: AppDecoration.fillGray.copyWith(
                  borderRadius: BorderRadiusStyle.roundedBorder12,
                ),
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: 20.0.v, horizontal: 20.h),
                  child: Row(
                    children: [
                      // CustomImageView(
                      //   imagePath: ImageConstant.imgMasterCard,
                      //   height: 40.adaptSize,
                      //   width: 40.adaptSize,
                      // ),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(left: 0.0),
                          child: Text(
                            "Send from".tr,
                            style: theme.textTheme.bodyLarge,
                          ),
                        ),
                      ),
                      CustomImageView(
                        imagePath: ImageConstant.imgArrowdown,
                        height: 20.adaptSize,
                        width: 20.adaptSize,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(height: 56.v),
            CustomTextFormField(
              controller: controller.priceController,
              alignment: Alignment.center,
              hintText: "\$00.00".tr,
              textInputType: TextInputType.number,
              textInputAction: TextInputAction.done,
              hintStyle: theme.textTheme.bodyLarge!.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 28.fSize,
                color: appTheme.black900,
              ),
              textStyle: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 28.fSize,
                color: appTheme.black900,
              ),
              fillColor: Colors.transparent,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24.v),
            CustomElevatedButton(
                height: 48.v,
                width: 183.v,
                text: "lbl_add_note".tr,
                buttonStyle: CustomButtonStyles.fillGray,
                buttonTextStyle: theme.textTheme.bodyLarge!),
            SizedBox(height: 56.v),
            _buildWajihTaysirHandal(),
          ],
        ),
      ),
      bottomNavigationBar: _buildButtons(),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
          imagePath: ImageConstant.imgExpandMoreFil,
          margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
          onTap: () {
            onTapExpandMoreFIL();
          },
        ),
        title: AppbarSubtitle(
            text: "msg_transfer_details".tr,
            margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildWajihTaysirHandal() {
    return Container(
      padding: EdgeInsets.all(16.h),
      decoration: AppDecoration.outlineBlack
          .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
      child: Row(
        children: [
          CustomImageView(
              imagePath: ImageConstant.imgEllipse135556x56,
              height: 56.adaptSize,
              width: 56.adaptSize,
              radius: BorderRadius.circular(28.h)),
          Padding(
            padding: EdgeInsets.only(left: 12.h, top: 3.v),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("msg_wajih_taysir_handal".tr,
                    style: theme.textTheme.titleMedium),
                SizedBox(height: 8.v),
                Text("lbl_02_9378_5922".tr, style: theme.textTheme.bodyLarge)
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
      margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
      decoration: AppDecoration.fillWhiteA,
      child: CustomElevatedButton(
        text: "lbl_continue".tr,
        onPressed: () {
          Get.toNamed(AppRoutes.confirmThreeScreen);
        },
      ),
    );
  }

  /// Navigates to the transferScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the transferDetailsScreen when the action is triggered.
  onTapTxtPrice() {
    Get.toNamed(AppRoutes.confirmThreeScreen);
  }
}
