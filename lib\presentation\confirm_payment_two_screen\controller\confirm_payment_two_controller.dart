import '../../../core/app_export.dart';
import '../models/confirm_payment_two_model.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter/material.dart';

/// A controller class for the ConfirmPaymentTwoScreen.
///
/// This class manages the state of the ConfirmPaymentTwoScreen, including the
/// current confirmPaymentTwoModelObj
class ConfirmPaymentTwoController extends GetxController with CodeAutoFill {
  Rx<TextEditingController> otpController = TextEditingController().obs;

  Rx<ConfirmPaymentTwoModel> confirmPaymentTwoModelObj =
      ConfirmPaymentTwoModel().obs;

  @override
  void codeUpdated() {
    otpController.value.text = code ?? '';
  }

  @override
  void onInit() {
    super.onInit();
    listenForCode();
  }
}
