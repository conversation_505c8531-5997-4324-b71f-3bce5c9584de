import '../../../core/app_export.dart';
import '../models/contry_of_residence_model.dart';
import '../models/contryofresidence_item_model.dart';

/// A controller class for the ContryOfResidenceScreen.
///
/// This class manages the state of the ContryOfResidenceScreen, including the
/// current contryOfResidenceModelObj
class ContryOfResidenceController extends GetxController {
  Rx<ContryOfResidenceModel> contryOfResidenceModelObj =
      ContryOfResidenceModel().obs;

  Rx<String> radioGroup = "".obs;

  Rx<String> radioGroup1 = "".obs;

  List<ContryofresidenceItemModel> countryOfResidenceList =
      ContryOfResidenceModel.contryOfResidenceItemList();

  RxInt selected = 0.obs;
}
