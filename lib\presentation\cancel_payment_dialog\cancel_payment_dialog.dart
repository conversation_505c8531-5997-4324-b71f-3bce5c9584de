import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/cancel_payment_controller.dart';

// ignore_for_file: must_be_immutable
class CancelPaymentDialog extends StatelessWidget {
  CancelPaymentDialog(this.controller, {Key? key}) : super(key: key);

  CancelPaymentController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
        width: 388.h,
        padding: EdgeInsets.symmetric(horizontal: 24.h, vertical: 32.v),
        decoration: AppDecoration.fillWhiteA
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          Container(
              height: 116.adaptSize,
              width: 116.adaptSize,
              padding: EdgeInsets.all(34.h),
              decoration: AppDecoration.fillRed
                  .copyWith(borderRadius: BorderRadiusStyle.circleBorder58),
              child: CustomImageView(
                  imagePath: ImageConstant.imgClose,
                  height: 47.adaptSize,
                  width: 47.adaptSize,
                  alignment: Alignment.center,
                  onTap: () {
                    onTapImgClose();
                  })),
          SizedBox(height: 18.v),
          Text("lbl_opps".tr, style: CustomTextStyles.titleLargeBlack900),
          SizedBox(height: 7.v),
          Container(
              width: 301.h,
              margin: EdgeInsets.symmetric(horizontal: 19.h),
              child: Text("msg_something_went_wrong".tr,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
          SizedBox(height: 37.v),
          CustomElevatedButton(text: "lbl_back_to_home".tr)
        ]));
  }

  /// Navigates to the previous screen.
  onTapImgClose() {
    Get.back();
  }
}
