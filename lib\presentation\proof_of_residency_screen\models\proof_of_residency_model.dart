import '../../../core/app_export.dart';import 'proofofresidency_item_model.dart';/// This class defines the variables used in the [proof_of_residency_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class ProofOfResidencyModel {Rx<List<ProofofresidencyItemModel>> proofofresidencyItemList = Rx([ProofofresidencyItemModel(passport:ImageConstant.imgContrast.obs,passport1: "Passport".obs,issuedInIndia: "Issued in india".obs),ProofofresidencyItemModel(passport:ImageConstant.imgContrastWhiteA700.obs,passport1: "Identity card".obs,issuedInIndia: "Issued in india".obs),ProofofresidencyItemModel(passport:ImageConstant.imgContrast.obs,passport1: "My info digital document".obs,issuedInIndia: "Issued in india".obs)]);

 }
