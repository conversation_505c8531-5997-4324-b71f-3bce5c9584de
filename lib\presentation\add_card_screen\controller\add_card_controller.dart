import '../../../core/app_export.dart';
import '../models/add_card_model.dart';
import 'package:flutter/material.dart';

/// A controller class for the AddCardScreen.
///
/// This class manages the state of the AddCardScreen, including the
/// current addCardModelObj
class AddCardController extends GetxController {
  TextEditingController cardNumberController = TextEditingController();

  TextEditingController dateController = TextEditingController();

  TextEditingController cvvController = TextEditingController();

  TextEditingController nameController = TextEditingController();

  Rx<AddCardModel> addCardModelObj = AddCardModel().obs;

  Rx<bool> isSelectedSwitch = false.obs;

  @override
  void onClose() {
    super.onClose();
    cardNumberController.dispose();
    dateController.dispose();
    cvvController.dispose();
    nameController.dispose();
  }

  void clearText() {
    cardNumberController = TextEditingController(text: "");
    dateController = TextEditingController(text: "");
    cvvController = TextEditingController(text: "");
    nameController = TextEditingController(text: "");
  }
}
