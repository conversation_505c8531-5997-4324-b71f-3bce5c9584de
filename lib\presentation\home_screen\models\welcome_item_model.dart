import '../../../core/app_export.dart';

/// This class is used in the [welcome_item_widget] screen.
class WelcomeItemModel {
  WelcomeItemModel({
    this.welcome,
    this.johnAbram,
    this.bellSixtyOne,
    this.id,
  }) {
    welcome = welcome ?? Rx("Welcome");
    johnAbram = johnAbram ?? Rx("John abram");
    bellSixtyOne = bellSixtyOne ?? Rx(ImageConstant.imgBell61);
    id = id ?? Rx("");
  }

  Rx<String>? welcome;

  Rx<String>? johnAbram;

  Rx<String>? bellSixtyOne;

  Rx<String>? id;
}
