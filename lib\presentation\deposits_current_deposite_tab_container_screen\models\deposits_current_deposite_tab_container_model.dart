/// This class defines the variables used in the [deposits_current_deposite_tab_container_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class DepositsCurrentMoneyTabContainerModel {
  String title;
  String price;
  String subPrice;

  DepositsCurrentMoneyTabContainerModel(
    this.title,
    this.price,
    this.subPrice,
  );
}

class DepositsCurrentDepositsTabContainerModel {
  String percentage;
  String price;
  String dateAndTime;

  DepositsCurrentDepositsTabContainerModel(
    this.percentage,
    this.price,
    this.dateAndTime,
  );
}
