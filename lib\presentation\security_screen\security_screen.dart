import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:payway/widgets/custom_switch.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/security_controller.dart';

// ignore_for_file: must_be_immutable
class SecurityScreen extends GetWidget<SecurityController> {
  SecurityScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: SizedBox(
        width: SizeUtils.width,
        child: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
            child: Column(
              children: [
                _buildFaceViewfinderOne(),
                SizedBox(height: 24.v),
                _buildBellSixtyOne(),
                SizedBox(height: 24.v),
                GestureDetector(
                  onTap: () {
                    Get.toNamed(
                      AppRoutes.changePasswordScreen,
                    );
                  },
                  child: Container(
                    padding: EdgeInsets.all(8.h),
                    decoration: AppDecoration.outlineBlack.copyWith(
                        borderRadius: BorderRadiusStyle.roundedBorder12),
                    child: Row(
                      children: [
                        CustomIconButton(
                            height: 48.adaptSize,
                            width: 48.adaptSize,
                            padding: EdgeInsets.all(12.h),
                            decoration: IconButtonStyleHelper.fillIndigoTL12,
                            child: CustomImageView(
                                imagePath: ImageConstant.imgLock21)),
                        Expanded(
                          child: Padding(
                              padding: EdgeInsets.only(
                                  left: 12.h, top: 13.v, bottom: 13.v),
                              child: Text("Change password".tr,
                                  style: CustomTextStyles.bodyLargeBlack900)),
                        ),
                        CustomImageView(
                          imagePath: ImageConstant.arrowRightGray,
                          height: 24.adaptSize,
                          width: 24.adaptSize,
                          margin: EdgeInsets.only(
                            top: 12.v,
                            right: 8.h,
                            bottom: 11.v,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 5.v),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_security".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildFaceViewfinderOne() {
    return Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(children: [
          CustomIconButton(
              height: 48.adaptSize,
              width: 48.adaptSize,
              padding: EdgeInsets.all(12.h),
              decoration: IconButtonStyleHelper.fillIndigoTL12,
              child:
                  CustomImageView(imagePath: ImageConstant.imgFaceViewfinder1)),
          Expanded(
            child: Padding(
                padding: EdgeInsets.only(left: 12.h, top: 13.v, bottom: 13.v),
                child: Text("lbl_face_id".tr,
                    style: CustomTextStyles.bodyLargeBlack900)),
          ),
          Obx(() => CustomSwitch(
              margin: EdgeInsets.only(top: 8.v, right: 8.h, bottom: 9.v),
              value: controller.isSelectedSwitch.value,
              onChange: (value) {
                controller.isSelectedSwitch.value = value;
              }))
        ]));
  }

  /// Section Widget
  Widget _buildBellSixtyOne() {
    return Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(children: [
          CustomIconButton(
              height: 48.adaptSize,
              width: 48.adaptSize,
              padding: EdgeInsets.all(12.h),
              decoration: IconButtonStyleHelper.fillIndigoTL12,
              child:
                  CustomImageView(imagePath: ImageConstant.imgBell61Primary)),
          Expanded(
            child: Padding(
                padding: EdgeInsets.only(left: 12.h, top: 13.v, bottom: 13.v),
                child: Text("lbl_notifications".tr,
                    style: CustomTextStyles.bodyLargeBlack900)),
          ),
          Obx(() => CustomSwitch(
              margin: EdgeInsets.only(top: 8.v, right: 8.h, bottom: 9.v),
              value: controller.isSelectedSwitch1.value,
              onChange: (value) {
                controller.isSelectedSwitch1.value = value;
              }))
        ]));
  }

  /// Navigates to the profileScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }
}
