// ignore_for_file: must_be_immutable

import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/feedback_one_controller.dart';

class FeedbackOneDialog extends StatelessWidget {
  FeedbackOneDialog(
    this.controller, {
    Key? key,
  }) : super(
          key: key,
        );

  FeedbackOneController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 388.h,
      padding: EdgeInsets.symmetric(
        horizontal: 24.h,
        vertical: 32.v,
      ),
      decoration: AppDecoration.fillWhiteA.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder12,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomImageView(
              imagePath: ImageConstant.passwordChanged,
              height: 116.v,
              width: 116.h,
              fit: BoxFit.contain,
              alignment: Alignment.center),
          <PERSON>zed<PERSON><PERSON>(height: 19.v),
          Text(
            "msg_your_feedback_submitted".tr,
            style: CustomTextStyles.titleLargeBlack900,
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 9.v),
          SizedBox(
            width: 226.h,
            child: Text(
              "msg_your_review_has".tr,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyLarge!.copyWith(
                height: 1.29,
              ),
            ),
          ),
          SizedBox(height: 37.v),
          CustomElevatedButton(
            text: "lbl_done".tr,
            onPressed: () {
              Get.back();
            },
          ),
        ],
      ),
    );
  }
}
