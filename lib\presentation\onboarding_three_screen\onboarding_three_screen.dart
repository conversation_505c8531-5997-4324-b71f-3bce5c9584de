import 'package:carousel_slider/carousel_slider.dart';import 'widgets/seventyfive_item_widget.dart';import 'models/seventyfive_item_model.dart';import 'package:smooth_page_indicator/smooth_page_indicator.dart';import 'package:payway/widgets/custom_elevated_button.dart';import 'package:flutter/material.dart';import 'package:payway/core/app_export.dart';import 'controller/onboarding_three_controller.dart';class OnboardingThreeScreen extends GetWidget<OnboardingThreeController> {const OnboardingThreeScreen({Key? key}) : super(key: key);

@override Widget build(BuildContext context) { return SafeArea(child: Scaffold(body: Container(width: double.maxFinite, padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 24.v), child: Column(children: [CustomImageView(imagePath: ImageConstant.imgRectangle346242561, height: 474.v, width: 388.h, radius: BorderRadius.circular(12.h)), <PERSON><PERSON><PERSON><PERSON>(height: 40.v), _buildOnboardingTitle(), <PERSON><PERSON><PERSON><PERSON>(height: 48.v), CustomElevatedButton(text: "lbl_get_started".tr, onPressed: () {onTapGetStarted();}), SizedBox(height: 5.v)])))); } 
/// Section Widget
Widget _buildOnboardingTitle() { return Padding(padding: EdgeInsets.symmetric(horizontal: 13.h), child: Column(children: [Obx(() => CarouselSlider.builder(options: CarouselOptions(height: 101.v, initialPage: 0, autoPlay: true, viewportFraction: 1.0, enableInfiniteScroll: false, scrollDirection: Axis.horizontal, onPageChanged: (index, reason) {controller.sliderIndex.value = index;}), itemCount: controller.onboardingThreeModelObj.value.seventyfiveItemList.value.length, itemBuilder: (context, index, realIndex) {SeventyfiveItemModel model = controller.onboardingThreeModelObj.value.seventyfiveItemList.value[index]; return SeventyfiveItemWidget(model);})), SizedBox(height: 47.v), Obx(() => SizedBox(height: 8.v, child: AnimatedSmoothIndicator(activeIndex: controller.sliderIndex.value, count: controller.onboardingThreeModelObj.value.seventyfiveItemList.value.length, axisDirection: Axis.horizontal, effect: ScrollingDotsEffect(spacing: 8, activeDotColor: theme.colorScheme.primary, dotColor: theme.colorScheme.primary.withOpacity(0.5), dotHeight: 8.v, dotWidth: 8.h))))])); } 
/// Navigates to the loginScreen when the action is triggered.
onTapGetStarted() { Get.toNamed(AppRoutes.loginScreen, ); } 
 }
