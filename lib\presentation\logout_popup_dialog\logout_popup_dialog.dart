import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import '../../widgets/custom_bottom_bar.dart';
import 'controller/logout_popup_controller.dart';

// ignore_for_file: must_be_immutable
class LogoutPopupDialog extends StatelessWidget {
  LogoutPopupDialog(this.controller, {Key? key}) : super(key: key);

  LogoutPopupController controller;

  CustomBottomBarController customBottomBarController =
      Get.put(CustomBottomBarController());

  @override
  Widget build(BuildContext context) {
    return _buildPopup();
  }

  /// Section Widget
  Widget _buildPopup() {
    return Card(
      clipBehavior: Clip.antiAlias,
      elevation: 0,
      margin: EdgeInsets.all(0),
      color: appTheme.whiteA700,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadiusStyle.roundedBorder12),
      child: Container(
        height: 218.v,
        width: 388.h,
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.fillWhiteA
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Stack(
          alignment: Alignment.topRight,
          children: [
            Align(
              alignment: Alignment.center,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.h),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text("lbl_logout".tr,
                        style: CustomTextStyles.titleLargeBlack900),
                    SizedBox(height: 8.v),
                    Text("msg_are_you_sure_you".tr,
                        style: theme.textTheme.bodyLarge),
                    SizedBox(height: 38.v),
                    CustomElevatedButton(
                      text: "lbl_yes_logout".tr,
                      onPressed: () {
                        PrefUtils.setLogin(true);
                        customBottomBarController.getIndex(0);
                        Get.toNamed(
                          AppRoutes.loginScreen,
                        );
                      },
                    )
                  ],
                ),
              ),
            ),
            CustomImageView(
              imagePath: ImageConstant.imgCloseFill0Wgh,
              height: 24.adaptSize,
              width: 24.adaptSize,
              alignment: Alignment.topRight,
              onTap: () {
                onTapImgCloseFILLWgh();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Navigates to the myProfileScreen when the action is triggered.
  onTapImgCloseFILLWgh() {
    Get.back();
  }
}
