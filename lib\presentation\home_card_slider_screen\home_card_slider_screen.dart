import 'package:carousel_slider/carousel_slider.dart';
import 'models/card_item_model.dart';
import 'models/categories_model.dart';
import 'widgets/homecardslider_item_widget.dart';
import 'models/homecardslider_item_model.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/home_card_slider_controller.dart';

class HomeCardSliderScreen extends StatefulWidget {
  const HomeCardSliderScreen({Key? key}) : super(key: key);

  @override
  State<HomeCardSliderScreen> createState() => _HomeCardSliderScreenState();
}

class _HomeCardSliderScreenState extends State<HomeCardSliderScreen> {
  HomeCardSliderController controller = Get.put(HomeCardSliderController());

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildWelcome(),
        Sized<PERSON>ox(height: 24.v),
        Expanded(
          child: <PERSON>View(
            padding: EdgeInsets.zero,
            children: [
              GetBuilder<HomeCardSliderController>(
                init: HomeCardSliderController(),
                builder: (categoriesController) => GridView.builder(
                  primary: false,
                  shrinkWrap: true,
                  itemCount: categoriesController.categoryList.length > 4
                      ? 4
                      : categoriesController.categoryList.length,
                  padding: EdgeInsets.only(top: 0.v, left: 20.h, right: 20.h),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    mainAxisExtent: 114,
                    crossAxisCount: 4,
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                  ),
                  itemBuilder: (context, index) {
                    CategorieModel model =
                        categoriesController.categoryList[index];

                    return GestureDetector(
                      onTap: model.action,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.h,
                          vertical: 8.v,
                        ),
                        decoration: AppDecoration.outlineBlack.copyWith(
                          borderRadius: BorderRadiusStyle.roundedBorder12,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomImageView(
                              imagePath: model.image,
                              height: 48.v,
                              width: 48.v,
                            ),
                            SizedBox(
                              height: 12.v,
                            ),
                            Text(
                              model.title!,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.left,
                              style: theme.textTheme.titleMedium!.copyWith(
                                  fontSize: 17.fSize,
                                  fontWeight: FontWeight.w400),
                            )
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              SizedBox(height: 26.v),
              _buildTransactions(),
              SizedBox(height: 16.v),
              _buildHomeCardSlider(),
              SizedBox(height: 120.v),
            ],
          ),
        )
      ],
    );
  }

  /// Section Widget
  Widget _buildWelcome() {
    return Container(
      height: 320.v,
      width: double.infinity,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 60.v, left: 20.h, right: 20.h),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("Welcome".tr, style: CustomTextStyles.homeWelcome),
                      Text(
                        "John abram".tr,
                        style: theme.textTheme.bodyLarge!.copyWith(
                          color: appTheme.whiteA700,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Get.toNamed(
                      AppRoutes.notificationScreen,
                    );
                  },
                  child: Container(
                    height: 48.v,
                    width: 48.h,
                    decoration: BoxDecoration(
                      color: appTheme.whiteA700.withOpacity(0.35),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: CustomImageView(
                        width: 24.v,
                        height: 24.v,
                        imagePath: ImageConstant.imgBell61,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          Container(
            child: CarouselSlider.builder(
              itemCount: controller.slider.length,
              itemBuilder: (context, index, realIndex) {
                SliderItemModel data = controller.slider[index];
                return Padding(
                  padding: EdgeInsets.only(left: 8, right: 8, top: 24.v),
                  child: Container(
                    height: 140.v,
                    width: 334.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.h),
                      image: DecorationImage(
                        fit: BoxFit.fill,
                        image: AssetImage(data.image!),
                      ),
                    ),
                    child: Padding(
                      padding: EdgeInsets.only(left: index == 2 ? 0.0.h : 18.h),
                      child: Column(
                        crossAxisAlignment: index == 2
                            ? CrossAxisAlignment.center
                            : CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            data.title!,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: theme.textTheme.titleMedium,
                          ),
                          SizedBox(height: index == 2 ? 11.v : 16.v),
                          GestureDetector(
                            onTap: () {
                              if (index == 2) {
                                Get.toNamed(
                                  AppRoutes.addCardOneScreen,
                                );
                              } else {
                                print("jhfjhfhfjukh------");
                              }
                            },
                            child: Text(
                              index == 2 ? "Add new card" : "Total balance",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: index == 2
                                  ? theme.textTheme.titleSmall!.copyWith(
                                      fontWeight: FontWeight.w600,
                                    )
                                  : theme.textTheme.bodyMedium,
                            ),
                          ),
                          Text(
                            index == 2 ? '' : "\$32.000",
                            style: theme.textTheme.titleSmall!.copyWith(
                              fontWeight: index == 2
                                  ? FontWeight.w400
                                  : FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
              options: CarouselOptions(
                  onPageChanged: (index, reason) {
                    controller.sliderIndex.value = index;
                    controller.update();
                  },
                  height: 150.v,
                  autoPlay: true,
                  scrollDirection: Axis.horizontal,
                  initialPage: 1,
                  viewportFraction: 0.8),
            ),
          ),
          SizedBox(height: 16.v),
          Obx(() {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                controller.slider.length,
                (index) {
                  return AnimatedContainer(
                    margin: EdgeInsets.only(left: 4.h, right: 4.h),
                    duration: const Duration(milliseconds: 300),
                    height: 8.h,
                    width: (index == controller.sliderIndex.value) ? 24.v : 8.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.h),
                      color: (index == controller.sliderIndex.value)
                          ? appTheme.whiteA700
                          : appTheme.whiteA700.withOpacity(0.46),
                    ),
                  );
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildTransactions() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text("lbl_transactions".tr,
              style: CustomTextStyles.titleLargeBlack900_1),
          GestureDetector(
            onTap: () {
              onTapTxtViewAll();
            },
            child: Text(
              "lbl_view_all".tr,
              style: theme.textTheme.bodyLarge,
            ),
          ),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildHomeCardSlider() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.h),
      child: Obx(
        () => ListView.separated(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: EdgeInsets.only(bottom: 30),
          separatorBuilder: (context, index) {
            return SizedBox(height: 16.v);
          },
          itemCount: controller
              .homeCardSliderModelObj.value.homecardsliderItemList.value.length,
          itemBuilder: (context, index) {
            HomecardsliderItemModel model = controller.homeCardSliderModelObj
                .value.homecardsliderItemList.value[index];
            return HomecardsliderItemWidget(
              model,
              onTapTransactions: () {
                onTapTransactions();
              },
            );
          },
        ),
      ),
    );
  }

  /// Common widget
  // Widget _buildTwentyEight({
  //   required String icon,
  //   required String statistic,
  //   Function? onTapIcon1,
  // }) {
  //   return Container(
  //       padding: EdgeInsets.symmetric(horizontal: 18.h, vertical: 22.v),
  //       decoration: AppDecoration.fillWhiteA,
  //       child: Column(mainAxisSize: MainAxisSize.min, children: [
  //         CustomImageView(
  //             imagePath: icon,
  //             height: 24.adaptSize,
  //             width: 24.adaptSize,
  //             onTap: () {
  //               onTapIcon1!.call();
  //             }),
  //         Padding(
  //             padding: EdgeInsets.only(top: 9.v),
  //             child: Text(statistic,
  //                 style: theme.textTheme.bodyMedium!
  //                     .copyWith(color: appTheme.gray700)))
  //       ]));
  // }

  /// Navigates to the notificationEmptyScreen when the action is triggered.
  onTapBtnBellSixtyOne() {
    Get.toNamed(
      AppRoutes.notificationEmptyScreen,
    );
  }

  /// Navigates to the addCardScreen when the action is triggered.
  onTapTxtAddNewCard() {
    Get.toNamed(
      AppRoutes.addCardScreen,
    );
  }

  /// Navigates to the scanQrToRideScreen when the action is triggered.
  onTapScanOne() {
    Get.toNamed(
      AppRoutes.scanQrToRideScreen,
    );
  }

  /// Navigates to the topUpScreen when the action is triggered.
  onTapRightUpOne() {
    Get.toNamed(
      AppRoutes.topUpScreen,
    );
  }

  /// Navigates to the selectBankPopupTwoScreen when the action is triggered.
  onTapAtmCashoutOne() {
    Get.toNamed(
      AppRoutes.selectBankPopupTwoScreen,
    );
  }

  /// Navigates to the transferScreen when the action is triggered.
  onTapTwoArrowsOne() {
    Get.toNamed(
      AppRoutes.transferScreen,
    );
  }

  /// Navigates to the transactionsScreen when the action is triggered.
  onTapTxtViewAll() {
    Get.toNamed(
      AppRoutes.transactionsScreen,
    );
  }

  /// Navigates to the transactionsDetailsScreen when the action is triggered.
  onTapTransactions() {
    Get.toNamed(
      AppRoutes.transactionsDetailsScreen,
    );
  }

  /// Navigates to the depositsCurrentDepositeTabContainerScreen when the action is triggered.
  onTapIcon() {
    Get.toNamed(
      AppRoutes.depositsCurrentDepositeTabContainerScreen,
    );
  }

  /// Navigates to the statisticIncomeTabContainerScreen when the action is triggered.
  onTapIcon1() {
    Get.toNamed(
      AppRoutes.statisticIncomeTabContainerScreen,
    );
  }

  /// Navigates to the loansContainerScreen when the action is triggered.
  onTapIcon2() {
    Get.toNamed(
      AppRoutes.loansContainerScreen,
    );
  }
}

// Stack(
//         // alignment: Alignment.bottomLeft,
//         children: [
//           Align(
//             alignment: Alignment.centerLeft,
//             child: SizedBox(
//               height: 276.v,
//               // width: 704.h,
//               child: Stack(
//                 alignment: Alignment.bottomRight,
//                 children: [
//                   CarouselSlider.builder(
//                     options: CarouselOptions(
//                         height: 276.v,
//                         initialPage: 0,
//                         autoPlay: true,
//                         viewportFraction: 1.0,
//                         enableInfiniteScroll: false,
//                         scrollDirection: Axis.horizontal,
//                         onPageChanged: (index, reason) {
//                           controller.sliderIndex.value = index;
//                         }),
//                     itemCount: controller.homeCardSliderModelObj.value
//                         .welcome1ItemList.value.length,
//                     itemBuilder: (context, index, realIndex) {
//                       Welcome1ItemModel model = controller
//                           .homeCardSliderModelObj
//                           .value
//                           .welcome1ItemList
//                           .value[index];
//                       return Welcome1ItemWidget(
//                         model,
//                         onTapBtnBellSixtyOne: () {
//                           onTapBtnBellSixtyOne();
//                         },
//                       );
//                     },
//                   ),
//                   Align(
//                     alignment: Alignment.bottomRight,
//                     child: Container(
//                       height: 8.v,
//                       margin: EdgeInsets.only(right: 186.h, bottom: 16.v),
//                       child: AnimatedSmoothIndicator(
//                         activeIndex: controller.sliderIndex.value,
//                         count: controller.homeCardSliderModelObj.value
//                             .welcome1ItemList.value.length,
//                         axisDirection: Axis.horizontal,
//                         effect: ScrollingDotsEffect(
//                           spacing: 8,
//                           activeDotColor: appTheme.whiteA700,
//                           dotColor: appTheme.whiteA700.withOpacity(0.42),
//                           dotHeight: 8.v,
//                           dotWidth: 8.h,
//                         ),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//           Padding(
//             padding: EdgeInsets.only(bottom: 40.v),
//             child: CarouselSlider.builder(
//               options: CarouselOptions(
//                   height: 140.v,
//                   initialPage: 0,
//                   autoPlay: true,
//                   viewportFraction: 1.0,
//                   enableInfiniteScroll: false,
//                   scrollDirection: Axis.horizontal,
//                   onPageChanged: (index, reason) {
//                     controller.sliderIndex1.value = index;
//                   }),
//               itemCount: controller
//                   .homeCardSliderModelObj.value.cardItemList.value.length,
//               itemBuilder: (context, index, realIndex) {
//                 CardItemModel model = controller
//                     .homeCardSliderModelObj.value.cardItemList.value[index];
//                 return CardItemWidget(
//                   model,
//                   onTapTxtAddNewCard: () {
//                     onTapTxtAddNewCard();
//                   },
//                 );
//               },
//             ),
//           ),
//         ],
//       )
