import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/app_bar/appbar_trailing_image.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'package:share_plus/share_plus.dart';
import 'controller/details_controller.dart';
import 'package:payway/presentation/withdrawal_success_dialog/withdrawal_success_dialog.dart';

class DetailsScreen extends GetWidget<DetailsController> {
  const DetailsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.all(20.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("lbl_payment_with".tr,
                style: CustomTextStyles.titleLargeBlack900_1),
            SizedBox(height: 17.v),
            _buildMasterCard(),
            SizedBox(height: 24.v),
            _buildSummary(),
            SizedBox(height: 5.v),
          ],
        ),
      ),
      bottomNavigationBar: _buildButtons(),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_details".tr, margin: EdgeInsets.only(left: 16.h)),
        actions: [
          AppbarTrailingImage(
              imagePath: ImageConstant.imgShareFill0Wgh,
              margin: EdgeInsets.fromLTRB(20.h, 21.v, 20.h, 29.v),
              onTap: () {
                onTapShareFILLWgh();
              })
        ],
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildMasterCard() {
    return Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(children: [
          CustomIconButton(
              height: 55.adaptSize,
              width: 55.adaptSize,
              padding: EdgeInsets.all(7.h),
              decoration: IconButtonStyleHelper.fillGrayTL12,
              child: CustomImageView(imagePath: ImageConstant.imgMasterCard)),
          Padding(
              padding: EdgeInsets.only(left: 12.h, top: 16.v, bottom: 17.v),
              child: Text("msg_2541".tr, style: theme.textTheme.bodyLarge))
        ]));
  }

  /// Section Widget
  Widget _buildSummary() {
    return Container(
      padding: EdgeInsets.all(16.h),
      decoration: AppDecoration.outlineBlack
          .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildWithdrawAmout(
              withdrawAmout: "lbl_trasnfer_date".tr,
              price: "lbl_jan_26_2021".tr),
          SizedBox(height: 23.v),
          _buildWithdrawAmout(
              withdrawAmout: "lbl_withdraw_amout".tr, price: "lbl_100_00".tr),
          SizedBox(height: 24.v),
          _buildWithdrawAmout(
              withdrawAmout: "lbl_total".tr, price: "lbl_100_00".tr),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
      margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
      decoration: AppDecoration.fillWhiteA,
      child: CustomElevatedButton(
        text: "lbl_download_print".tr,
        onPressed: () {
          Get.toNamed(
            AppRoutes.homeScreen,
          );
        },
      ),
    );
  }

  /// Common widget
  Widget _buildWithdrawAmout({
    required String withdrawAmout,
    required String price,
  }) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Text(withdrawAmout,
          style: theme.textTheme.bodyLarge!.copyWith(color: appTheme.gray700)),
      Text(price,
          style: CustomTextStyles.bodyLargeBlack900
              .copyWith(color: appTheme.black900))
    ]);
  }

  /// Displays a dialog with the [WithdrawalSuccessDialog] content.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the sharePopupScreen when the action is triggered.
  onTapShareFILLWgh() {
    Share.share("Withdraw details..");
  }
}
