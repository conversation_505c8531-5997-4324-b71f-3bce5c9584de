import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/presentation/reaset_password_dialog/reaset_password_dialog.dart';
import 'package:payway/presentation/reaset_password_dialog/controller/reaset_password_controller.dart';

// ignore_for_file: must_be_immutable
class ResetPasswordOneScreen extends StatefulWidget {
  const ResetPasswordOneScreen({Key? key}) : super(key: key);

  @override
  State<ResetPasswordOneScreen> createState() => _ResetPasswordOneScreenState();
}

class _ResetPasswordOneScreenState extends State<ResetPasswordOneScreen> {
  ReasetPasswordController controller = Get.put(ReasetPasswordController());

  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: SizedBox(
        width: SizeUtils.width,
        child: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Form(
            key: formKey,
            child: Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 18.v),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("msg_create_your_new".tr,
                      style: CustomTextStyles.bodyLargeBlack900),
                  SizedBox(height: 39.v),
                  Obx(() => CustomTextFormField(
                        controller: controller.newPasswordController,
                        hintText: "lbl_new_password".tr,
                        hintStyle: theme.textTheme.bodyLarge!,
                        textInputType: TextInputType.visiblePassword,
                        suffix: GestureDetector(
                            onTap: () {
                              controller.isShowPassword.value =
                                  !controller.isShowPassword.value;
                            },
                            child: Container(
                                margin:
                                    EdgeInsets.fromLTRB(30.h, 18.v, 16.h, 18.v),
                                child: CustomImageView(
                                    imagePath: ImageConstant.imgEye21,
                                    height: 20.adaptSize,
                                    width: 20.adaptSize))),
                        suffixConstraints: BoxConstraints(maxHeight: 56.v),
                        validator: (value) {
                          if (value != null && value.isEmpty)
                            return 'Please enter new password.';
                          return null;
                        },
                        obscureText: controller.isShowPassword.value,
                      )),
                  SizedBox(height: 24.v),
                  Obx(() => CustomTextFormField(
                        controller: controller.confirmPasswordController,
                        hintText: "msg_confirm_password".tr,
                        hintStyle: theme.textTheme.bodyLarge!,
                        textInputAction: TextInputAction.done,
                        textInputType: TextInputType.visiblePassword,
                        suffix: GestureDetector(
                            onTap: () {
                              controller.isShowPassword1.value =
                                  !controller.isShowPassword1.value;
                            },
                            child: Container(
                                margin:
                                    EdgeInsets.fromLTRB(30.h, 18.v, 16.h, 18.v),
                                child: CustomImageView(
                                    imagePath: ImageConstant.imgEye21,
                                    height: 20.adaptSize,
                                    width: 20.adaptSize))),
                        suffixConstraints: BoxConstraints(maxHeight: 56.v),
                        validator: (value) {
                          if (value != null && value.isEmpty)
                            return 'Please enter confirm password.';
                          if (value != controller.newPasswordController.text)
                            return 'Please check your password';
                          return null;
                        },
                        obscureText: controller.isShowPassword1.value,
                      )),
                  SizedBox(height: 48.v),
                  CustomElevatedButton(
                      text: "lbl_reset_password".tr,
                      onPressed: () {
                        if (formKey.currentState!.validate()) {
                          onTapResetPassword();
                        }
                      }),
                  SizedBox(height: 5.v),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_reset_password".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Navigates to the verifyCodeScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Displays a dialog with the [ReasetPasswordDialog] content.
  onTapResetPassword() {
    controller.clearText();

    Get.dialog(AlertDialog(
      backgroundColor: Colors.transparent,
      contentPadding: EdgeInsets.zero,
      insetPadding: const EdgeInsets.only(left: 0),
      content: ReasetPasswordDialog(),
    ));
  }
}
