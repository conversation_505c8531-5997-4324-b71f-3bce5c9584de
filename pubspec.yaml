name: kojapay
description: A fintech payment app.
version: 1.0.0+1
environment:
  sdk: '>=3.1.0 <4.0.0'
  flutter: '>=3.24.0'
dependencies:
  flutter:
    sdk: flutter
  get: ^4.6.6
  connectivity_plus: ^6.1.0
  shared_preferences: ^2.3.3
  cached_network_image: ^3.4.1
  flutter_svg: ^2.0.16
  pin_code_fields: ^8.0.1
  carousel_slider: ^5.0.0
  smooth_page_indicator: ^1.2.0+3
  sms_autofill: ^2.4.0
  flutter_svg_provider: ^1.0.7
  intl: ^0.20.1
  pinput: ^5.0.0
  flutter_staggered_animations: ^1.1.1
  share_plus: ^10.1.1
  syncfusion_flutter_charts: ^28.1.33
  google_maps_flutter: ^2.10.0
  geolocator: ^13.0.2
  geocoding: ^3.0.0
  image_picker: ^1.1.2
  file_picker: ^8.1.4
  permission_handler: ^11.3.1
  url_launcher: ^6.3.1
  webview_flutter: ^4.10.0
  flutter_stripe: ^11.2.0
  # Additional useful dependencies for fintech apps
  crypto: ^3.0.6
  http: ^1.2.2
  dio: ^5.7.0
  local_auth: ^2.3.0
  device_info_plus: ^10.1.2
  package_info_plus: ^8.1.0
  path_provider: ^2.1.5
  sqflite: ^2.4.1
  flutter_secure_storage: ^9.2.2
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/images/
  fonts:
    - family: SF Pro Display
      fonts:
        - asset: assets/fonts/SFProDisplayBold.ttf
          weight: 700
        - asset: assets/fonts/SFProDisplayRegular.ttf
          weight: 400
        - asset: assets/fonts/SFProDisplayMedium.ttf
          weight: 500
        - asset: assets/fonts/SFProDisplaySemibold.ttf
          weight: 600
    - family: Satoshi
      fonts:
        - asset: assets/fonts/SatoshiRegular.ttf
          weight: 400
