import 'package:payway/presentation/deposits_current_deposite_tab_container_screen/controller/deposits_current_deposite_tab_container_controller.dart';
import 'package:get/get.dart';

/// A binding class for the DepositsCurrentDepositeTabContainerScreen.
///
/// This class ensures that the DepositsCurrentDepositeTabContainerController is created when the
/// DepositsCurrentDepositeTabContainerScreen is first loaded.
class DepositsCurrentDepositeTabContainerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => DepositsCurrentDepositeTabContainerController());
  }
}
