import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import '../../data/data_file.dart';
import '../select_bank_popup_three_screen/select_bank_popup_three_screen.dart';
import '../top_up_screen/controller/top_up_controller.dart';
import '../top_up_screen/models/top_up_model.dart';
import '../top_up_screen/widgets/frame1_item_widget.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/repay_controller.dart';

class RepayScreen extends GetWidget<RepayController> {
  RepayScreen({Key? key}) : super(key: key);

  final List<PriceModel> model = DataFile.priceList;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _buildAppBar(),
        body: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 19.h, vertical: 16.v),
            child: Column(children: [
              // CustomDropDown(
              //     icon: Container(
              //         margin: EdgeInsets.fromLTRB(30.h, 18.v, 16.h, 18.v),
              //         child: CustomImageView(
              //             imagePath: ImageConstant.imgArrowdown,
              //             height: 20.adaptSize,
              //             width: 20.adaptSize)),
              //     hintText: "lbl_select_card".tr,
              //     items: controller.repayModelObj.value.dropdownItemList.value),

              GestureDetector(
                onTap: () {
                  Get.bottomSheet(
                    SelectBankPopupThreeScreen(),
                    isScrollControlled: true,
                    backgroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(40.h),
                        topRight: Radius.circular(40.h),
                      ),
                    ),
                  );
                },
                child: Container(
                  width: double.infinity,
                  height: 56.v,
                  decoration: AppDecoration.fillGray.copyWith(
                    borderRadius: BorderRadiusStyle.roundedBorder12,
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                        vertical: 12.0.v, horizontal: 16.h),
                    child: Row(
                      children: [
                        // CustomImageView(
                        //   imagePath: ImageConstant.imgMasterCard,
                        //   height: 40.adaptSize,
                        //   width: 40.adaptSize,
                        // ),
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(left: 0.0),
                            child: Text(
                              "Send from".tr,
                              style: theme.textTheme.bodyLarge,
                            ),
                          ),
                        ),
                        CustomImageView(
                          imagePath: ImageConstant.imgArrowdown,
                          height: 20.adaptSize,
                          width: 20.adaptSize,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              SizedBox(height: 56.v),
              GetBuilder<TopUpController>(
                  init: TopUpController(),
                  builder: (controller) {
                    return Text(controller.selectedPrice,
                        style: theme.textTheme.headlineMedium);
                  }),
              SizedBox(height: 24.v),
              GetBuilder<TopUpController>(
                init: TopUpController(),
                builder: (controller) {
                  return GridView.builder(
                    shrinkWrap: true,
                    itemCount: model.length,
                    physics: NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      mainAxisExtent: 53.h,
                      crossAxisCount: 3,
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                    ),
                    itemBuilder: (context, index) {
                      PriceModel list = model[index];
                      return GestureDetector(
                          onTap: () {
                            controller.isSelectPrice = index;
                            controller.selectedPrice = list.title;
                            controller.update();
                          },
                          child: Frame1ItemWidget(
                            color: controller.isSelectPrice == index
                                ? theme.colorScheme.primary
                                : Colors.transparent,
                            bgColor: controller.isSelectPrice == index
                                ? theme.colorScheme.primary.withOpacity(0.15)
                                : appTheme.textfeild,
                            text: list.title,
                          ));
                    },
                  );
                },
              ),
            ])),
        bottomNavigationBar: _buildButtons());
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_repay".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
        margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
        decoration: AppDecoration.fillWhiteA,
        child: CustomElevatedButton(
            text: "lbl_continue".tr,
            onPressed: () {
              onTapContinue();
            }));
  }

  /// Navigates to the loansContainerScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the selectBankPopupFourScreen when the action is triggered.
  onTapContinue() {
    Get.toNamed(
      AppRoutes.confirmFourScreen,
    );
  }
}
