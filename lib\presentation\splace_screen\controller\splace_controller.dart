import '../../../core/app_export.dart';
import '../models/splace_model.dart';

/// A controller class for the SplaceScreen.
///
/// This class manages the state of the SplaceScreen, including the
/// current splaceModelObj
class SplaceController extends GetxController {
  Rx<SplaceModel> splaceModelObj = SplaceModel().obs;

  @override
  void onReady() {
    Future.delayed(const Duration(seconds: 10), () {
      Get.offNamed(
        AppRoutes.homeScreen,
      );
    });
  }
}
