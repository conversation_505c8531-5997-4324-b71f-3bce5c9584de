import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/app_bar/appbar_trailing_image.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/my_profile_controller.dart';

// ignore_for_file: must_be_immutable
class MyProfileScreen extends GetWidget<MyProfileController> {
  MyProfileScreen({Key? key}) : super(key: key);

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: SizedBox(
        width: SizeUtils.width,
        child: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Form(
            key: _formKey,
            child: Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 24.v),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(children: [
                    CustomImageView(
                        imagePath: ImageConstant.imgAvtar1,
                        height: 80.adaptSize,
                        width: 80.adaptSize),
                    Padding(
                        padding: EdgeInsets.only(
                            left: 15.h, top: 15.v, bottom: 15.v),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("lbl_john_abram2".tr,
                                  style: theme.textTheme.titleMedium),
                              SizedBox(height: 6.v),
                              Text("msg_johnabram_gmail_com".tr,
                                  style: theme.textTheme.bodyLarge)
                            ]))
                  ]),
                  SizedBox(height: 40.v),
                  _buildLastName(
                      lastName: "lbl_first_name".tr, abram: "lbl_john".tr),
                  SizedBox(height: 16.v),
                  _buildLastName(
                      lastName: "lbl_last_name".tr, abram: "lbl_abram".tr),
                  SizedBox(height: 16.v),
                  _buildEmailName(
                      email: "Email address".tr,
                      emailAddress: "<EMAIL>".tr),
                  SizedBox(height: 5.v)
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_my_profile".tr, margin: EdgeInsets.only(left: 16.h)),
        actions: [
          AppbarTrailingImage(
              imagePath: ImageConstant.edit,
              margin: EdgeInsets.fromLTRB(20.h, 21.v, 20.h, 29.v),
              onTap: () {
                onTapPencilEleven();
              })
        ],
        styleType: Style.bgFill);
  }

  /// Common widget
  Widget _buildLastName({
    required String lastName,
    required String abram,
  }) {
    return Container(
        width: 388.h,
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(lastName,
                  style: theme.textTheme.bodyLarge!
                      .copyWith(color: appTheme.gray700)),
              SizedBox(height: 8.v),
              Text(abram,
                  style: theme.textTheme.titleMedium!
                      .copyWith(color: appTheme.black900))
            ]));
  }

  Widget _buildEmailName({
    required String email,
    required String emailAddress,
  }) {
    return Container(
        width: 388.h,
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(email,
                  style: theme.textTheme.bodyLarge!
                      .copyWith(color: appTheme.gray700)),
              SizedBox(height: 8.v),
              Text(emailAddress,
                  style: theme.textTheme.titleMedium!
                      .copyWith(color: appTheme.black900))
            ]));
  }

  /// Navigates to the profileScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the editProfileScreen when the action is triggered.
  onTapPencilEleven() {
    Get.toNamed(
      AppRoutes.editProfileScreen,
    );
  }
}
