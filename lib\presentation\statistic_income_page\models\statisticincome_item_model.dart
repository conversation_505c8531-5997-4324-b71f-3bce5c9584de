import '../../../core/app_export.dart';

/// This class is used in the [statisticincome_item_widget] screen.
class StatisticincomeItemModel {
  StatisticincomeItemModel({
    this.restlessness,
    this.time,
    this.price,
    this.id,
    this.image,
  }) {
    restlessness = restlessness ?? Rx("Restlessness");
    time = time ?? Rx("4 January, 4:00 Am");
    price = price ?? Rx("\$200.00");
    image = image ?? Rx(ImageConstant.imgCallReceivedF);
    id = id ?? Rx("");
  }

  Rx<String>? restlessness;

  Rx<String>? time;

  Rx<String>? price;

  Rx<String>? id;
  Rx<String>? image;
}
