@echo off
echo ========================================
echo KojaPay Flutter App - Complete Setup
echo Following Official Documentation
echo ========================================
echo.

echo [STEP 1] Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter from https://flutter.dev/docs/get-started/install
    echo.
    echo Required versions:
    echo - Flutter: 3.10.6+ (Recommended: 3.24.0+)
    echo - Dart: 2.19.5+
    echo - Android Studio: Giraffe 2022.3.1+
    pause
    exit /b 1
)

echo.
echo [STEP 2] Running Flutter Doctor...
flutter doctor
echo.

echo [STEP 3] Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo WARNING: Clean command had issues, continuing...
)

echo.
echo [STEP 4] Getting dependencies (as per documentation)...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to get dependencies
    echo.
    echo Troubleshooting steps:
    echo 1. Check internet connection
    echo 2. Run: flutter pub cache repair
    echo 3. Delete pubspec.lock and try again
    pause
    exit /b 1
)

echo.
echo [STEP 5] Checking Android configuration...
echo Verifying android/local.properties...
if exist "android\local.properties" (
    echo ✓ local.properties found
    type "android\local.properties"
) else (
    echo ✗ local.properties not found - this may cause build issues
)

echo.
echo [STEP 6] Build options available:
echo ========================================
echo 1. Debug APK (for testing)
echo 2. Release APK (for distribution)
echo 3. App Bundle (for Play Store)
echo 4. Run on device/emulator
echo 5. Open in Android Studio
echo 6. iOS Setup (requires macOS)
echo ========================================
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" (
    echo.
    echo Building Debug APK...
    flutter build apk --debug
    if %errorlevel% equ 0 (
        echo.
        echo ✓ Debug APK built successfully!
        echo Location: build\app\outputs\flutter-apk\app-debug.apk
        echo Size: 
        dir "build\app\outputs\flutter-apk\app-debug.apk" | find "app-debug.apk"
    )
) else if "%choice%"=="2" (
    echo.
    echo Building Release APK...
    flutter build apk --release --split-per-abi
    if %errorlevel% equ 0 (
        echo.
        echo ✓ Release APK built successfully!
        echo Location: build\app\outputs\flutter-apk\
        echo Files created:
        dir "build\app\outputs\flutter-apk\" | find ".apk"
    )
) else if "%choice%"=="3" (
    echo.
    echo Building App Bundle for Play Store...
    flutter build appbundle --release
    if %errorlevel% equ 0 (
        echo.
        echo ✓ App Bundle built successfully!
        echo Location: build\app\outputs\bundle\release\app-release.aab
        echo Size:
        dir "build\app\outputs\bundle\release\app-release.aab" | find "app-release.aab"
    )
) else if "%choice%"=="4" (
    echo.
    echo Checking connected devices...
    flutter devices
    echo.
    echo Running app on connected device...
    flutter run
) else if "%choice%"=="5" (
    echo.
    echo Opening project in Android Studio...
    echo.
    echo Manual steps:
    echo 1. Open Android Studio
    echo 2. File → Open
    echo 3. Select this project folder: %CD%
    echo 4. Wait for indexing to complete
    echo 5. Click Run button or use Shift+F10
    echo.
    echo Alternative: Tools → Flutter → Open Android module in Android Studio
    pause
) else if "%choice%"=="6" (
    echo.
    echo iOS Setup (requires macOS)...
    echo.
    echo Steps for iOS:
    echo 1. cd ios
    echo 2. pod install
    echo 3. Open ios/Runner.xcworkspace in Xcode
    echo 4. Select your development team
    echo 5. Run the project
    echo.
    echo Note: iOS development requires macOS and Xcode
    pause
) else (
    echo Invalid choice. Please run the script again.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo 📱 KojaPay Features Updated:
echo ✓ Blue theme (#1231B8) applied
echo ✓ New KojaPay logo added
echo ✓ All dependencies updated to latest versions
echo ✓ Android build configuration modernized
echo ✓ Security features added (biometric auth, secure storage)
echo.
echo 🔧 Troubleshooting:
echo - If build fails: flutter clean && flutter pub get
echo - For Gradle issues: Open android folder in Android Studio
echo - For dependency conflicts: flutter pub deps
echo.
echo 📚 Documentation: See index.html for complete guide
echo 🆘 Support: <EMAIL>
echo.
pause
