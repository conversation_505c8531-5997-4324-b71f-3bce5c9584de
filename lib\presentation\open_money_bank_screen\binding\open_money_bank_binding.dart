import 'package:payway/presentation/open_money_bank_screen/controller/open_money_bank_controller.dart';
import 'package:get/get.dart';

/// A binding class for the OpenMoneyBankScreen.
///
/// This class ensures that the OpenMoneyBankController is created when the
/// OpenMoneyBankScreen is first loaded.
class OpenMoneyBankBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => OpenMoneyBankController());
  }
}
