import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/presentation/deposits_current_money_page/deposits_current_money_page.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import '../../widgets/custom_elevated_button.dart';
import '../../widgets/custom_outlined_button.dart';
import '../deposits_current_deposite_page/deposits_current_deposite_page.dart';
import 'controller/deposits_current_deposite_tab_container_controller.dart';

class DepositsCurrentDepositeTabContainerScreen extends StatefulWidget {
  const DepositsCurrentDepositeTabContainerScreen({Key? key})
      : super(
          key: key,
        );

  @override
  State<DepositsCurrentDepositeTabContainerScreen> createState() =>
      _DepositsCurrentDepositeTabContainerScreenState();
}

class _DepositsCurrentDepositeTabContainerScreenState
    extends State<DepositsCurrentDepositeTabContainerScreen> {
  DepositsCurrentDepositeTabContainerController controller =
      Get.put(DepositsCurrentDepositeTabContainerController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 16.v),
              Container(
                height: 29.v,
                width: 339.h,
                margin: EdgeInsets.only(left: 27.h, bottom: 16),
                child: TabBar(
                  unselectedLabelStyle: theme.textTheme.bodyLarge,
                  labelStyle: theme.textTheme.titleMedium!
                      .copyWith(color: theme.colorScheme.primary),
                  indicatorWeight: 4,
                  indicatorSize: TabBarIndicatorSize.tab,
                  labelPadding: EdgeInsets.zero,
                  labelColor: theme.colorScheme.primary,
                  controller: controller.tabviewController,
                  tabs: [
                    Tab(
                      child: Text(
                        "msg_current_deposits".tr,
                      ),
                    ),
                    Tab(
                      child: Text(
                        "lbl_current_money".tr,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: controller.tabviewController,
                  children: [
                    DepositsCurrentDepositePage(),
                    DepositsCurrentMoneyPage(),
                  ],
                ),
              ),
            ],
          ),
          _buildButtons(),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildButtons() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: double.maxFinite,
        margin: EdgeInsets.only(bottom: 80.v, top: 20.v),
        color: appTheme.whiteA700,
        padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 20.v),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildMoneyBank(),
            _buildDeposite(),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildMoneyBank() {
    return Expanded(
        child: CustomOutlinedButton(
            text: "lbl_money_bank".tr,
            margin: EdgeInsets.only(right: 8.h, bottom: 16.v),
            buttonStyle: CustomButtonStyles.outlinePrimary,
            onPressed: () {
              onTapMoneyBank();
            }));
  }

  /// Section Widget
  Widget _buildDeposite() {
    return Expanded(
        child: CustomElevatedButton(
            text: "lbl_deposite".tr,
            margin: EdgeInsets.only(left: 8.h, bottom: 46.v),
            onPressed: () {
              onTapDeposite();
            }));
  }

  /// Navigates to the openMoneyBankScreen when the action is triggered.
  onTapMoneyBank() {
    Get.toNamed(
      AppRoutes.openMoneyBankScreen,
    );
  }

  /// Navigates to the openDepositsScreen when the action is triggered.
  onTapDeposite() {
    Get.toNamed(
      AppRoutes.openDepositsScreen,
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      title: AppbarSubtitle(
        text: "lbl_deposits".tr,
        margin: EdgeInsets.only(left: 20.h),
      ),
    );
  }
}
