import '../../../core/app_export.dart';
import '../../home_card_slider_screen/models/card_item_model.dart';
import 'welcome_item_model.dart';
import 'frame_item_model.dart';
import 'home_item_model.dart';

/// This class defines the variables used in the [home_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class HomeModel {
  Rx<List<WelcomeItemModel>> welcomeItemList = Rx([
    WelcomeItemModel(
        welcome: "Welcome".obs,
        johnAbram: "<PERSON> abram".obs,
        bellSixtyOne: ImageConstant.imgBell61.obs)
  ]);

  Rx<List<FrameItemModel>> frameItemList =
      Rx(List.generate(1, (index) => FrameItemModel()));

  Rx<List<HomeItemModel>> homeItemList = Rx([
    HomeItemModel(
        financialtransaction: "Financial transaction".obs,
        duration: "Today, 1:20 Pm".obs,
        price: "52.00".obs),
    HomeItemModel(
        financialtransaction: "Withdraw ATM".obs,
        duration: "4 January, 4:00 Am".obs,
        price: "100.00".obs)
  ]);

  static List<SliderItemModel> slidercarItemList() {
    return [
      SliderItemModel(ImageConstant.homeBanner, "Razor bank"),
      SliderItemModel(ImageConstant.homeBanner, "Razor bank"),
      SliderItemModel(ImageConstant.homeBannerSecond, "No cards saved!"),
    ];
  }
}
