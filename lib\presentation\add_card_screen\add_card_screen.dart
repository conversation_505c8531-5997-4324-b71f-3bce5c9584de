import 'package:flutter/services.dart';
import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:payway/widgets/custom_switch.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/add_card_controller.dart';

// ignore_for_file: must_be_immutable
class AddCardScreen extends GetWidget<AddCardController> {
  AddCardScreen({Key? key}) : super(key: key);

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: SizedBox(
        width: SizeUtils.width,
        child: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Form(
            key: _formKey,
            child: Container(
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
              child: Column(
                children: [
                  _buildCardNumber(),
                  SizedBox(height: 24.v),
                  _buildFrame(),
                  SizedBox(height: 24.v),
                  _buildName(),
                  SizedBox(height: 16.v),
                  _buildDefultCard(),
                  SizedBox(height: 48.v),
                  _buildAdd(),
                  SizedBox(height: 5.v),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_add_card".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildCardNumber() {
    return CustomTextFormField(
      controller: controller.cardNumberController,
      hintText: "lbl_card_number".tr,
      hintStyle: theme.textTheme.bodyLarge!,
      textInputType: TextInputType.number,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter card number";
        }
        return null;
      },
    );
  }

  /// Section Widget
  Widget _buildDate() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(right: 8.h),
        child: CustomTextFormField(
          controller: controller.dateController,
          hintText: "lbl_expiray_date".tr,
          hintStyle: theme.textTheme.bodyLarge!,
          textInputType: TextInputType.datetime,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(04),
            CardMonthInputFormatter(),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return "Please enter Expiry date";
            }
            return null;
          },
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildCvv() {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(left: 8.h),
        child: CustomTextFormField(
          controller: controller.cvvController,
          hintText: "lbl_cvv".tr,
          hintStyle: theme.textTheme.bodyLarge!,
          textInputType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(3),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return "Please enter CVV code";
            }
            return null;
          },
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildFrame() {
    return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [_buildDate(), _buildCvv()]);
  }

  /// Section Widget
  Widget _buildName() {
    return CustomTextFormField(
      controller: controller.nameController,
      hintText: "msg_card_holder_name".tr,
      hintStyle: theme.textTheme.bodyLarge!,
      textInputAction: TextInputAction.done,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return "Please enter Card holder name";
        }
        return null;
      },
    );
  }

  /// Section Widget
  Widget _buildDefultCard() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
            padding: EdgeInsets.symmetric(vertical: 4.v),
            child: Text("lbl_default_card".tr,
                style: theme.textTheme.titleMedium)),
        Obx(
          () => CustomSwitch(
            value: controller.isSelectedSwitch.value,
            onChange: (value) {
              controller.isSelectedSwitch.value = value;
            },
          ),
        ),
      ],
    );
  }

  /// Section Widget
  Widget _buildAdd() {
    return CustomElevatedButton(
      text: "lbl_add".tr,
      onPressed: () {
        if (_formKey.currentState!.validate()) {
          onTapAdd();
        }
      },
    );
  }

  /// Navigates to the homeCardSliderScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the confirmPaymentTwoScreen when the action is triggered.
  onTapAdd() {
    controller.clearText();
    Get.toNamed(
      AppRoutes.confirmPaymentTwoScreen,
    );
  }
}

class CardMonthInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    var newText = newValue.text;
    if (newValue.selection.baseOffset == 0) {
      return newValue;
    }
    var buffer = StringBuffer();
    for (int i = 0; i < newText.length; i++) {
      buffer.write(newText[i]);
      var nonZeroIndex = i + 1;
      if (nonZeroIndex % 2 == 0 && nonZeroIndex != newText.length) {
        buffer.write('/');
      }
    }
    var string = buffer.toString();
    return newValue.copyWith(
        text: string,
        selection: TextSelection.collapsed(offset: string.length));
  }
}
