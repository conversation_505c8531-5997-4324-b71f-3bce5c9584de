import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:payway/core/app_export.dart';
import 'controller/business_verification_controller.dart';

class BusinessVerificationScreen extends GetWidget<BusinessVerificationController> {
  const BusinessVerificationScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Business Verification',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildCACSection(),
            SizedBox(height: 24.h),
            _buildTINSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildAddressSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildBusinessDetailsSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildLogoSection(),
            <PERSON><PERSON>Box(height: 32.h),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.verified, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'Business Verification Required',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              'Please provide the required business documents and information for verification. This process helps ensure secure transactions.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCACSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'CAC Registration',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 8.h),
            Text(
              'Corporate Affairs Commission (CAC) registration is required for business verification.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.cacNumberController,
              decoration: InputDecoration(
                labelText: 'CAC Registration Number',
                hintText: 'e.g., RC123456',
                prefixIcon: Icon(Icons.business),
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => controller.uploadCACDocument(),
                    icon: Icon(Icons.upload_file),
                    label: Text('Upload CAC Certificate'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => controller.verifyCAC(),
                    icon: Icon(Icons.verified),
                    label: Text('Verify CAC'),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Obx(() => controller.cacVerified.value
              ? Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 16.w),
                    SizedBox(width: 8.w),
                    Text(
                      'CAC verified successfully',
                      style: TextStyle(color: Colors.green, fontSize: 12.fSize),
                    ),
                  ],
                )
              : controller.cacVerifying.value
                ? Row(
                    children: [
                      SizedBox(
                        width: 16.w,
                        height: 16.w,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Verifying CAC...',
                        style: TextStyle(color: Colors.blue, fontSize: 12.fSize),
                      ),
                    ],
                  )
                : SizedBox()),
          ],
        ),
      ),
    );
  }

  Widget _buildTINSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tax Identification Number (TIN)',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 8.h),
            Text(
              'Provide your business tax identification number for verification.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.tinNumberController,
              decoration: InputDecoration(
                labelText: 'TIN Number',
                hintText: 'e.g., *********-0001',
                prefixIcon: Icon(Icons.receipt),
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => controller.uploadTINDocument(),
                    icon: Icon(Icons.upload_file),
                    label: Text('Upload Tax Certificate'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => controller.verifyTIN(),
                    icon: Icon(Icons.verified),
                    label: Text('Verify TIN'),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Obx(() => controller.tinVerified.value
              ? Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 16.w),
                    SizedBox(width: 8.w),
                    Text(
                      'TIN verified successfully',
                      style: TextStyle(color: Colors.green, fontSize: 12.fSize),
                    ),
                  ],
                )
              : controller.tinVerifying.value
                ? Row(
                    children: [
                      SizedBox(
                        width: 16.w,
                        height: 16.w,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Verifying TIN...',
                        style: TextStyle(color: Colors.blue, fontSize: 12.fSize),
                      ),
                    ],
                  )
                : SizedBox()),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Business Address Verification',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.addressController,
              decoration: InputDecoration(
                labelText: 'Business Address',
                prefixIcon: Icon(Icons.location_on),
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller.cityController,
                    decoration: InputDecoration(
                      labelText: 'City',
                      prefixIcon: Icon(Icons.location_city),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: TextField(
                    controller: controller.stateController,
                    decoration: InputDecoration(
                      labelText: 'State',
                      prefixIcon: Icon(Icons.map),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller.postalCodeController,
                    decoration: InputDecoration(
                      labelText: 'Postal Code',
                      prefixIcon: Icon(Icons.markunread_mailbox),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => controller.verifyAddress(),
                    icon: Icon(Icons.verified),
                    label: Text('Verify Address'),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Obx(() => controller.addressVerified.value
              ? Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 16.w),
                    SizedBox(width: 8.w),
                    Text(
                      'Address verified successfully',
                      style: TextStyle(color: Colors.green, fontSize: 12.fSize),
                    ),
                  ],
                )
              : controller.addressVerifying.value
                ? Row(
                    children: [
                      SizedBox(
                        width: 16.w,
                        height: 16.w,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'Verifying address...',
                        style: TextStyle(color: Colors.blue, fontSize: 12.fSize),
                      ),
                    ],
                  )
                : SizedBox()),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessDetailsSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Business Contact Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.businessPhoneController,
              decoration: InputDecoration(
                labelText: 'Business Phone Number',
                prefixIcon: Icon(Icons.phone),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.businessEmailController,
              decoration: InputDecoration(
                labelText: 'Business Email',
                prefixIcon: Icon(Icons.email),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.businessTypeController,
              decoration: InputDecoration(
                labelText: 'Type of Business',
                hintText: 'e.g., Retail, Manufacturing, Service',
                prefixIcon: Icon(Icons.category),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Business Logo (Optional)',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 8.h),
            Text(
              'Upload your business logo to enhance your profile.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16.h),
            Obx(() => controller.logoPath.value.isNotEmpty
              ? Column(
                  children: [
                    Container(
                      width: 100.w,
                      height: 100.w,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8.h),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8.h),
                        child: Image.file(
                          File(controller.logoPath.value),
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.image,
                            size: 32.w,
                            color: Colors.grey[400],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'Logo uploaded successfully',
                      style: TextStyle(color: Colors.green, fontSize: 12.fSize),
                    ),
                  ],
                )
              : SizedBox()),
            SizedBox(height: 16.h),
            ElevatedButton.icon(
              onPressed: () => controller.uploadLogo(),
              icon: Icon(Icons.upload_file),
              label: Text('Upload Business Logo'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.secondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Obx(() => SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: controller.canSubmit.value ? () => controller.submitVerification() : null,
        child: controller.isLoading.value
          ? CircularProgressIndicator(color: Colors.white)
          : Text(
              'Submit for Verification',
              style: TextStyle(fontSize: 16.fSize, fontWeight: FontWeight.w600),
            ),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          backgroundColor: controller.canSubmit.value 
            ? Theme.of(context).colorScheme.primary 
            : Colors.grey,
        ),
      ),
    ));
  }
} 