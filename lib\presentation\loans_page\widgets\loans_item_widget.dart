import '../models/loans_item_model.dart';
import '../controller/loans_controller.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class LoansItemWidget extends StatelessWidget {
  LoansItemWidget(
    this.loansItemModelObj, {
    Key? key,
    this.onTapTxtRepay,
  }) : super(
          key: key,
        );

  LoansItemModel loansItemModelObj;

  var controller = Get.find<LoansController>();

  VoidCallback? onTapTxtRepay;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(right: 7.h, left: 16.v, bottom: 16.v, top: 16.h),
      decoration: AppDecoration.outlineBlack.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder12,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  loansItemModelObj.price!.value,
                  style: CustomTextStyles.titleLargeBlack900_1,
                ),
              ),
              GestureDetector(
                onTap: () {
                  onTapTxtRepay!.call();
                },
                child: Container(
                  // width: 84.h,
                  padding: EdgeInsets.only(
                      right: 20.h, left: 20.h, top: 8.v, bottom: 8.v),
                  decoration: AppDecoration.fillPrimary.copyWith(
                    borderRadius: BorderRadiusStyle.roundedBorder12,
                  ),
                  child: Center(
                    child: Text(
                      loansItemModelObj.repay!.value,
                      style: CustomTextStyles.titleMediumWhiteA700,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 14.v),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  loansItemModelObj.rate!.value,
                  style: theme.textTheme.bodyLarge,
                ),
              ),
              Text(
                loansItemModelObj.fifteen!.value,
                style: CustomTextStyles.bodyLargeBlack900,
              ),
            ],
          ),
          SizedBox(height: 24.v),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  loansItemModelObj.perioud!.value,
                  style: theme.textTheme.bodyLarge,
                ),
              ),
              Text(
                loansItemModelObj.duration!.value,
                style: CustomTextStyles.bodyLargeBlack900,
              ),
            ],
          ),
          SizedBox(height: 24.v),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  loansItemModelObj.monthlyPayment!.value,
                  style: theme.textTheme.bodyLarge,
                ),
              ),
              Text(
                loansItemModelObj.price1!.value,
                style: CustomTextStyles.bodyLargeBlack900,
              ),
            ],
          ),
          SizedBox(height: 23.v),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  loansItemModelObj.totalPeriod!.value,
                  style: theme.textTheme.bodyLarge,
                ),
              ),
              Text(
                loansItemModelObj.price2!.value,
                style: CustomTextStyles.bodyLargeBlack900,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
