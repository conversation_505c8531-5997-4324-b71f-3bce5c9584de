import 'package:payway/presentation/loans_container_screen/controller/loans_container_controller.dart';
import 'package:get/get.dart';

/// A binding class for the LoansContainerScreen.
///
/// This class ensures that the LoansContainerController is created when the
/// LoansContainerScreen is first loaded.
class LoansContainerBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => LoansContainerController());
  }
}
