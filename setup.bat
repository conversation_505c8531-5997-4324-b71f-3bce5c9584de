@echo off
echo ========================================
echo KojaPay Flutter App Setup
echo ========================================
echo.

echo Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter from https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo.
echo Installing dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Checking Android setup...
flutter doctor
if %errorlevel% neq 0 (
    echo WARNING: Some Flutter doctor checks failed
    echo Please review the output above and fix any issues
)

echo.
echo Setting up Android project...
cd android
gradlew clean
cd ..

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo To run the app:
echo 1. Connect your device or start an emulator
echo 2. Run: flutter run
echo.
echo To open in Android Studio:
echo 1. Open Android Studio
echo 2. File -> Open
echo 3. Select this project folder
echo.
pause 