import '../../../core/app_export.dart';
import 'selectbankpopup_item_model.dart';

/// This class defines the variables used in the [select_bank_popup_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class SelectBankPopupModel {
  Rx<List<SelectbankpopupItemModel>> selectbankpopupItemList = Rx(
    [
      SelectbankpopupItemModel(
        masterCard: ImageConstant.imgMasterCard.obs,
        twoThousandFiveHundredFortyOne: "**** **** **** 2541".obs,
        price: "\$20,000.00".obs,
        image: ImageConstant.imgMasterCard.obs,
      ),
      SelectbankpopupItemModel(
        masterCard: ImageConstant.imgMasterCard.obs,
        twoThousandFiveHundredFortyOne: "**** **** **** 2564".obs,
        price: "\$10,000.00".obs,
        image: ImageConstant.imgPaypal.obs,
      ),
      SelectbankpopupItemModel(
        masterCard: ImageConstant.imgMasterCard.obs,
        twoThousandFiveHundredFortyOne: "**** **** **** 2541".obs,
        price: "\$50,000.00".obs,
        image: ImageConstant.imgStripe.obs,
      ),
    ],
  );
}
