// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

class CustomBottomBar extends StatelessWidget {
  CustomBottomBar({
    Key? key,
    this.onChanged,
  }) : super(
          key: key,
        );

  // RxInt selectedIndex = 0.obs;

  List<BottomMenuModel> bottomMenuList = [
    BottomMenuModel(
      icon: ImageConstant.homeUnSelected,
      activeIcon: ImageConstant.homeSelected,
      title: "lbl_home".tr,
      type: BottomBarEnum.Home,
    ),
    BottomMenuModel(
      icon: ImageConstant.depositsUnSelected,
      activeIcon: ImageConstant.depositsSelected,
      title: "lbl_deposits".tr,
      type: BottomBarEnum.Deposits,
    ),
    BottomMenuModel(
      icon: ImageConstant.statisticUnSelected,
      activeIcon: ImageConstant.statisticSelected,
      title: "lbl_statistic".tr,
      type: BottomBarEnum.Statistic,
    ),
    BottomMenuModel(
      icon: ImageConstant.loanUnSelected,
      activeIcon: ImageConstant.loanSelected,
      title: "lbl_loan".tr,
      type: BottomBarEnum.Loan,
    ),
    BottomMenuModel(
      icon: ImageConstant.profileUnSelected,
      activeIcon: ImageConstant.profileSelected,
      title: "lbl_profile".tr,
      type: BottomBarEnum.Profile,
    )
  ];

  Function(BottomBarEnum)? onChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 140.v,
      decoration: BoxDecoration(
        color: appTheme.whiteA700,
        boxShadow: [
          BoxShadow(
            color: appTheme.indigo8001e,
            spreadRadius: 2.h,
            blurRadius: 2.h,
            offset: Offset(
              4,
              0,
            ),
          ),
        ],
      ),
      child: GetBuilder<CustomBottomBarController>(
          init: CustomBottomBarController(),
          builder: (controller) {
            return BottomNavigationBar(
              backgroundColor: Colors.transparent,
              showSelectedLabels: false,
              showUnselectedLabels: false,
              selectedFontSize: 0,
              elevation: 0,
              currentIndex: controller.selectedIndex,
              type: BottomNavigationBarType.fixed,
              items: List.generate(bottomMenuList.length, (index) {
                return BottomNavigationBarItem(
                  icon: SizedBox(
                    // decoration: AppDecoration.fillWhiteA,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        CustomImageView(
                          imagePath: bottomMenuList[index].icon,
                          height: 24.adaptSize,
                          width: 24.adaptSize,
                          color: appTheme.gray700,
                          margin: EdgeInsets.only(top: 22.v),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            top: 9.v,
                            bottom: 22.v,
                          ),
                          child: Text(
                            bottomMenuList[index].title ?? "",
                            style: theme.textTheme.bodyMedium!.copyWith(
                              color: appTheme.gray700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  activeIcon: SizedBox(
                    // decoration: AppDecoration.fillWhiteA,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: 18.v),
                          decoration: AppDecoration.fillIndigo.copyWith(
                            borderRadius: BorderRadiusStyle.circleBorder16,
                          ),
                          child: CustomImageView(
                            imagePath: bottomMenuList[index].activeIcon,
                            height: 24.adaptSize,
                            width: 24.adaptSize,
                            color: appTheme.black900,
                            margin: EdgeInsets.fromLTRB(17.h, 4.v, 18.h, 4.v),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            top: 5.v,
                            bottom: 22.v,
                          ),
                          child: Text(
                            bottomMenuList[index].title ?? "",
                            style:
                                CustomTextStyles.bodyMediumBlack900_1.copyWith(
                              color: appTheme.black900,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  label: '',
                );
              }),
              onTap: (index) {
                // selectedIndex.value = index;
                // onChanged?.call(bottomMenuList[index].type);

                controller.getIndex(index);
              },
            );
          }),
    );
  }
}

enum BottomBarEnum {
  Home,
  Deposits,
  Statistic,
  Loan,
  Profile,
}

class BottomMenuModel {
  BottomMenuModel({
    required this.icon,
    required this.activeIcon,
    this.title,
    required this.type,
  });

  String icon;

  String activeIcon;

  String? title;

  BottomBarEnum type;
}

class DefaultWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xffffffff),
      padding: EdgeInsets.all(10),
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Please replace the respective Widget here',
              style: TextStyle(
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CustomBottomBarController extends GetxController {
  int selectedIndex = 0;

  getIndex(int index) {
    selectedIndex = index;
    update();
  }
}
