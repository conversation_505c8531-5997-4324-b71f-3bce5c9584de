import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/feedback_controller.dart';
import 'package:payway/presentation/feedback_one_dialog/feedback_one_dialog.dart';
import 'package:payway/presentation/feedback_one_dialog/controller/feedback_one_controller.dart';

class FeedbackScreen extends GetWidget<FeedbackController> {
  const FeedbackScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: SizedBox(
            width: double.maxFinite,
            child: Column(children: [
              SizedBox(height: 16.v),
              Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.h),
                  child: CustomTextFormField(
                      controller: controller.inputController,
                      hintText: "msg_write_your_feedback".tr,
                      hintStyle: theme.textTheme.bodyLarge!,
                      textInputAction: TextInputAction.done,
                      maxLines: 5)),
            ])),
        bottomNavigationBar: _buildButtons());
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_feedback".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
        margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
        decoration: AppDecoration.fillWhiteA,
        child: CustomElevatedButton(
            text: "lbl_submit".tr,
            onPressed: () {
              onTapSubmit();
            }));
  }

  /// Navigates to the settingsScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Displays a dialog with the [FeedbackOneDialog] content.
  onTapSubmit() {
    Get.dialog(AlertDialog(
      backgroundColor: Colors.transparent,
      contentPadding: EdgeInsets.zero,
      insetPadding: const EdgeInsets.only(left: 0),
      content: FeedbackOneDialog(
        Get.put(
          FeedbackOneController(),
        ),
      ),
    ));
  }
}
