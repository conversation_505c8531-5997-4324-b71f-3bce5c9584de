import 'package:flutter/material.dart';import 'package:payway/core/app_export.dart';import 'controller/share_popup_controller.dart';class SharePopupScreen extends GetWidget<SharePopupController> {const SharePopupScreen({Key? key}) : super(key: key);

@override Widget build(BuildContext context) { return SafeArea(child: Scaffold(extendBody: true, extendBodyBehindAppBar: true, body: Container(width: SizeUtils.width, height: SizeUtils.height, decoration: BoxDecoration(color: appTheme.whiteA700, image: DecorationImage(image: AssetImage(ImageConstant.imgSharePopup), fit: BoxFit.cover)), child: SizedBox(width: double.maxFinite, child: Column(mainAxisSize: MainAxisSize.min, children: [Spacer(), _buildShareSheet()]))))); } 
/// Section Widget
Widget _buildShareSheet() { return Container(padding: EdgeInsets.symmetric(vertical: 21.v), decoration: BoxDecoration(borderRadius: BorderRadiusStyle.customBorderTL12), child: Column(children: [Padding(padding: EdgeInsets.only(left: 20.h, right: 16.h), child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [Text("lbl_share".tr, style: theme.textTheme.headlineMedium), CustomImageView(imagePath: ImageConstant.imgCloseFill0Wgh, height: 24.adaptSize, width: 24.adaptSize, margin: EdgeInsets.only(top: 4.v, bottom: 5.v), onTap: () {onTapImgCloseFILLWgh();})])), SizedBox(height: 18.v), Divider(color: appTheme.whiteA700), SizedBox(height: 24.v), SingleChildScrollView(scrollDirection: Axis.horizontal, padding: EdgeInsets.only(left: 20.h), child: IntrinsicWidth(child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [Column(children: [CustomImageView(imagePath: ImageConstant.imgIcon60x60, height: 60.adaptSize, width: 60.adaptSize, radius: BorderRadius.circular(30.h)), SizedBox(height: 5.v), Text("lbl_watsapp".tr, style: CustomTextStyles.bodyLargeBlack900)]), Padding(padding: EdgeInsets.only(left: 28.h), child: Column(children: [CustomImageView(imagePath: ImageConstant.imgIcon2, height: 60.adaptSize, width: 60.adaptSize, radius: BorderRadius.circular(30.h)), SizedBox(height: 6.v), Text("lbl_instagram".tr, style: CustomTextStyles.bodyLargeBlack900)])), Container(width: 254.h, margin: EdgeInsets.only(left: 21.h), child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [Column(children: [CustomImageView(imagePath: ImageConstant.imgIcon3, height: 60.adaptSize, width: 60.adaptSize, radius: BorderRadius.circular(30.h)), SizedBox(height: 5.v), Text("lbl_snapchat".tr, style: CustomTextStyles.bodyLargeBlack900)]), Padding(padding: EdgeInsets.only(left: 23.h), child: Column(children: [CustomImageView(imagePath: ImageConstant.imgIcon4, height: 60.adaptSize, width: 60.adaptSize, radius: BorderRadius.circular(30.h)), SizedBox(height: 4.v), Text("lbl_linkedin".tr, style: CustomTextStyles.bodyLargeBlack900)])), Spacer(), Column(children: [CustomImageView(imagePath: ImageConstant.imgIcon5, height: 60.adaptSize, width: 60.adaptSize, radius: BorderRadius.circular(30.h)), SizedBox(height: 4.v), Text("lbl_facebook".tr, textAlign: TextAlign.center, style: CustomTextStyles.bodyLargeBlack900)])]))]))), SizedBox(height: 22.v), Align(alignment: Alignment.centerLeft, child: Container(margin: EdgeInsets.only(left: 20.h, right: 34.h), padding: EdgeInsets.all(11.h), decoration: AppDecoration.outlineWhiteA.copyWith(borderRadius: BorderRadiusStyle.roundedBorder12), child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [GestureDetector(onTap: () {onTapTxtHttpsDemoDsrr();}, child: Padding(padding: EdgeInsets.only(top: 5.v), child: Text("msg_https_demo_dsrr".tr, style: CustomTextStyles.bodyLargeSatoshiBlack900))), Padding(padding: EdgeInsets.only(top: 1.v, right: 2.h, bottom: 2.v), child: Text("lbl_copy".tr, style: theme.textTheme.titleLarge))]))), SizedBox(height: 26.v)])); } 
/// Navigates to the detailsOneScreen when the action is triggered.
onTapImgCloseFILLWgh() { Get.toNamed(AppRoutes.detailsOneScreen, ); } 

onTapTxtHttpsDemoDsrr() { // TODO: implement Actions
 } 
 }
