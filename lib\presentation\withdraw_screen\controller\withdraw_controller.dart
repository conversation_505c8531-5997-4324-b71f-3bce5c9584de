import 'package:flutter/cupertino.dart';

import '../../../core/app_export.dart';
import '../models/withdraw_model.dart';

/// A controller class for the WithdrawScreen.
///
/// This class manages the state of the WithdrawScreen, including the
/// current withdrawModelObj
class WithdrawController extends GetxController {
 Rx<WithdrawModel> withdrawModelObj = WithdrawModel().obs;

 SelectionPopupModel? selectedDropDownValue;

 TextEditingController priceController = TextEditingController();

 onSelected(dynamic value) {
  for (var element in withdrawModelObj.value.dropdownItemList.value) {
   element.isSelected = false;
   if (element.id == value.id) {
    element.isSelected = true;
   }
  }
  withdrawModelObj.value.dropdownItemList.refresh();
 }
}
