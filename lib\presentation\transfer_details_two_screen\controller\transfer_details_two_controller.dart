import 'package:flutter/cupertino.dart';

import '../../../core/app_export.dart';
import '../models/transfer_details_two_model.dart';

/// A controller class for the TransferDetailsTwoScreen.
///
/// This class manages the state of the TransferDetailsTwoScreen, including the
/// current transferDetailsTwoModelObj
class TransferDetailsTwoController extends GetxController {
  Rx<TransferDetailsTwoModel> transferDetailsTwoModelObj =
      TransferDetailsTwoModel().obs;

  SelectionPopupModel? selectedDropDownValue;

  TextEditingController priceController = TextEditingController();

  void clearText() {
    priceController = TextEditingController(text: "");
  }

  onSelected(dynamic value) {
    for (var element
    in transferDetailsTwoModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    transferDetailsTwoModelObj.value.dropdownItemList.refresh();
  }
}
