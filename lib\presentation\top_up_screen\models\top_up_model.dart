import 'package:payway/core/app_export.dart';
import 'frame1_item_model.dart';

/// This class defines the variables used in the [top_up_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class TopUpModel {
  Rx<List<SelectionPopupModel>> dropdownItemList = Rx([
    SelectionPopupModel(
      id: 1,
      title: "Item One",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "Item Two",
    ),
    SelectionPopupModel(
      id: 3,
      title: "Item Three",
    )
  ]);

  Rx<List<Frame1ItemModel>> frame1ItemList =
      Rx(List.generate(6, (index) => Frame1ItemModel()));
}

class PriceModel {
  final String title;

  PriceModel({
    required this.title,
  });
}
