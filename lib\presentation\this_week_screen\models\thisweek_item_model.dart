import '../../../core/app_export.dart';

/// This class is used in the [thisweek_item_widget] screen.
class ThisweekItemModel {
  ThisweekItemModel({
    this.financialtransaction,
    this.duration,
    this.price,
    this.id,
  }) {
    financialtransaction = financialtransaction ?? Rx("Financial transaction");
    duration = duration ?? Rx("Today, 1:20 Pm");
    price = price ?? Rx("52.00");
    id = id ?? Rx("");
  }

  Rx<String>? financialtransaction;

  Rx<String>? duration;

  Rx<String>? price;

  Rx<String>? id;
}
