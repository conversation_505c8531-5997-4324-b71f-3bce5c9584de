import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'widgets/loans_item_widget.dart';
import 'models/loans_item_model.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/loans_controller.dart';
import 'models/loans_model.dart';

// ignore_for_file: must_be_immutable
class LoansPage extends StatelessWidget {
  LoansPage({Key? key}) : super(key: key);

  LoansController controller = Get.put(LoansController(LoansModel().obs));

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          _buildLoans(),
          Padding(
            padding: const EdgeInsets.only(bottom: 25),
            child: CustomElevatedButton(
              width: 388.h,
              text: "lbl_new_loan".tr,
              margin: EdgeInsets.only(bottom: 120.v),
              onPressed: () {
                onTapNewLoan();
              },
              alignment: Alignment.bottomCenter,
            ),
          ),
        ],
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        title: AppbarSubtitle(
            text: "lbl_loans".tr, margin: EdgeInsets.only(left: 20.h)));
  }

  /// Section Widget
  Widget _buildLoans() {
    return Expanded(
      child: ListView.separated(
        physics: AlwaysScrollableScrollPhysics(),
        shrinkWrap: true,
        padding:
            EdgeInsets.only(top: 16.v, bottom: 220.v, right: 20.h, left: 20.h),
        separatorBuilder: (context, index) {
          return SizedBox(height: 16.v);
        },
        itemCount: controller.loansModelObj.value.loansItemList.value.length,
        itemBuilder: (context, index) {
          LoansItemModel model =
              controller.loansModelObj.value.loansItemList.value[index];
          return LoansItemWidget(
            model,
            onTapTxtRepay: () {
              onTapTxtRepay();
            },
          );
        },
      ),
    );
  }

  /// Navigates to the repayScreen when the action is triggered.
  onTapTxtRepay() {
    Get.toNamed(
      AppRoutes.repayScreen,
    );
  }

  /// Navigates to the newLoanScreen when the action is triggered.
  onTapNewLoan() {
    Get.toNamed(
      AppRoutes.newLoanScreen,
    );
  }
}
