<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4665a7fb-11e1-4be5-b117-85548d833f34" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="306VquyR1fFGF5e6mRhoKNY4rn1" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "android.gradle.sync.needed": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "com.google.services.firebase.aqiPopupShown": "true",
    "dart.analysis.tool.window.visible": "false",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "show.migrate.to.gradle.popup": "false"
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4665a7fb-11e1-4be5-b117-85548d833f34" name="Changes" comment="" />
      <created>1752948205799</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752948205799</updated>
    </task>
    <servers />
  </component>
</project>