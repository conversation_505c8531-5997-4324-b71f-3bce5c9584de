import '../../../core/app_export.dart';
import 'contryofresidence_item_model.dart';

/// This class defines the variables used in the [contry_of_residence_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class ContryOfResidenceModel {
  // Rx<List<ContryofresidenceItemModel>> contryofresidenceItemList = Rx([
  //   ContryofresidenceItemModel(
  //       england: ImageConstant.imgContrast.obs,
  //       england1: "England".obs,
  //       england2: ImageConstant.imgEngland.obs),
  //   ContryofresidenceItemModel(
  //       england: ImageConstant.imgContrast.obs,
  //       england1: "England".obs,
  //       england2: ImageConstant.imgEngland.obs),
  //   ContryofresidenceItemModel(
  //       england: ImageConstant.imgContrast.obs,
  //       england1: "England".obs,
  //       england2: ImageConstant.imgEngland.obs),
  //   ContryofresidenceItemModel(
  //       england: ImageConstant.imgContrast.obs,
  //       england1: "England".obs,
  //       england2: ImageConstant.imgEngland.obs),
  //   ContryofresidenceItemModel(
  //       england: ImageConstant.imgContrast.obs,
  //       england1: "England".obs,
  //       england2: ImageConstant.imgEngland.obs),
  // ]);

  static List<ContryofresidenceItemModel> contryOfResidenceItemList() {
    return [
      ContryofresidenceItemModel(
        ImageConstant.england,
        "England",
      ),
      ContryofresidenceItemModel(
        ImageConstant.indonesia,
        "Indonesia",
      ),
      ContryofresidenceItemModel(
        ImageConstant.germany,
        "Germany",
      ),
      ContryofresidenceItemModel(
        ImageConstant.france,
        "French",
      ),
      ContryofresidenceItemModel(
        ImageConstant.deutsch,
        "Deutsch",
      ),
      ContryofresidenceItemModel(
        ImageConstant.italian,
        "Italian",
      ),
      ContryofresidenceItemModel(
        ImageConstant.russian,
        "Russian",
      ),
    ];
  }
}
