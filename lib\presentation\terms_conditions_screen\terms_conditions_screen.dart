import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/terms_conditions_controller.dart';

class TermsConditionsScreen extends GetWidget<TermsConditionsController> {
  const TermsConditionsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _buildAppBar(),
        body: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 17.v),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text("msg_last_update_27_12_2023".tr,
                  style: theme.textTheme.bodyLarge),
              Sized<PERSON><PERSON>(height: 7.v),
              Container(
                  width: 357.h,
                  margin: EdgeInsets.only(right: 30.h),
                  child: Text("msg_please_read_these".tr,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: CustomTextStyles.bodyLargeBlack900
                          .copyWith(height: 1.29))),
              SizedBox(height: 24.v),
              Text("msg_conditions_of_uses".tr,
                  style: CustomTextStyles.titleLargeBlack900_1),
              SizedBox(height: 19.v),
              Container(
                  width: 382.h,
                  margin: EdgeInsets.only(right: 5.h),
                  child: Text("msg_it_is_a_long_established".tr,
                      maxLines: 13,
                      overflow: TextOverflow.ellipsis,
                      style: CustomTextStyles.bodyLargeBlack900
                          .copyWith(height: 1.29))),
              SizedBox(height: 5.v)
            ])));
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "msg_terms_conditions".tr,
            margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Navigates to the profileScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }
}
