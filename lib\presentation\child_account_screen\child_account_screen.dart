import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:payway/core/app_export.dart';
import 'controller/child_account_controller.dart';

class ChildAccountScreen extends GetWidget<ChildAccountController> {
  const ChildAccountScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Create Child Account',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            Sized<PERSON>ox(height: 24.h),
            _buildChildInfoSection(),
            SizedBox(height: 24.h),
            _buildParentalControlsSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildSpendingLimitsSection(),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildCategoryRestrictionsSection(),
            Sized<PERSON>ox(height: 32.h),
            _buildCreateButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.child_care, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'Child Account Setup',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              'Create a secure child account with parental controls to help your child learn financial responsibility.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChildInfoSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Child Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.childNameController,
              decoration: InputDecoration(
                labelText: 'Child\'s Full Name',
                prefixIcon: Icon(Icons.person),
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller.childEmailController,
                    decoration: InputDecoration(
                      labelText: 'Email Address',
                      prefixIcon: Icon(Icons.email),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: TextField(
                    controller: controller.childPhoneController,
                    decoration: InputDecoration(
                      labelText: 'Phone Number',
                      prefixIcon: Icon(Icons.phone),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Text(
              'Date of Birth',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            SizedBox(height: 8.h),
            Obx(() => InkWell(
              onTap: () => controller.selectDateOfBirth(),
              child: Container(
                padding: EdgeInsets.all(16.h),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8.h),
                ),
                child: Row(
                  children: [
                    Icon(Icons.calendar_today, color: Colors.grey[600]),
                    SizedBox(width: 12.w),
                    Text(
                      controller.selectedDate.value ?? 'Select Date of Birth',
                      style: TextStyle(
                        color: controller.selectedDate.value != null 
                          ? Colors.black 
                          : Colors.grey[500],
                      ),
                    ),
                    Spacer(),
                    Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            )),
            SizedBox(height: 8.h),
            if (controller.selectedDate.value != null)
              Text(
                'Age: ${controller.calculateAge()} years old',
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 12.fSize,
                  fontWeight: FontWeight.w600,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildParentalControlsSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Parental Controls',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            _buildControlSwitch(
              title: 'Spending Limits',
              description: 'Set daily and transaction spending limits',
              value: controller.spendingLimitEnabled.value,
              onChanged: (value) => controller.spendingLimitEnabled.value = value,
            ),
            SizedBox(height: 12.h),
            _buildControlSwitch(
              title: 'Category Restrictions',
              description: 'Block or allow specific spending categories',
              value: controller.categoryRestrictionsEnabled.value,
              onChanged: (value) => controller.categoryRestrictionsEnabled.value = value,
            ),
            SizedBox(height: 12.h),
            _buildControlSwitch(
              title: 'Time Restrictions',
              description: 'Limit when the account can be used',
              value: controller.timeRestrictionsEnabled.value,
              onChanged: (value) => controller.timeRestrictionsEnabled.value = value,
            ),
            SizedBox(height: 12.h),
            _buildControlSwitch(
              title: 'Location Restrictions',
              description: 'Restrict usage to specific locations',
              value: controller.locationRestrictionsEnabled.value,
              onChanged: (value) => controller.locationRestrictionsEnabled.value = value,
            ),
            SizedBox(height: 12.h),
            _buildControlSwitch(
              title: 'Transaction Notifications',
              description: 'Get notified of all child transactions',
              value: controller.notificationEnabled.value,
              onChanged: (value) => controller.notificationEnabled.value = value,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildControlSwitch({
    required String title,
    required String description,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeColor: Theme.of(context).colorScheme.primary,
        ),
      ],
    );
  }

  Widget _buildSpendingLimitsSection() {
    return Obx(() => Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Spending Limits',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 8.h),
            Text(
              'Set limits to help your child learn responsible spending.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.dailyLimitController,
              enabled: controller.spendingLimitEnabled.value,
              decoration: InputDecoration(
                labelText: 'Daily Spending Limit',
                prefixIcon: Icon(Icons.attach_money),
                suffixText: 'NGN',
              ),
              keyboardType: TextInputType.number,
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.transactionLimitController,
              enabled: controller.spendingLimitEnabled.value,
              decoration: InputDecoration(
                labelText: 'Maximum Transaction Amount',
                prefixIcon: Icon(Icons.payment),
                suffixText: 'NGN',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildCategoryRestrictionsSection() {
    return Obx(() => Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category Restrictions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 8.h),
            Text(
              'Choose which categories your child can spend on.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16.h),
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: controller.categories.map((category) {
                final isSelected = controller.selectedCategories.contains(category);
                return FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      controller.selectedCategories.add(category);
                    } else {
                      controller.selectedCategories.remove(category);
                    }
                  },
                  selectedColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  checkmarkColor: Theme.of(context).colorScheme.primary,
                );
              }).toList(),
            ),
            SizedBox(height: 16.h),
            Text(
              'Selected Categories: ${controller.selectedCategories.length}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildCreateButton() {
    return Obx(() => SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: controller.canCreate.value ? () => controller.createChildAccount() : null,
        child: controller.isLoading.value
          ? CircularProgressIndicator(color: Colors.white)
          : Text(
              'Create Child Account',
              style: TextStyle(fontSize: 16.fSize, fontWeight: FontWeight.w600),
            ),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          backgroundColor: controller.canCreate.value 
            ? Theme.of(context).colorScheme.primary 
            : Colors.grey,
        ),
      ),
    ));
  }
} 