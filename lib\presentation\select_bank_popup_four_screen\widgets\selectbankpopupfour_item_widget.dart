import '../models/selectbankpopupfour_item_model.dart';
import '../controller/select_bank_popup_four_controller.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class SelectbankpopupfourItemWidget extends StatelessWidget {
  SelectbankpopupfourItemWidget(
    this.selectbankpopupfourItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  SelectbankpopupfourItemModel selectbankpopupfourItemModelObj;

  var controller = Get.find<SelectBankPopupFourController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8.h),
      decoration: AppDecoration.outlineBlack.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder12,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 212.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Obx(
                  () => CustomIconButton(
                    height: 55.adaptSize,
                    width: 55.adaptSize,
                    padding: EdgeInsets.all(7.h),
                    decoration: IconButtonStyleHelper.fillGrayTL12,
                    child: CustomImageView(
                      imagePath:
                          selectbankpopupfourItemModelObj.masterCard!.value,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(vertical: 6.v),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Obx(
                        () => Text(
                          selectbankpopupfourItemModelObj
                              .twoThousandFiveHundredFortyOne!.value,
                          style: theme.textTheme.bodyLarge,
                        ),
                      ),
                      SizedBox(height: 3.v),
                      Obx(
                        () => Text(
                          selectbankpopupfourItemModelObj.price!.value,
                          style: theme.textTheme.titleSmall,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Obx(
            () => CustomImageView(
              imagePath: selectbankpopupfourItemModelObj.image!.value,
              height: 24.v,
              width: 25.h,
              margin: EdgeInsets.only(
                top: 16.v,
                right: 8.h,
                bottom: 15.v,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
