import 'package:payway/core/utils/validation_functions.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/sign_up_controller.dart';

// ignore_for_file: must_be_immutable
class SignUpScreen extends GetWidget<SignUpController> {
  SignUpScreen({Key? key}) : super(key: key);

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
            resizeToAvoidBottomInset: false,
            body: SizedBox(
                width: SizeUtils.width,
                child: SingleChildScrollView(
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom),
                    child: Form(
                        key: _formKey,
                        child: Container(
                            width: double.maxFinite,
                            padding: EdgeInsets.symmetric(
                                horizontal: 20.h, vertical: 24.v),
                            child: Column(children: [
                              CustomImageView(
                                  imagePath: ImageConstant.imgThumbsUp,
                                  height: 55.v,
                                  width: 65.h),
                              SizedBox(height: 20.v),
                              Text("lbl_welcome_back".tr,
                                  style: CustomTextStyles.titleLargeBlack900),
                              SizedBox(height: 49.v),
                              Align(
                                  alignment: Alignment.centerRight,
                                  child: Padding(
                                      padding: EdgeInsets.only(
                                          left: 74.h, right: 7.h),
                                      child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            GestureDetector(
                                                onTap: () {
                                                  onTapTxtOngoing();
                                                },
                                                child: Padding(
                                                    padding: EdgeInsets.only(
                                                        top: 4.v, bottom: 2.v),
                                                    child: Text("lbl_log_in".tr,
                                                        style: theme.textTheme
                                                            .bodyLarge))),
                                            Column(children: [
                                              Text("lbl_sign_up".tr,
                                                  style: CustomTextStyles
                                                      .titleMediumPrimary),
                                              SizedBox(height: 2.v),
                                              SizedBox(
                                                  width: 177.h,
                                                  child: Divider())
                                            ])
                                          ]))),
                              SizedBox(height: 40.v),
                              _buildFirstName(),
                              SizedBox(height: 24.v),
                              _buildLastName(),
                              SizedBox(height: 24.v),
                              _buildEmail(),
                              SizedBox(height: 24.v),
                              _buildPassword(),
                              SizedBox(height: 48.v),
                              _buildSignUp(),
                              SizedBox(height: 5.v)
                            ])))))));
  }

  /// Section Widget
  Widget _buildFirstName() {
    return CustomTextFormField(
        controller: controller.firstNameController,
        hintText: "lbl_first_name".tr,
        hintStyle: theme.textTheme.bodyLarge!,
        validator: (value) {
          if (!isText(value)) {
            return "err_msg_please_enter_valid_text".tr;
          }
          return null;
        });
  }

  /// Section Widget
  Widget _buildLastName() {
    return CustomTextFormField(
        controller: controller.lastNameController,
        hintText: "lbl_last_name".tr,
        hintStyle: theme.textTheme.bodyLarge!,
        validator: (value) {
          if (!isText(value)) {
            return "err_msg_please_enter_valid_text".tr;
          }
          return null;
        });
  }

  /// Section Widget
  Widget _buildEmail() {
    return CustomTextFormField(
        controller: controller.emailController,
        hintText: "lbl_email_address".tr,
        hintStyle: theme.textTheme.bodyLarge!,
        textInputType: TextInputType.emailAddress,
        validator: (value) {
          if (value == null || (!isValidEmail(value, isRequired: true))) {
            return "err_msg_please_enter_valid_email".tr;
          }
          return null;
        });
  }

  /// Section Widget
  Widget _buildPassword() {
    return Obx(() => CustomTextFormField(
        controller: controller.passwordController,
        hintText: "lbl_password".tr,
        hintStyle: theme.textTheme.bodyLarge!,
        textInputAction: TextInputAction.done,
        textInputType: TextInputType.visiblePassword,
        suffix: InkWell(
            onTap: () {
              controller.isShowPassword.value =
                  !controller.isShowPassword.value;
            },
            child: Container(
                margin: EdgeInsets.fromLTRB(30.h, 18.v, 16.h, 18.v),
                child: CustomImageView(
                    imagePath: ImageConstant.imgEye21,
                    height: 20.adaptSize,
                    width: 20.adaptSize))),
        suffixConstraints: BoxConstraints(maxHeight: 56.v),
        validator: (value) {
          if (value == null || (!isValidPassword(value, isRequired: true))) {
            return "err_msg_please_enter_valid_password".tr;
          }
          return null;
        },
        obscureText: controller.isShowPassword.value,
        contentPadding: EdgeInsets.only(left: 20.h, top: 17.v, bottom: 17.v)));
  }

  /// Section Widget
  Widget _buildSignUp() {
    return CustomElevatedButton(
        text: "lbl_sign_up".tr,
        onPressed: () {
          onTapSignUp();
        });
  }

  /// Navigates to the loginScreen when the action is triggered.
  onTapTxtOngoing() {
    Get.toNamed(
      AppRoutes.loginScreen,
    );
  }

  /// Navigates to the contryOfResidenceScreen when the action is triggered.
  onTapSignUp() {
    Get.toNamed(
      AppRoutes.contryOfResidenceScreen,
    );
  }
}
