import '../models/contryofresidence_item_model.dart';
import '../controller/contry_of_residence_controller.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class ContryofresidenceItemWidget extends StatelessWidget {
  ContryofresidenceItemWidget(
    this.contryofresidenceItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  ContryofresidenceItemModel contryofresidenceItemModelObj;

  var controller = Get.find<ContryOfResidenceController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 16.h,
        vertical: 17.v,
      ),
      decoration: AppDecoration.outlineBlack.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder12,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 96.h,
            margin: EdgeInsets.only(
              top: 3.v,
              bottom: 2.v,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomImageView(
                  imagePath: contryofresidenceItemModelObj.image,
                  height: 24.v,
                  width: 25.h,
                ),
                Padding(
                  padding: EdgeInsets.only(top: 3.v),
                  child: Text(
                    contryofresidenceItemModelObj.title!,
                    style: CustomTextStyles.bodyLargeBlack900,
                  ),
                ),
              ],
            ),
          ),
          CustomImageView(
            imagePath: contryofresidenceItemModelObj.image,
            height: 30.v,
            width: 41.h,
          ),
        ],
      ),
    );
  }
}
