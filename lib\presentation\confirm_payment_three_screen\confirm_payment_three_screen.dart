import 'package:payway/widgets/app_bar/custom_app_bar.dart';import 'package:payway/widgets/app_bar/appbar_leading_image.dart';import 'package:payway/widgets/app_bar/appbar_subtitle.dart';import 'package:payway/widgets/custom_pin_code_text_field.dart';import 'package:payway/widgets/custom_elevated_button.dart';import 'package:flutter/material.dart';import 'package:payway/core/app_export.dart';import 'controller/confirm_payment_three_controller.dart';import 'package:payway/presentation/confirm_payment_five_dialog/confirm_payment_five_dialog.dart';import 'package:payway/presentation/confirm_payment_five_dialog/controller/confirm_payment_five_controller.dart';class ConfirmPaymentThreeScreen extends GetWidget<ConfirmPaymentThreeController> {const ConfirmPaymentThreeScreen({Key? key}) : super(key: key);

@override Widget build(BuildContext context) { return SafeArea(child: Scaffold(resizeToAvoidBottomInset: false, appBar: _buildAppBar(), body: Container(width: double.maxFinite, padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 18.v), child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [Text("msg_please_enter_pin".tr, style: CustomTextStyles.bodyLargeBlack900), SizedBox(height: 38.v), Padding(padding: EdgeInsets.only(right: 32.h), child: Obx(() => CustomPinCodeTextField(context: Get.context!, controller: controller.otpController.value, onChanged: (value) {}))), SizedBox(height: 5.v)])), bottomNavigationBar: _buildButtons())); } 
/// Section Widget
PreferredSizeWidget _buildAppBar() { return CustomAppBar(leadingWidth: 52.h, leading: AppbarLeadingImage(imagePath: ImageConstant.imgExpandMoreFil, margin: EdgeInsets.only(left: 20.h, top: 17.v, bottom: 25.v), onTap: () {onTapExpandMoreFIL();}), title: AppbarSubtitle(text: "lbl_enter_your_pin".tr, margin: EdgeInsets.only(left: 16.h)), styleType: Style.bgFill); } 
/// Section Widget
Widget _buildButtons() { return Container(margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v), decoration: AppDecoration.fillWhiteA, child: CustomElevatedButton(text: "lbl_confirm_payment".tr, onPressed: () {onTapConfirmPayment();})); } 
/// Navigates to the confirmThreeScreen when the action is triggered.
onTapExpandMoreFIL() { Get.toNamed(AppRoutes.confirmThreeScreen, ); } 

/// Displays a dialog with the [ConfirmPaymentFiveDialog] content.
onTapConfirmPayment() { Get.dialog(AlertDialog(backgroundColor: Colors.transparent, contentPadding: EdgeInsets.zero, insetPadding: const EdgeInsets.only(left: 0), content:ConfirmPaymentFiveDialog(Get.put(ConfirmPaymentFiveController(),),),)); } 
 }
