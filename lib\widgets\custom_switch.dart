// ignore_for_file: must_be_immutable

import 'package:flutter/cupertino.dart';
import '../core/app_export.dart';

class CustomSwitch extends StatelessWidget {
  CustomSwitch({
    Key? key,
    required this.onChange,
    this.alignment,
    this.value,
    this.width,
    this.height,
    this.margin,
  }) : super(
          key: key,
        );

  final Alignment? alignment;

  bool? value;

  final Function(bool) onChange;

  final double? width;

  final double? height;

  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    return Container(
        height: height,
        width: width,
        margin: margin,
        child: alignment != null
            ? Align(
                alignment: alignment ?? Alignment.center,
                child: switchWidget,
              )
            : switchWidget);
  }

  Widget get switchWidget => CupertinoSwitch(
        value: value ?? false,
        trackColor:
            (value ?? false) ? theme.colorScheme.primary : appTheme.gray100,
        thumbColor: (value ?? false) ? appTheme.whiteA700 : appTheme.whiteA700,
        activeColor: theme.colorScheme.primary,
        onChanged: (value) {
          onChange(value);
        },
      );
}
