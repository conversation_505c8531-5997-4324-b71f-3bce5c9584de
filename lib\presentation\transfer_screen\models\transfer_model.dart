import '../../../core/app_export.dart';
import 'transfer_item_model.dart';

/// This class defines the variables used in the [transfer_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class TransferModel {
  Rx<List<TransferItemModel>> transferItemList = Rx([
    TransferItemModel(
        wajihTaysirHandal: ImageConstant.imgEllipse135556x56.obs,
        wajihTaysirHandal1: "Wajih Taysir Handal".obs,
        twoHundredNinetyThreeMillionSe: "(02) 9378 5922".obs),
    TransferItemModel(
        wajihTaysirHandal: ImageConstant.imgEllipse13551.obs,
        wajihTaysirHandal1: "Qusay Thabit".obs,
        twoHundredNinetyThreeMillionSe: "(02) 6727 8247".obs),
    TransferItemModel(
        wajihTaysirHandal: ImageConstant.imgEllipse13552.obs,
        wajihTaysirHandal1: "Ameer Harb".obs,
        twoHundredNinetyThreeMillionSe: "(03) 9050 5960".obs),
    TransferItemModel(
        wajihTaysirHandal: ImageConstant.imgEllipse13553.obs,
        wajihTaysirHandal1: "Dhakwan Ghanem".obs,
        twoHundredNinetyThreeMillionSe: "(03) 5398 1914".obs)
  ]);
}
