import 'package:payway/presentation/open_deposits_screen/controller/open_deposits_controller.dart';
import 'package:get/get.dart';

/// A binding class for the OpenDepositsScreen.
///
/// This class ensures that the OpenDepositsController is created when the
/// OpenDepositsScreen is first loaded.
class OpenDepositsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => OpenDepositsController());
  }
}
