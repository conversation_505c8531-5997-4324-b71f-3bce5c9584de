import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:payway/core/utils/validation_functions.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/edit_profile_controller.dart';

// ignore_for_file: must_be_immutable
class EditProfileScreen extends GetWidget<EditProfileController> {
  EditProfileScreen({Key? key}) : super(key: key);

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: SizedBox(
          width: SizeUtils.width,
          child: SingleChildScrollView(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: Form(
              key: _formKey,
              child: Container(
                width: double.maxFinite,
                padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(children: [
                      SizedBox(
                          height: 80.adaptSize,
                          width: 80.adaptSize,
                          child: Stack(
                              alignment: Alignment.bottomRight,
                              children: [
                                CustomImageView(
                                    imagePath: ImageConstant.imgEllipse225,
                                    height: 80.adaptSize,
                                    width: 80.adaptSize,
                                    radius: BorderRadius.circular(40.h),
                                    alignment: Alignment.center),
                                CustomIconButton(
                                    height: 40.adaptSize,
                                    width: 40.adaptSize,
                                    padding: EdgeInsets.all(10.h),
                                    decoration:
                                        IconButtonStyleHelper.outlineBlack,
                                    alignment: Alignment.bottomRight,
                                    child: CustomImageView(
                                        imagePath: ImageConstant.imgCamera41))
                              ])),
                      Padding(
                          padding: EdgeInsets.only(
                              left: 15.h, top: 15.v, bottom: 15.v),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text("lbl_john_abram2".tr,
                                    style: theme.textTheme.titleMedium),
                                SizedBox(height: 6.v),
                                Text("msg_johnabram_gmail_com".tr,
                                    style: theme.textTheme.bodyLarge)
                              ]))
                    ]),
                    SizedBox(height: 40.v),
                    _buildFilled(),
                    SizedBox(height: 24.v),
                    _buildFilled1(),
                    SizedBox(height: 24.v),
                    _buildEmail(),
                    SizedBox(height: 5.v),
                  ],
                ),
              ),
            ),
          ),
        ),
        bottomNavigationBar: _buildButtons());
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 17.v, bottom: 20.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_edit_profile".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildFilled() {
    return CustomTextFormField(
      controller: controller.filledController,
      hintText: "First name".tr,
      validator: (value) {
        if (!isText(value)) {
          return "err_msg_please_enter_valid_text".tr;
        }
        return null;
      },
    );
  }

  /// Section Widget
  Widget _buildFilled1() {
    return CustomTextFormField(
      controller: controller.filledController1,
      hintText: "Last name".tr,
      validator: (value) {
        if (!isText(value)) {
          return "err_msg_please_enter_valid_text".tr;
        }
        return null;
      },
    );
  }

  /// Section Widget
  Widget _buildEmail() {
    return CustomTextFormField(
        controller: controller.emailController,
        hintText: "Email address".tr,
        textInputAction: TextInputAction.done,
        textInputType: TextInputType.emailAddress,
        validator: (value) {
          if (value == null || (!isValidEmail(value, isRequired: true))) {
            return "err_msg_please_enter_valid_email".tr;
          }
          return null;
        });
  }

  /// Section Widget
  Widget _buildSave() {
    return CustomElevatedButton(
      text: "lbl_save".tr,
      onPressed: () {
        if (_formKey.currentState!.validate()) {
          onTapSave();
        }
      },
    );
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
        margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
        decoration: AppDecoration.fillWhiteA,
        child: _buildSave());
  }

  /// Navigates to the myProfileScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the myProfileScreen when the action is triggered.
  onTapSave() {
    Get.back();
  }
}
