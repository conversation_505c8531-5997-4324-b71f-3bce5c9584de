import 'package:payway/presentation/transfer_screen/controller/transfer_controller.dart';
import 'package:get/get.dart';

/// A binding class for the TransferScreen.
///
/// This class ensures that the TransferController is created when the
/// TransferScreen is first loaded.
class TransferBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => TransferController());
  }
}
