import '../core/utils/image_constant.dart';
import '../presentation/deposits_current_deposite_tab_container_screen/models/deposits_current_deposite_tab_container_model.dart';
import '../presentation/onboarding_one_screen/models/onboarding_one_model.dart';
import '../presentation/top_up_screen/models/top_up_model.dart';

class DataFile {
  static List<OnboardingModel> getAllIntroList() {
    List<OnboardingModel> list = [];
    list.add(
      OnboardingModel(
        "Make it simple",
        "Money transfer generally refers to one of the following cashless modes of payment.",
        ImageConstant.imgRectangle34624256,
      ),
    );
    list.add(
      OnboardingModel(
        "New banking",
        "Trends in this report include AI open banking  robotic process automation",
        ImageConstant.imgRectangle34624256474x388,
      ),
    );
    list.add(
      OnboardingModel(
        "Zero fees",
        "Zero fee merchant processing and no fee merchant services are merchant account",
        ImageConstant.imgRectangle346242561,
      ),
    );
    return list;
  }

  static List<DepositsCurrentMoneyTabContainerModel> getAllCurrentMoney() {
    List<DepositsCurrentMoneyTabContainerModel> list = [];
    list.add(
      DepositsCurrentMoneyTabContainerModel(
        "Laptop nothing powar",
        "\$1300.00",
        "\$250.00",
      ),
    );
    list.add(
      DepositsCurrentMoneyTabContainerModel(
        "Mobile",
        "\$2000.00",
        "\$500.00",
      ),
    );
    list.add(
      DepositsCurrentMoneyTabContainerModel(
        "Fridge",
        "\$3000.00",
        "\$400.00",
      ),
    );
    return list;
  }

  static List<PriceModel> priceList = [
    PriceModel(title: '\$100.00'),
    PriceModel(title: '\$200.00'),
    PriceModel(title: '\$300.00'),
    PriceModel(title: '\$400.00'),
    PriceModel(title: '\$500.00'),
    PriceModel(title: '\$600.00'),
  ];
}
