import '../../../core/app_export.dart';
import '../models/confirm_payment_model.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter/material.dart';

/// A controller class for the ConfirmPaymentScreen.
///
/// This class manages the state of the ConfirmPaymentScreen, including the
/// current confirmPaymentModelObj
class ConfirmPaymentController extends GetxController with CodeAutoFill {
 Rx<TextEditingController> otpController = TextEditingController().obs;

 Rx<ConfirmPaymentModel> confirmPaymentModelObj = ConfirmPaymentModel().obs;

 @override
 void codeUpdated() {
  otpController.value.text = code ?? '';
 }

 void clearText() {
  otpController = TextEditingController(text: "").obs;
 }

 @override
 void onInit() {
  super.onInit();
  listenForCode();
 }
}
