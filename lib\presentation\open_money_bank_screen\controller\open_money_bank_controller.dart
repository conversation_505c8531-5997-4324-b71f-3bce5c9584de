import '../../../core/app_export.dart';import '../models/open_money_bank_model.dart';import 'package:flutter/material.dart';/// A controller class for the OpenMoneyBankScreen.
///
/// This class manages the state of the OpenMoneyBankScreen, including the
/// current openMoneyBankModelObj
class OpenMoneyBankController extends GetxController {TextEditingController defaultController = TextEditingController();

TextEditingController defaultController1 = TextEditingController();

TextEditingController defaultController2 = TextEditingController();

Rx<OpenMoneyBankModel> openMoneyBankModelObj = OpenMoneyBankModel().obs;

Rx<String> chooseInstallment = "".obs;

@override void onClose() { super.onClose(); defaultController.dispose(); defaultController1.dispose(); defaultController2.dispose(); } 
 }
