import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/guest_profile_controller.dart';
import 'models/guest_profile_model.dart';

// ignore_for_file: must_be_immutable
class GuestProfilePage extends StatelessWidget {
  GuestProfilePage({Key? key}) : super(key: key);

  GuestProfileController controller = Get.put(
      GuestProfileController(GuestProfileModel().obs));

  @override Widget build(BuildContext context) {
    return SafeArea(child: Scaffold(resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Container(width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 24.v),
            child: Column(crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(children: [
                    CustomImageView(imagePath: ImageConstant.imgAvtar1,
                        height: 80.adaptSize,
                        width: 80.adaptSize),
                    Padding(padding: EdgeInsets.only(
                        left: 16.h, top: 16.v, bottom: 14.v),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("lbl_guest_profile".tr,
                                  style: theme.textTheme.titleMedium),
                              SizedBox(height: 5.v),
                              SizedBox(width: 130.h,
                                  child: Row(
                                      mainAxisAlignment: MainAxisAlignment
                                          .spaceBetween, children: [
                                    GestureDetector(onTap: () {
                                      onTapTxtLogIn();
                                    },
                                        child: Text("lbl_log_in".tr,
                                            style: theme.textTheme.bodyLarge)),
                                    SizedBox(height: 20.v,
                                        child: VerticalDivider(width: 1.h,
                                            thickness: 1.v,
                                            color: appTheme.gray700,
                                            endIndent: 4.h)),
                                    GestureDetector(onTap: () {
                                      onTapTxtSignUp();
                                    },
                                        child: Text("lbl_sign_up".tr,
                                            style: theme.textTheme.bodyLarge))
                                  ]))
                            ]))
                  ]),
                  SizedBox(height: 32.v),
                  _buildShieldkeyholeOne(),
                  SizedBox(height: 24.v),
                  _buildSettingsOne(),
                  SizedBox(height: 24.v),
                  _buildShieldcheckOne(),
                  SizedBox(height: 24.v),
                  _buildTermAndCondition(),
                  SizedBox(height: 5.v)
                ]))));
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(title: AppbarSubtitle(
        text: "lbl_profile".tr, margin: EdgeInsets.only(left: 20.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildShieldkeyholeOne() {
    return CustomTextFormField(
        controller: controller.shieldkeyholeOneController,
        hintText: "lbl_security".tr,
        prefix: Container(margin: EdgeInsets.fromLTRB(20.h, 20.v, 24.h, 20.v),
            child: CustomImageView(imagePath: ImageConstant.security,
                height: 24.adaptSize,
                width: 24.adaptSize)),
        prefixConstraints: BoxConstraints(maxHeight: 64.v),
        suffix: Container(margin: EdgeInsets.fromLTRB(30.h, 20.v, 16.h, 20.v),
            child: CustomImageView(imagePath: ImageConstant.arrowRightGray,
                height: 24.adaptSize,
                width: 24.adaptSize)),
        suffixConstraints: BoxConstraints(maxHeight: 64.v),
        contentPadding: EdgeInsets.symmetric(vertical: 21.v),
        borderDecoration: TextFormFieldStyleHelper.outlineBlack,
        fillColor: appTheme.whiteA700);
  }

  /// Section Widget
  Widget _buildSettingsOne() {
    return CustomTextFormField(controller: controller.settingsOneController,
        hintText: "lbl_settings".tr,
        prefix: Container(margin: EdgeInsets.fromLTRB(20.h, 20.v, 24.h, 20.v),
            child: CustomImageView(imagePath: ImageConstant.settings,
                height: 24.adaptSize,
                width: 24.adaptSize)),
        prefixConstraints: BoxConstraints(maxHeight: 64.v),
        suffix: Container(margin: EdgeInsets.fromLTRB(30.h, 20.v, 16.h, 20.v),
            child: CustomImageView(imagePath: ImageConstant.arrowRightGray,
                height: 24.adaptSize,
                width: 24.adaptSize)),
        suffixConstraints: BoxConstraints(maxHeight: 64.v),
        contentPadding: EdgeInsets.symmetric(vertical: 21.v),
        borderDecoration: TextFormFieldStyleHelper.outlineBlack,
        fillColor: appTheme.whiteA700);
  }

  /// Section Widget
  Widget _buildShieldcheckOne() {
    return CustomTextFormField(controller: controller.shieldcheckOneController,
        hintText: "lbl_privacy_policy".tr,
        prefix: Container(margin: EdgeInsets.fromLTRB(20.h, 20.v, 24.h, 20.v),
            child: CustomImageView(imagePath: ImageConstant.privacyPolicy,
                height: 24.adaptSize,
                width: 24.adaptSize)),
        prefixConstraints: BoxConstraints(maxHeight: 64.v),
        suffix: Container(margin: EdgeInsets.fromLTRB(30.h, 20.v, 16.h, 20.v),
            child: CustomImageView(imagePath: ImageConstant.arrowRightGray,
                height: 24.adaptSize,
                width: 24.adaptSize)),
        suffixConstraints: BoxConstraints(maxHeight: 64.v),
        contentPadding: EdgeInsets.symmetric(vertical: 21.v),
        borderDecoration: TextFormFieldStyleHelper.outlineBlack,
        fillColor: appTheme.whiteA700);
  }

  /// Section Widget
  Widget _buildTermAndCondition() {
    return CustomTextFormField(
        controller: controller.termAndConditionController,
        hintText: "msg_terms_conditions".tr,
        textInputAction: TextInputAction.done,
        prefix: Container(margin: EdgeInsets.fromLTRB(20.h, 20.v, 24.h, 20.v),
            child: CustomImageView(imagePath: ImageConstant.termsAndCondition,
                height: 24.adaptSize,
                width: 24.adaptSize)),
        prefixConstraints: BoxConstraints(maxHeight: 64.v),
        suffix: Container(margin: EdgeInsets.fromLTRB(30.h, 20.v, 16.h, 20.v),
            child: CustomImageView(imagePath: ImageConstant.arrowRightGray,
                height: 24.adaptSize,
                width: 24.adaptSize)),
        suffixConstraints: BoxConstraints(maxHeight: 64.v),
        contentPadding: EdgeInsets.symmetric(vertical: 21.v),
        borderDecoration: TextFormFieldStyleHelper.outlineBlack,
        fillColor: appTheme.whiteA700);
  }

  /// Navigates to the loginScreen when the action is triggered.
  onTapTxtLogIn() {
    Get.toNamed(AppRoutes.loginScreen,);
  }

  /// Navigates to the signUpScreen when the action is triggered.
  onTapTxtSignUp() {
    Get.toNamed(AppRoutes.signUpScreen,);
  }
}
