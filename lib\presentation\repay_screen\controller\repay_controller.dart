import '../../../core/app_export.dart';
import '../models/repay_model.dart';

/// A controller class for the RepayScreen.
///
/// This class manages the state of the RepayScreen, including the
/// current repayModelObj
class RepayController extends GetxController {
  Rx<RepayModel> repayModelObj = RepayModel().obs;

  SelectionPopupModel? selectedDropDownValue;

  int isSelectPrice = 0;

  String selectedPrice = "\$100.00";

  onSelected(dynamic value) {
    for (var element in repayModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    repayModelObj.value.dropdownItemList.refresh();
  }
}
