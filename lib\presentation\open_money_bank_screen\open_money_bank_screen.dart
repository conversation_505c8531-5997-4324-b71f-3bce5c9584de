import 'package:payway/presentation/money_bank_created_popup_two_dialog/controller/money_bank_created_popup_two_controller.dart';
import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:payway/widgets/custom_radio_button.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import '../money_bank_created_popup_two_dialog/money_bank_created_popup_two_dialog.dart';
import 'controller/open_money_bank_controller.dart';

class OpenMoneyBankScreen extends GetWidget<OpenMoneyBankController> {
  const OpenMoneyBankScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 24.v),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDefault(),
            SizedBox(height: 24.v),
            _buildFrame(),
            SizedBox(height: 26.v),
            Text("msg_choose_installment".tr,
                style: CustomTextStyles.titleLargeBlack900_1),
            SizedBox(height: 19.v),
            _buildChooseInstallment(),
            SizedBox(height: 48.v),
            _buildOpenMoneyBank(),
            SizedBox(height: 5.v),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_open_money_bank".tr,
            margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildDefault() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(
          AppRoutes.addCardScreen,
        );
      },
      child: Container(
        width: double.infinity,
        height: 56.h,
        decoration: AppDecoration.fillGray.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder12,
        ),
        padding:
            EdgeInsets.only(left: 16.h, top: 17.v, bottom: 17.v, right: 16.v),
        child: Row(
          children: [
            Expanded(
                child: Text("Add card".tr, style: theme.textTheme.bodyLarge)),
            CustomImageView(
              imagePath: ImageConstant.imgDbnhguieofyuike,
              height: 20.adaptSize,
              width: 20.adaptSize,
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildDefault1() {
    return Expanded(
        child: Padding(
            padding: EdgeInsets.only(right: 8.h),
            child: CustomTextFormField(
                controller: controller.defaultController1,
                hintText: "lbl_amout_achieve".tr,
                hintStyle: theme.textTheme.bodyLarge!)));
  }

  /// Section Widget
  Widget _buildDefault2() {
    return Expanded(
        child: Padding(
            padding: EdgeInsets.only(left: 8.h),
            child: CustomTextFormField(
                controller: controller.defaultController2,
                hintText: "lbl_your_goal".tr,
                hintStyle: theme.textTheme.bodyLarge!,
                textInputAction: TextInputAction.done)));
  }

  /// Section Widget
  Widget _buildFrame() {
    return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [_buildDefault1(), _buildDefault2()]);
  }

  /// Section Widget
  Widget _buildChooseInstallment() {
    return Padding(
      padding: EdgeInsets.only(right: 0.h),
      child: Obx(
        () => Column(
          children: [
            Padding(
              padding: EdgeInsets.only(right: 0.h),
              child: CustomRadioButton(
                text: "lbl_50_usd_per_day".tr,
                value:
                    controller.openMoneyBankModelObj.value.radioList.value[0],
                groupValue: controller.chooseInstallment.value,
                padding: EdgeInsets.symmetric(vertical: 1.v),
                onChange: (value) {
                  controller.chooseInstallment.value = value;
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 24.v, right: 0.h),
              child: CustomRadioButton(
                text: "msg_rounding_up_to_1".tr,
                value:
                    controller.openMoneyBankModelObj.value.radioList.value[1],
                groupValue: controller.chooseInstallment.value,
                padding: EdgeInsets.symmetric(vertical: 1.v),
                onChange: (value) {
                  controller.chooseInstallment.value = value;
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 24.v),
              child: CustomRadioButton(
                text: "msg_rounding_up_to_10".tr,
                value:
                    controller.openMoneyBankModelObj.value.radioList.value[2],
                groupValue: controller.chooseInstallment.value,
                padding: EdgeInsets.symmetric(vertical: 1.v),
                onChange: (value) {
                  controller.chooseInstallment.value = value;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildOpenMoneyBank() {
    return CustomElevatedButton(
        text: "lbl_open_money_bank".tr,
        onPressed: () {
          onTapOpenMoneyBank();
        });
  }

  /// Navigates to the depositsCurrentDepositeTabContainerScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the depositsCurrentDepositeTabContainerScreen when the action is triggered.
  onTapOpenMoneyBank() {
    Get.dialog(AlertDialog(
      backgroundColor: Colors.transparent,
      contentPadding: EdgeInsets.zero,
      insetPadding: const EdgeInsets.only(left: 0),
      content: MoneyBankCreatedPopupTwoDialog(
        Get.put(
          MoneyBankCreatedPopupTwoController(),
        ),
      ),
    ));
  }
}
