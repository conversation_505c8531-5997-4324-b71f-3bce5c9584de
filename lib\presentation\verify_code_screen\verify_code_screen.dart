import 'package:payway/presentation/verify_code_screen/controller/verify_code_controller.dart';
import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'package:pinput/pinput.dart';

// ignore_for_file: must_be_immutable
class VerifyCodeScreen extends StatefulWidget {
  const VerifyCodeScreen({Key? key}) : super(key: key);

  @override
  State<VerifyCodeScreen> createState() => _VerifyCodeScreenState();
}

class _VerifyCodeScreenState extends State<VerifyCodeScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  VerifyCodeController controller = Get.put(VerifyCodeController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 19.h, vertical: 17.v),
            child: Form(
              key: formKey,
              child: Column(children: [
                Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                        width: 286.h,
                        margin: EdgeInsets.only(right: 102.h),
                        child: RichText(
                            text: TextSpan(children: [
                              TextSpan(
                                  text: "msg_the_confimation2".tr,
                                  style: CustomTextStyles.bodyLargeff000000),
                              TextSpan(
                                  text: "msg_johnabram_gmail_com".tr,
                                  style: CustomTextStyles.bodyLargeff5486e9)
                            ]),
                            textAlign: TextAlign.left))),
                SizedBox(height: 38.v),
                Pinput(
                  errorText: "Please enter OTP",
                  errorTextStyle: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 16.fSize,
                    color: appTheme.error,
                  ),
                  textInputAction: TextInputAction.done,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  disabledPinTheme: PinTheme(
                      textStyle: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 24.fSize,
                        color: appTheme.black900,
                      ),
                      padding: EdgeInsets.only(left: 9.h, right: 9.h),
                      decoration: BoxDecoration(color: appTheme.error)),
                  controller: controller.otpController,
                  length: 6,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Please enter a valid code";
                    }
                    return null;
                  },
                  errorPinTheme: PinTheme(
                    padding: EdgeInsets.only(left: 8.5.h, right: 8.5.h),
                    decoration: BoxDecoration(
                      color: appTheme.textfeild,
                      // border: Border.all(color: appTheme.textfeild),
                      borderRadius: BorderRadius.circular(16.h),
                    ),
                    textStyle: TextStyle(
                      color: appTheme.error,
                      fontSize: 14.fSize,
                      fontWeight: FontWeight.w400,
                    ),
                    width: 52.h,
                    height: 52.h,
                  ),
                  defaultPinTheme: PinTheme(
                    padding: EdgeInsets.only(left: 8.5.h, right: 8.5.h),
                    width: 52.h,
                    height: 52.h,
                    textStyle: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 24.fSize,
                      color: appTheme.black900,
                    ),
                    decoration: BoxDecoration(
                      color: appTheme.textfeild,
                      borderRadius: BorderRadius.circular(16.h),
                    ),
                  ),
                ),
                SizedBox(height: 42.v),
                Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                  Text("msg_don_t_get_the_code".tr,
                      style: theme.textTheme.bodyLarge),
                  Padding(
                      padding: EdgeInsets.only(left: 5.h, bottom: 2.v),
                      child: Text("lbl_resend_code".tr,
                          style: theme.textTheme.titleSmall))
                ]),
                SizedBox(height: 47.v),
                CustomElevatedButton(
                    text: "lbl_verify_now".tr,
                    onPressed: () {
                      if (formKey.currentState!.validate()) {
                        onTapVerifyNow();
                      }
                    }),
                SizedBox(height: 5.v)
              ]),
            )));
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_verify_code".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Navigates to the forgotPasswordScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the reasetPasswordOneScreen when the action is triggered.
  onTapVerifyNow() {
    Get.toNamed(
      AppRoutes.reasetPasswordOneScreen,
    );
    controller.clearText();
  }
}
