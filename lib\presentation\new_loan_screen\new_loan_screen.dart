// ignore_for_file: must_be_immutable

import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_drop_down.dart';
import 'package:payway/widgets/custom_text_form_field.dart';
import 'package:payway/widgets/custom_switch.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/new_loan_controller.dart';
import 'package:payway/presentation/new_loan_success_popup_dialog/new_loan_success_popup_dialog.dart';
import 'package:payway/presentation/new_loan_success_popup_dialog/controller/new_loan_success_popup_controller.dart';

class NewLoanScreen extends GetWidget<NewLoanController> {
  NewLoanScreen({Key? key}) : super(key: key);

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              CustomDropDown(
                  icon: Container(
                      margin: EdgeInsets.fromLTRB(0.h, 0.v, 16.h, 0.v),
                      child: CustomImageView(
                          imagePath: ImageConstant.imgArrowdown,
                          height: 20.adaptSize,
                          width: 20.adaptSize)),
                  hintText: "lbl_select_currency".tr,
                  items:
                      controller.newLoanModelObj.value.dropdownItemList.value),
              SizedBox(height: 24.v),
              CustomDropDown(
                  icon: Container(
                      margin: EdgeInsets.fromLTRB(0.h, 0.v, 16.h, 0.v),
                      child: CustomImageView(
                        imagePath: ImageConstant.imgArrowdown,
                        height: 20.adaptSize,
                        width: 20.adaptSize,
                      )),
                  hintText: "msg_select_deposite".tr,
                  items:
                      controller.newLoanModelObj.value.dropdownItemList1.value),
              SizedBox(height: 24.v),
              CustomTextFormField(
                controller: controller.defaultController,
                hintText: "lbl_enter_amout".tr,
                textInputType: TextInputType.number,
                hintStyle: theme.textTheme.bodyLarge!,
              ),
              SizedBox(height: 24.v),
              CustomTextFormField(
                controller: controller.defaultController1,
                hintText: "msg_enter_monthly_repayment".tr,
                hintStyle: theme.textTheme.bodyLarge!,
                textInputType: TextInputType.number,
                textInputAction: TextInputAction.done,
              ),
              SizedBox(height: 24.v),
              _buildFrame(),
              SizedBox(height: 48.v),
              CustomElevatedButton(
                text: "lbl_open_new_loan".tr,
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    onTapOpenNewLoan();
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_new_loan".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildFrame() {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Padding(
          padding: EdgeInsets.only(top: 5.v, bottom: 3.v),
          child: Text("msg_enter_monthly_repayment".tr,
              style: theme.textTheme.titleMedium)),
      Obx(() => CustomSwitch(
          value: controller.isSelectedSwitch.value,
          onChange: (value) {
            controller.isSelectedSwitch.value = value;
          }))
    ]);
  }

  /// Navigates to the loansContainerScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.toNamed(
      AppRoutes.loansContainerScreen,
    );
  }

  /// Displays a dialog with the [NewLoanSuccessPopupDialog] content.
  onTapOpenNewLoan() {
    Get.dialog(AlertDialog(
      backgroundColor: Colors.transparent,
      contentPadding: EdgeInsets.zero,
      insetPadding: const EdgeInsets.only(left: 0),
      content: NewLoanSuccessPopupDialog(
        Get.put(
          NewLoanSuccessPopupController(),
        ),
      ),
    ));
  }
}
