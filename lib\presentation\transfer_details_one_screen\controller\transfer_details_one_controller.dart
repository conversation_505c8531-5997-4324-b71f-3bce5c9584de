import '../../../core/app_export.dart';import '../models/transfer_details_one_model.dart';/// A controller class for the TransferDetailsOneScreen.
///
/// This class manages the state of the TransferDetailsOneScreen, including the
/// current transferDetailsOneModelObj
class TransferDetailsOneController extends GetxController {Rx<TransferDetailsOneModel> transferDetailsOneModelObj = TransferDetailsOneModel().obs;

SelectionPopupModel? selectedDropDownValue;

onSelected(dynamic value) { for (var element in transferDetailsOneModelObj.value.dropdownItemList.value) {element.isSelected = false; if (element.id == value.id) {element.isSelected = true;}} transferDetailsOneModelObj.value.dropdownItemList.refresh(); } 
 }
