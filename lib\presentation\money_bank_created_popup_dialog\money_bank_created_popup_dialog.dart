import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/money_bank_created_popup_controller.dart';

// ignore_for_file: must_be_immutable
class MoneyBankCreatedPopupDialog extends StatelessWidget {
  MoneyBankCreatedPopupDialog(this.controller, {Key? key}) : super(key: key);

  MoneyBankCreatedPopupController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
        width: 388.h,
        padding: EdgeInsets.symmetric(horizontal: 24.h, vertical: 32.v),
        decoration: AppDecoration.fillWhiteA
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomImageView(
                  imagePath: ImageConstant.passwordChanged,
                  height: 116.v,
                  width: 116.h,
                  fit: BoxFit.contain,
                  alignment: Alignment.center),
              SizedBox(height: 19.v),
              Text("lbl_card_created".tr,
                  style: CustomTextStyles.titleLargeBlack900),
              SizedBox(height: 9.v),
              Text("msg_jazopay_card_has".tr,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyLarge!.copyWith(height: 1.29)),
              SizedBox(height: 37.v),
              CustomElevatedButton(
                  text: "lbl_done".tr,
                  onPressed: () {
                    onTapDone();
                  })
            ]));
  }

  /// Navigates to the homeCardSliderScreen when the action is triggered.
  onTapDone() {
    Get.toNamed(
      AppRoutes.homeScreen,
    );
  }
}
