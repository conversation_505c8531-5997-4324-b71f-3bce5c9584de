import 'package:payway/presentation/transfer_screen/models/recent_transactions_model.dart';
import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import '../../widgets/custom_text_form_field.dart';
import 'widgets/transfer_item_widget.dart';
import 'models/transfer_item_model.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/transfer_controller.dart';

class TransferScreen extends GetWidget<TransferController> {
  const TransferScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: SizedBox(
        width: SizeUtils.width,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(top: 16.v),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 19.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                GetBuilder<TransferController>(
                    init: TransferController(),
                    builder: (controller) {
                      return CustomTextFormField(
                        controller: controller.searchController,
                        hintText: "lbl_search".tr,
                        textInputType: TextInputType.text,
                        textInputAction: TextInputAction.done,
                        prefix: Container(
                          margin: EdgeInsets.fromLTRB(16.h, 16.v, 12.h, 16.v),
                          child: CustomImageView(
                            imagePath: ImageConstant.imgSearch,
                          ),
                        ),
                        prefixConstraints: BoxConstraints(
                          maxHeight: 56.v,
                        ),
                      );
                    }),
                SizedBox(height: 26.v),
                Padding(
                    padding: EdgeInsets.only(left: 1.h),
                    child: Text("msg_recent_transactions".tr,
                        style: CustomTextStyles.titleLargeBlack900_1)),
                SizedBox(height: 19.v),
                GridView.builder(
                  primary: true,
                  shrinkWrap: true,
                  itemCount: controller.recentTransactionList.length,
                  // padding: EdgeInsets.only(top: 0.v, left: 20.h, right: 20.h),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    mainAxisExtent: 115.h,
                    crossAxisCount: 4,
                    mainAxisSpacing: 16.v,
                    crossAxisSpacing: 16.v,
                  ),
                  itemBuilder: (context, index) {
                    RecentTransactionModel model =
                        controller.recentTransactionList[index];

                    return GestureDetector(
                      onTap: () {
                        onTapWajihTaysirHandal();
                      },
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomImageView(
                            imagePath: model.image,
                            height: 75.v,
                            width: 75.v,
                          ),
                          SizedBox(height: 6.h),
                          Text(model.title ?? "",
                              style: theme.textTheme.bodyLarge)
                        ],
                      ),
                    );
                  },
                ),
                SizedBox(height: 24.v),
                Padding(
                    padding: EdgeInsets.only(left: 1.h),
                    child: Text("lbl_all_people".tr,
                        style: CustomTextStyles.titleLargeBlack900_1)),
                SizedBox(height: 17.v),
                _buildTransfer(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_transfer2".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildTransfer() {
    return Padding(
      padding: EdgeInsets.only(left: 1.h),
      child: Obx(
        () => ListView.separated(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          separatorBuilder: (context, index) {
            return SizedBox(height: 16.v);
          },
          itemCount:
              controller.transferModelObj.value.transferItemList.value.length,
          itemBuilder: (context, index) {
            TransferItemModel model =
                controller.transferModelObj.value.transferItemList.value[index];
            return TransferItemWidget(
              model,
              onTapWajihTaysirHandal: () {
                onTapWajihTaysirHandal();
              },
            );
          },
        ),
      ),
    );
  }

  /// Navigates to the homeCardSliderScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the transferDetailsThreeScreen when the action is triggered.
  onTapWajihTaysirHandal() {
    Get.toNamed(
      AppRoutes.transferDetailsThreeScreen,
    );
  }
}
