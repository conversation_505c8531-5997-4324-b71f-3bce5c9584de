import '../models/depositscurrentdeposite_item_model.dart';
import '../controller/deposits_current_deposite_controller.dart';
import 'package:payway/widgets/custom_outlined_button.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class DepositscurrentdepositeItemWidget extends StatelessWidget {
  DepositscurrentdepositeItemWidget(
    this.depositscurrentdepositeItemModelObj, {
    Key? key,
    this.onTapWithdrawal,
    this.onTapTopUp,
  }) : super(
          key: key,
        );

  DepositscurrentdepositeItemModel depositscurrentdepositeItemModelObj;

  var controller = Get.find<DepositsCurrentDepositeController>();

  VoidCallback? onTapWithdrawal;

  VoidCallback? onTapTopUp;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineBlack.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder12,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(
                      () => Text(
                        depositscurrentdepositeItemModelObj.eight!.value,
                        style: theme.textTheme.titleMedium,
                      ),
                    ),
                    SizedBox(height: 6.v),
                    Obx(
                      () => Text(
                        depositscurrentdepositeItemModelObj.subtitle!.value,
                        style: theme.textTheme.bodyLarge,
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: EdgeInsets.only(
                    top: 12.v,
                    bottom: 14.v,
                  ),
                  child: Obx(
                    () => Text(
                      depositscurrentdepositeItemModelObj.price!.value,
                      style: theme.textTheme.titleMedium,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 15.v),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildWithdrawal(),
                _buildTopUp(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildWithdrawal() {
    return Expanded(
      child: CustomOutlinedButton(
        height: 40.v,
        text: "lbl_withdrawal".tr,
        margin: EdgeInsets.only(right: 8.h),
        buttonTextStyle: CustomTextStyles.titleSmallGray700,
        onPressed: () {
          onTapWithdrawal!.call();
        },
      ),
    );
  }

  /// Section Widget
  Widget _buildTopUp() {
    return Expanded(
      child: CustomElevatedButton(
        height: 40.v,
        text: "lbl_top_up".tr,
        margin: EdgeInsets.only(left: 8.h),
        buttonStyle: CustomButtonStyles.fillIndigo,
        buttonTextStyle: CustomTextStyles.titleSmallSemiBold14,
        onPressed: () {
          onTapTopUp!.call();
        },
      ),
    );
  }
}
