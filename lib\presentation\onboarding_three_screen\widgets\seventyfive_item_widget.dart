import '../models/seventyfive_item_model.dart';
import '../controller/onboarding_three_controller.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class SeventyfiveItemWidget extends StatelessWidget {
  SeventyfiveItemWidget(
    this.seventyfiveItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  SeventyfiveItemModel seventyfiveItemModelObj;

  var controller = Get.find<OnboardingThreeController>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          "lbl_zero_fees".tr,
          style: theme.textTheme.displaySmall,
        ),
        Sized<PERSON><PERSON>(height: 16.v),
        <PERSON>zed<PERSON><PERSON>(
          width: 360.h,
          child: Text(
            "msg_zero_fee_merchant".tr,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: CustomTextStyles.bodyLargeBlack900.copyWith(
              height: 1.29,
            ),
          ),
        ),
      ],
    );
  }
}
