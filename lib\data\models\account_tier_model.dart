enum AccountTier {
  basic,
  premium,
  business,
  enterprise
}

enum AccountType {
  personal,
  business,
  child
}

class AccountTierModel {
  final String id;
  final String userId;
  final AccountTier tier;
  final AccountType type;
  final DateTime createdAt;
  final DateTime? upgradedAt;
  final Map<String, dynamic> features;
  final bool isActive;

  AccountTierModel({
    required this.id,
    required this.userId,
    required this.tier,
    required this.type,
    required this.createdAt,
    this.upgradedAt,
    required this.features,
    required this.isActive,
  });

  factory AccountTierModel.fromJson(Map<String, dynamic> json) {
    return AccountTierModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      tier: AccountTier.values.firstWhere(
        (e) => e.toString() == 'AccountTier.${json['tier']}',
        orElse: () => AccountTier.basic,
      ),
      type: AccountType.values.firstWhere(
        (e) => e.toString() == 'AccountType.${json['type']}',
        orElse: () => AccountType.personal,
      ),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      upgradedAt: json['upgradedAt'] != null 
        ? DateTime.parse(json['upgradedAt']) 
        : null,
      features: Map<String, dynamic>.from(json['features'] ?? {}),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'tier': tier.toString().split('.').last,
      'type': type.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'upgradedAt': upgradedAt?.toIso8601String(),
      'features': features,
      'isActive': isActive,
    };
  }

  String get tierDisplayName {
    switch (tier) {
      case AccountTier.basic:
        return 'Basic';
      case AccountTier.premium:
        return 'Premium';
      case AccountTier.business:
        return 'Business';
      case AccountTier.enterprise:
        return 'Enterprise';
    }
  }

  String get tierDescription {
    switch (tier) {
      case AccountTier.basic:
        return 'Free account with basic features';
      case AccountTier.premium:
        return 'Enhanced features and priority support';
      case AccountTier.business:
        return 'Business tools and analytics';
      case AccountTier.enterprise:
        return 'Advanced business features and dedicated support';
    }
  }

  List<String> get tierFeatures {
    switch (tier) {
      case AccountTier.basic:
        return [
          'Basic payments',
          'Transaction history',
          'QR code payments',
          'Basic support'
        ];
      case AccountTier.premium:
        return [
          'All Basic features',
          'Priority support',
          'Advanced analytics',
          'Custom themes',
          'Higher transaction limits'
        ];
      case AccountTier.business:
        return [
          'All Premium features',
          'Business dashboard',
          'Product management',
          'Escrow payments',
          'Business analytics',
          'Multi-user access'
        ];
      case AccountTier.enterprise:
        return [
          'All Business features',
          'Dedicated support',
          'Custom integrations',
          'Advanced security',
          'White-label options',
          'API access'
        ];
    }
  }

  double get monthlyFee {
    switch (tier) {
      case AccountTier.basic:
        return 0.0;
      case AccountTier.premium:
        return 9.99;
      case AccountTier.business:
        return 29.99;
      case AccountTier.enterprise:
        return 99.99;
    }
  }
} 