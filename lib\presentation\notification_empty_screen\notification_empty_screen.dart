import 'package:payway/widgets/app_bar/custom_app_bar.dart';import 'package:payway/widgets/app_bar/appbar_leading_image.dart';import 'package:payway/widgets/app_bar/appbar_subtitle.dart';import 'package:flutter/material.dart';import 'package:payway/core/app_export.dart';import 'controller/notification_empty_controller.dart';class NotificationEmptyScreen extends GetWidget<NotificationEmptyController> {const NotificationEmptyScreen({Key? key}) : super(key: key);

@override Widget build(BuildContext context) { return SafeArea(child: Scaffold(appBar: _buildAppBar(), body: Container(width: double.maxFinite, padding: EdgeInsets.only(top: 239.v), child: Column(children: [GestureDetector(onTap: () {onTapLayerSeventeen();}, child: Container(height: 116.adaptSize, width: 116.adaptSize, padding: EdgeInsets.symmetric(horizontal: 35.h, vertical: 26.v), decoration: AppDecoration.fillIndigo.copyWith(borderRadius: BorderRadiusStyle.circleBorder58), child: CustomImageView(imagePath: ImageConstant.imgLayer17, height: 64.v, width: 45.h, alignment: Alignment.center))), SizedBox(height: 18.v), Text("msg_no_notifications".tr, style: CustomTextStyles.titleLargeBlack900), SizedBox(height: 7.v), SizedBox(width: 266.h, child: Text("msg_we_did_not_found".tr, maxLines: 2, overflow: TextOverflow.ellipsis, textAlign: TextAlign.center, style: theme.textTheme.bodyLarge!.copyWith(height: 1.29))), SizedBox(height: 5.v)])))); } 
/// Section Widget
PreferredSizeWidget _buildAppBar() { return CustomAppBar(leadingWidth: 52.h, leading: AppbarLeadingImage(imagePath: ImageConstant.imgExpandMoreFil, margin: EdgeInsets.only(left: 20.h, top: 17.v, bottom: 25.v), onTap: () {onTapExpandMoreFIL();}), title: AppbarSubtitle(text: "lbl_notifications".tr, margin: EdgeInsets.only(left: 16.h)), styleType: Style.bgFill); } 
/// Navigates to the homeCardSliderScreen when the action is triggered.
onTapExpandMoreFIL() { Get.toNamed(AppRoutes.homeCardSliderScreen, ); } 
/// Navigates to the notificationScreen when the action is triggered.
onTapLayerSeventeen() { Get.toNamed(AppRoutes.notificationScreen, ); } 
 }
