import '../../../core/app_export.dart';
import 'thisweek_item_model.dart';

/// This class defines the variables used in the [this_week_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class ThisWeekModel {
  Rx<List<ThisweekItemModel>> thisweekItemList = Rx([
    ThisweekItemModel(
      financialtransaction: "Financial transaction".obs,
      duration: "Today, 1:20 Pm".obs,
      price: "\$52.00".obs,
    ),
    ThisweekItemModel(
      financialtransaction: "Loan deposite".obs,
      duration: "20 March, 2:10 Am".obs,
      price: "\$120.00".obs,
    ),
    ThisweekItemModel(
      financialtransaction: "Withdraw ATM".obs,
      duration: "4 January, 4:00 Am".obs,
      price: "\$100.00".obs,
    ),
    ThisweekItemModel(
      financialtransaction: "Nicotine cravings".obs,
      duration: "4 January, 4:00 Am".obs,
      price: "\$20.00".obs,
    ),
    ThisweekItemModel(
      financialtransaction: "Restlessness".obs,
      duration: "4 January, 4:00 Am".obs,
      price: "\$200.00".obs,
    ),
    ThisweekItemModel(
      financialtransaction: "Trouble".obs,
      duration: "4 January, 4:00 Am".obs,
      price: "\$123.00".obs,
    ),
    ThisweekItemModel(
      financialtransaction: "Sleeping".obs,
      duration: "4 January, 4:00 Am".obs,
      price: "\$450.00".obs,
    ),
    ThisweekItemModel(
      financialtransaction: "Mood swings".obs,
      duration: "4 January, 4:00 Am".obs,
      price: "\$213.00".obs,
    ),
  ]);
}
