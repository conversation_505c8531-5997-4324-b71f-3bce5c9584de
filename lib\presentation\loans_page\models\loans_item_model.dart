import '../../../core/app_export.dart';

/// This class is used in the [loans_item_widget] screen.
class LoansItemModel {
  LoansItemModel({
    this.price,
    this.repay,
    this.rate,
    this.fifteen,
    this.perioud,
    this.duration,
    this.monthlyPayment,
    this.price1,
    this.totalPeriod,
    this.price2,
    this.id,
  }) {
    price = price ?? Rx("\$2000.00");
    repay = repay ?? Rx("Repay");
    rate = rate ?? Rx("Rate");
    fifteen = fifteen ?? Rx("15%");
    perioud = perioud ?? Rx("Perioud");
    duration = duration ?? Rx("20 Month");
    monthlyPayment = monthlyPayment ?? Rx("Monthly payment");
    price1 = price1 ?? Rx("2000.00");
    totalPeriod = totalPeriod ?? Rx("Total period");
    price2 = price2 ?? Rx("2500.00");
    id = id ?? Rx("");
  }

  Rx<String>? price;

  Rx<String>? repay;

  Rx<String>? rate;

  Rx<String>? fifteen;

  Rx<String>? perioud;

  Rx<String>? duration;

  Rx<String>? monthlyPayment;

  Rx<String>? price1;

  Rx<String>? totalPeriod;

  Rx<String>? price2;

  Rx<String>? id;
}
