import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/reaset_password_controller.dart';

// ignore_for_file: must_be_immutable
class ReasetPasswordDialog extends StatefulWidget {
  ReasetPasswordDialog({Key? key}) : super(key: key);

  @override
  State<ReasetPasswordDialog> createState() => _ReasetPasswordDialogState();
}

class _ReasetPasswordDialogState extends State<ReasetPasswordDialog> {
  ReasetPasswordController controller = Get.put(ReasetPasswordController());

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 388.h,
      padding: EdgeInsets.symmetric(horizontal: 24.h, vertical: 32.v),
      decoration: AppDecoration.fillWhiteA
          .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
      child: <PERSON>umn(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomImageView(
              imagePath: ImageConstant.passwordChanged,
              height: 116.v,
              width: 116.h,
              fit: BoxFit.contain,
              alignment: Alignment.center),
          SizedBox(height: 16.v),
          Text("msg_password_changed".tr,
              style: CustomTextStyles.titleLargeBlack900),
          SizedBox(height: 8.v),
          Container(
              width: 332.h,
              margin: EdgeInsets.symmetric(horizontal: 3.h),
              child: Text("msg_your_password_has".tr,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
          SizedBox(height: 37.v),
          CustomElevatedButton(
            text: "lbl_ok".tr,
            onPressed: () {
              controller.clearText();
              onTapOk();
            },
          ),
        ],
      ),
    );
  }

  /// Navigates to the contryOfResidenceScreen when the action is triggered.
  onTapOk() {
    Get.toNamed(
      AppRoutes.contryOfResidenceScreen,
    );
  }
}
