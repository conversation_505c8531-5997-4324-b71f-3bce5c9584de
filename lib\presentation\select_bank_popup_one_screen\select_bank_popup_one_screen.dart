// ignore_for_file: must_be_immutable

import '../../widgets/custom_icon_button.dart';
import 'models/selectbankpopupone_item_model.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/select_bank_popup_one_controller.dart';

class SelectBankPopupOneScreen extends StatelessWidget {
  SelectBankPopupOneScreen({Key? key}) : super(key: key);

  SelectBankPopupOneController controller =
      Get.put(SelectBankPopupOneController());

  @override
  Widget build(BuildContext context) {
    return Container(
      width: SizeUtils.width,
      // height: 470.v,
      decoration: BoxDecoration(
        color: appTheme.whiteA700,
        borderRadius: BorderRadiusStyle.customBorderTL12,
      ),
      child: _buildPopup(),
    );
  }

  /// Section Widget
  Widget _buildPopup() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 24.v),
        Align(
            alignment: Alignment.centerLeft,
            child: Padding(
                padding: EdgeInsets.only(left: 20.h),
                child: Text("lbl_select_bank".tr,
                    style: CustomTextStyles.titleLargeBlack900))),
        SizedBox(height: 17.v),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.h),
          child: Obx(
            () => ListView.separated(
              physics: NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              separatorBuilder: (context, index) {
                return SizedBox(height: 16.v);
              },
              itemCount: controller.selectBankPopupOneModelObj.value
                  .selectbankpopuponeItemList.value.length,
              itemBuilder: (context, index) {
                SelectbankpopuponeItemModel model = controller
                    .selectBankPopupOneModelObj
                    .value
                    .selectbankpopuponeItemList
                    .value[index];

                return GestureDetector(
                  onTap: () {
                    controller.isSelected.value = index;
                    controller.update();
                  },
                  child: Container(
                    padding: EdgeInsets.all(8.h),
                    decoration: AppDecoration.outlineBlack.copyWith(
                      borderRadius: BorderRadiusStyle.roundedBorder12,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Obx(
                                () => CustomIconButton(
                                  height: 55.adaptSize,
                                  width: 55.adaptSize,
                                  padding: EdgeInsets.all(7.h),
                                  decoration:
                                      IconButtonStyleHelper.fillGrayTL12,
                                  child: CustomImageView(
                                    imagePath: model.masterCard!.value,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    top: 6.v, bottom: 6.v, left: 12.h),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Obx(
                                      () => Text(
                                        model.twoThousandFiveHundredFortyOne!
                                            .value,
                                        style: theme.textTheme.bodyLarge,
                                      ),
                                    ),
                                    SizedBox(height: 3.v),
                                    Obx(
                                      () => Text(
                                        model.price!.value,
                                        style: theme.textTheme.titleSmall,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Obx(
                          () => CustomImageView(
                            imagePath: controller.isSelected.value == index
                                ? ImageConstant.selectedRadio
                                : ImageConstant.unSelectedRadio,
                            height: 24.v,
                            width: 25.h,
                            margin: EdgeInsets.only(
                              top: 16.v,
                              right: 8.h,
                              bottom: 15.v,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        SizedBox(height: 16.v),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
          decoration: AppDecoration.fillWhiteA,
          child: Column(
            children: [
              SizedBox(height: 16.v),
              CustomElevatedButton(
                text: "lbl_continue".tr,
                onPressed: () {
                  onTapContinue();
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Navigates to the transferDetailsOneScreen when the action is triggered.
  onTapContinue() {
    Get.back();
  }
}
