import '../../../core/app_export.dart';
import '../models/proof_of_residency_model.dart';

/// A controller class for the ProofOfResidencyScreen.
///
/// This class manages the state of the ProofOfResidencyScreen, including the
/// current proofOfResidencyModelObj
class ProofOfResidencyController extends GetxController {
  Rx<ProofOfResidencyModel> proofOfResidencyModelObj =
      ProofOfResidencyModel().obs;

  RxInt selected = 0.obs;
}
