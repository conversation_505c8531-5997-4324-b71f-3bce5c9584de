import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:payway/presentation/home_screen/controller/home_controller.dart';
import 'package:payway/theme/theme_helper.dart';

import '../../core/utils/image_constant.dart';
import '../../core/utils/pref_utils.dart';
import '../../core/utils/size_utils.dart';
import '../../routes/app_routes.dart';
import '../../widgets/custom_image_view.dart';

class SplashScreen extends StatefulWidget {
  static String tag = '/';
  final String routeName;

  SplashScreen({this.routeName = "/"});

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  AnimationController? scaleController;
  Animation<double>? scaleAnimation;

  bool _a = false;
  bool _c = false;
  bool _d = false;
  bool _e = false;
  bool secondAnim = false;

  HomeController putHomeController = Get.put(HomeController());
  HomeController homeFindScreenController = Get.find<HomeController>();

  // List<LanguageData> langList = Data.getLanguage();
  // SelectLanguagesScreenController selectLanguagesScreenController1 =
  //     Get.put(SelectLanguagesScreenController());

  Color boxColor = Colors.transparent;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    await Future.delayed(Duration.zero);
    if (ModalRoute.of(context)!.settings.arguments != null) {
      HomeController homeController = Get.find<HomeController>();
      homeController.onChange(0.obs);
      Get.toNamed(AppRoutes.homeCardSliderScreen);
      return;
    }

    Timer(Duration(milliseconds: 1000), () {
      setState(() {
        boxColor = theme.colorScheme.primary;
        _a = true;
      });
    });
    Timer(Duration(milliseconds: 1500), () {
      setState(() {
        boxColor = theme.colorScheme.primary;
        _c = true;
      });
    });
    Timer(Duration(milliseconds: 1700), () {
      setState(() {
        _e = true;
      });
    });
    Timer(Duration(milliseconds: 3200), () {
      secondAnim = true;

      scaleController = AnimationController(
        vsync: this,
        duration: Duration(milliseconds: 1000),
      )..forward();
      scaleAnimation =
          Tween<double>(begin: 0.0, end: 12).animate(scaleController!);

      setState(() {
        boxColor = appTheme.bgColor;
        _d = true;
      });
    });

    Timer(Duration(milliseconds: 2000), () async {
      if (ModalRoute.of(context)!.settings.arguments != null) {
        HomeController homeController = Get.find<HomeController>();
        homeController.onChange(0.obs);
        Get.toNamed(AppRoutes.homeScreen);
      } else {
        bool isLogin = await PrefUtils.getLogin();
        bool isIntro = await PrefUtils.getIntro();

        await Future.delayed(Duration.zero);

        Timer(const Duration(seconds: 3), () {
          if (isIntro) {
            Get.toNamed(
              AppRoutes.onboardingOneScreen,
            );
          } else if (isLogin) {
            Get.toNamed(
              AppRoutes.loginScreen,
            );
            debugPrint("is Login ==== $isLogin");
          } else {
            Get.toNamed(
              AppRoutes.homeScreen,
            );
          }
        });
      }
    });
  }

  @override
  void dispose() {
    if (scaleController != null) {
      scaleController!.dispose();
    }

    super.dispose();
  }

  Widget build(BuildContext context) {
    double _h = MediaQuery.of(context).size.height;
    double _w = MediaQuery.of(context).size.width;
    // Constant.setupSize(context);
    mediaQueryData = MediaQuery.of(context);

    return Scaffold(
      backgroundColor: appTheme.bgColor,
      appBar: getInVisibleAppBar(
        statusBarBrightness: Brightness.dark,
        statusBarIconBrightness: Brightness.dark,
      ),
      body: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            AnimatedContainer(
              duration: Duration(milliseconds: _d ? 900 : 2500),
              curve: _d ? Curves.fastLinearToSlowEaseIn : Curves.elasticOut,
              height: _d
                  ? 0
                  : _a
                      ? _h / (kIsWeb ? 2.5 : 4.5)
                      : 20,
              width: 20,
            ),
            AnimatedContainer(
              duration: Duration(seconds: _c ? 2 : 0),
              curve: Curves.fastLinearToSlowEaseIn,
              height: _d
                  ? _h
                  : _c
                      ? 160
                      : 20,
              width: _d
                  ? _w
                  : _c
                      ? 200
                      : 20,
              decoration: BoxDecoration(
                  color: !_d ? appTheme.gray100 : appTheme.bgColor,
                  // shape: _d ? BoxShape.rectangle : BoxShape.circle,
                  borderRadius:
                      _d ? BorderRadius.only() : BorderRadius.circular(30)),
              child: secondAnim
                  ? Center(
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                            color: appTheme.bgColor, shape: BoxShape.circle),
                        child: AnimatedBuilder(
                          animation: scaleAnimation!,
                          builder: (c, child) => Transform.scale(
                            scale: scaleAnimation!.value,
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: appTheme.bgColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                    )
                  : Center(
                      child: _e
                          ? CustomImageView(
                              imagePath: ImageConstant.kojaPayLogo,
                              height: 150.v,
                              width: 150.v,
                            )
                          : SizedBox(),
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

AppBar getInVisibleAppBar(
    {Color color = Colors.transparent,
    Brightness? statusBarBrightness,
    Brightness? statusBarIconBrightness}) {
  return AppBar(
    toolbarHeight: 0,
    elevation: 0,
    backgroundColor: color,
    systemOverlayStyle: SystemUiOverlayStyle(
      statusBarColor: color,
      statusBarBrightness: statusBarBrightness,
      statusBarIconBrightness: statusBarIconBrightness,
    ),
  );
}
