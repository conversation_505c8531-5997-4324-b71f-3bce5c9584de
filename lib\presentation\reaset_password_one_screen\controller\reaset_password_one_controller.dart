import '../../../core/app_export.dart';import '../models/reaset_password_one_model.dart';import 'package:flutter/material.dart';/// A controller class for the ReasetPasswordOneScreen.
///
/// This class manages the state of the ReasetPasswordOneScreen, including the
/// current reasetPasswordOneModelObj
class ReasetPasswordOneController extends GetxController {TextEditingController newpasswordController = TextEditingController();

TextEditingController confirmpasswordController = TextEditingController();

Rx<ReasetPasswordOneModel> reasetPasswordOneModelObj = ReasetPasswordOneModel().obs;

Rx<bool> isShowPassword = true.obs;

Rx<bool> isShowPassword1 = true.obs;

@override void onClose() { super.onClose(); newpasswordController.dispose(); confirmpasswordController.dispose(); } 
 }
