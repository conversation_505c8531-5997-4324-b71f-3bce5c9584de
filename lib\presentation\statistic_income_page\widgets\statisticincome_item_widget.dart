import '../models/statisticincome_item_model.dart';
import '../controller/statistic_income_controller.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class StatisticincomeItemWidget extends StatelessWidget {
  StatisticincomeItemWidget(
    this.statisticincomeItemModelObj, {
    Key? key,
    this.onTapTransactions,
  }) : super(
          key: key,
        );

  StatisticincomeItemModel statisticincomeItemModelObj;

  var controller = Get.find<StatisticIncomeController>();

  VoidCallback? onTapTransactions;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTapTransactions!.call();
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16.h,
          vertical: 15.v,
        ),
        decoration: AppDecoration.outlineBlack.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder12,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconButton(
              height: 48.adaptSize,
              width: 48.adaptSize,
              padding: EdgeInsets.all(12.h),
              child: CustomImageView(
                imagePath: statisticincomeItemModelObj.image!.value,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 12.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(
                    () => Text(
                      statisticincomeItemModelObj.restlessness!.value,
                      style: theme.textTheme.titleMedium,
                    ),
                  ),
                  SizedBox(height: 6.v),
                  Obx(
                    () => Text(
                      statisticincomeItemModelObj.time!.value,
                      style: theme.textTheme.bodyLarge,
                    ),
                  ),
                ],
              ),
            ),
            Spacer(),
            Padding(
              padding: EdgeInsets.symmetric(vertical: 12.v),
              child: Obx(
                () => Text(
                  statisticincomeItemModelObj.price!.value,
                  style: theme.textTheme.titleLarge,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
