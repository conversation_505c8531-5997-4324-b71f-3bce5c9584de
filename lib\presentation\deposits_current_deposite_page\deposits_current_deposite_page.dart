import 'widgets/depositscurrentdeposite_item_widget.dart';
import 'models/depositscurrentdeposite_item_model.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/deposits_current_deposite_controller.dart';
import 'models/deposits_current_deposite_model.dart';

// ignore_for_file: must_be_immutable
class DepositsCurrentDepositePage extends StatelessWidget {
  DepositsCurrentDepositePage({Key? key}) : super(key: key);

  DepositsCurrentDepositeController controller = Get.put(
      DepositsCurrentDepositeController(DepositsCurrentDepositeModel().obs));

  @override
  Widget build(BuildContext context) {
    return _buildDepositscurrentdeposite();
  }

  /// Section Widget
  Widget _buildDepositscurrentdeposite() {
    return Obx(
      () => ListView.separated(
        physics: AlwaysScrollableScrollPhysics(),
        shrinkWrap: true,
        separatorBuilder: (context, index) {
          return SizedBox(height: 16.v);
        },
        padding:
            EdgeInsets.only(left: 20.h, right: 20.h, bottom: 240.v, top: 24.v),
        itemCount: controller.depositsCurrentDepositeModelObj.value
            .depositscurrentdepositeItemList.value.length,
        itemBuilder: (context, index) {
          DepositscurrentdepositeItemModel model = controller
              .depositsCurrentDepositeModelObj
              .value
              .depositscurrentdepositeItemList
              .value[index];
          return DepositscurrentdepositeItemWidget(
            model,
            onTapWithdrawal: () {
              onTapWithdrawal();
            },
            onTapTopUp: () {
              onTapTopUp();
            },
          );
        },
      ),
    );
  }

  /// Navigates to the withdrawScreen when the action is triggered.
  onTapWithdrawal() {
    Get.toNamed(
      AppRoutes.withdrawScreen,
    );
  }

  /// Navigates to the topUpScreen when the action is triggered.
  onTapTopUp() {
    Get.toNamed(
      AppRoutes.topUpScreen,
    );
  }

  /// Navigates to the openMoneyBankScreen when the action is triggered.
  onTapMoneyBank() {
    Get.toNamed(
      AppRoutes.openMoneyBankScreen,
    );
  }

  /// Navigates to the openDepositsScreen when the action is triggered.
  onTapDeposite() {
    Get.toNamed(
      AppRoutes.openDepositsScreen,
    );
  }

  /// Navigates to the homeScreen when the action is triggered.
  onTapIcon() {
    Get.toNamed(
      AppRoutes.homeScreen,
    );
  }

  /// Navigates to the statisticIncomeTabContainerScreen when the action is triggered.
  onTapIcon1() {
    Get.toNamed(
      AppRoutes.statisticIncomeTabContainerScreen,
    );
  }

  /// Navigates to the loansContainerScreen when the action is triggered.
  onTapIcon2() {
    Get.toNamed(
      AppRoutes.loansContainerScreen,
    );
  }
}
