import 'package:flutter/cupertino.dart';

import '../../../core/app_export.dart';
import '../models/reaset_password_model.dart';

/// A controller class for the ReasetPasswordDialog.
///
/// This class manages the state of the ReasetPasswordDialog, including the
/// current reasetPasswordModelObj
class ReasetPasswordController extends GetxController {
  Rx<ReasetPasswordModel> reasetPasswordModelObj = ReasetPasswordModel().obs;

  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();

  RxBool isShowPassword = true.obs;

  RxBool isShowPassword1 = true.obs;

  void clearText() {
    newPasswordController = TextEditingController(text: "");
    confirmPasswordController = TextEditingController(text: "");
  }
}
