import '../../../core/app_export.dart';
import 'depositscurrentdeposite_item_model.dart';

/// This class defines the variables used in the [deposits_current_deposite_page],
/// and is typically used to hold data that is passed between different parts of the application.
class DepositsCurrentDepositeModel {
  Rx<List<DepositscurrentdepositeItemModel>> depositscurrentdepositeItemList =
      Rx([
    DepositscurrentdepositeItemModel(
        eight: "8%".obs,
        subtitle: "Sep 30, 2022 - Mar 23, 2010".obs,
        price: "\$2000.00".obs),
    DepositscurrentdepositeItemModel(
        eight: "10%".obs,
        subtitle: "Jan 5, 2010 - May 15, 2020".obs,
        price: "\$3000.00".obs),
    DepositscurrentdepositeItemModel(
        eight: "15%".obs,
        subtitle: "Feb 30, 2011 - Apr 16, 2030".obs,
        price: "\$4000.00".obs),
    DepositscurrentdepositeItemModel(
        eight: "20%".obs,
        subtitle: "Jan 30, 2012 - May 20, 2040".obs,
        price: "\$2000.00".obs)
  ]);
}
