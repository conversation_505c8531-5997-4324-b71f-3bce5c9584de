import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/data/models/business_account_model.dart';
import 'controller/nearby_vendors_controller.dart';

class NearbyVendorsScreen extends GetWidget<NearbyVendorsController> {
  const NearbyVendorsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Nearby Vendors',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.my_location),
            onPressed: () => controller.getCurrentLocation(),
          ),
          IconButton(
            icon: Icon(Icons.filter_list),
            onPressed: () => controller.showFilterDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: _buildMapView(),
          ),
          _buildVendorList(),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: EdgeInsets.all(16.h),
      child: TextField(
        controller: controller.searchController,
        decoration: InputDecoration(
          hintText: 'Search vendors, products, or services...',
          prefixIcon: Icon(Icons.search),
          suffixIcon: IconButton(
            icon: Icon(Icons.clear),
            onPressed: () => controller.searchController.clear(),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.h),
          ),
        ),
        onChanged: (value) => controller.searchVendors(value),
      ),
    );
  }

  Widget _buildMapView() {
    return Obx(() => GoogleMap(
      initialCameraPosition: CameraPosition(
        target: LatLng(
          controller.currentLatitude.value,
          controller.currentLongitude.value,
        ),
        zoom: 15.0,
      ),
      onMapCreated: controller.onMapCreated,
      markers: controller.markers,
      myLocationEnabled: true,
      myLocationButtonEnabled: true,
      zoomControlsEnabled: true,
      mapType: MapType.normal,
      onCameraMove: controller.onCameraMove,
      onTap: controller.onMapTap,
    ));
  }

  Widget _buildVendorList() {
    return Obx(() => Container(
      height: 200.h,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.h)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildDragHandle(),
          Expanded(
            child: controller.isLoading.value
              ? Center(child: CircularProgressIndicator())
              : controller.filteredVendors.isEmpty
                ? _buildEmptyState()
                : _buildVendorListView(),
          ),
        ],
      ),
    ));
  }

  Widget _buildDragHandle() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.h),
      width: 40.w,
      height: 4.h,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(2.h),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 64.w,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'No vendors found nearby',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Try expanding your search area or check back later',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVendorListView() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.h),
      itemCount: controller.filteredVendors.length,
      itemBuilder: (context, index) {
        final vendor = controller.filteredVendors[index];
        return _buildVendorCard(vendor);
      },
    );
  }

  Widget _buildVendorCard(BusinessAccount vendor) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: () => controller.selectVendor(vendor),
        child: Padding(
          padding: EdgeInsets.all(16.h),
          child: Row(
            children: [
              _buildVendorAvatar(vendor),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      vendor.businessName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      vendor.businessType,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: 16.w,
                          color: Colors.grey[500],
                        ),
                        SizedBox(width: 4.w),
                        Expanded(
                          child: Text(
                            '${vendor.city}, ${vendor.state}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[500],
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: 16.w,
                          color: Colors.amber,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          '4.5 (120 reviews)',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        Spacer(),
                        if (vendor.isVerified)
                          Icon(
                            Icons.verified,
                            size: 16.w,
                            color: Colors.blue,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16.w,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVendorAvatar(BusinessAccount vendor) {
    return Container(
      width: 60.w,
      height: 60.w,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.h),
      ),
      child: Center(
        child: Icon(
          Icons.business,
          size: 24.w,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }
} 