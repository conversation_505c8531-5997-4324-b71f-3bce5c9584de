import '../../../core/app_export.dart';
import 'package:flutter/material.dart';

/// A controller class for the DepositsCurrentDepositeTabContainerScreen.
///
/// This class manages the state of the DepositsCurrentDepositeTabContainerScreen, including the
/// current depositsCurrentDepositeTabContainerModelObj
class DepositsCurrentDepositeTabContainerController extends GetxController
    with GetSingleTickerProviderStateMixin {
  // Rx<DepositsCurrentDepositeTabContainerModel>
  //     depositsCurrentDepositeTabContainerModelObj =
  //     DepositsCurrentDepositeTabContainerModel().obs;

  late TabController tabviewController =
      Get.put(TabController(vsync: this, length: 2));
}
