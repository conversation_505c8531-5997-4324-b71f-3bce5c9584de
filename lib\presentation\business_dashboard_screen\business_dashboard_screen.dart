import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/data/models/business_account_model.dart';
import 'package:payway/data/models/product_model.dart';
import 'controller/business_dashboard_controller.dart';

class BusinessDashboardScreen extends GetWidget<BusinessDashboardController> {
  const BusinessDashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Business Dashboard',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () => controller.openBusinessSettings(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBusinessHeader(),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildQuickStats(),
            Sized<PERSON>ox(height: 24.h),
            _buildQuickActions(),
            SizedBox(height: 24.h),
            _buildRecentProducts(),
            SizedBox(height: 24.h),
            _buildRecentTransactions(),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessHeader() {
    return Obx(() => Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Row(
          children: [
            Container(
              width: 60.w,
              height: 60.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.h),
              ),
              child: Icon(
                Icons.business,
                size: 32.w,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    controller.businessAccount.value?.businessName ?? 'Business Name',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    controller.businessAccount.value?.businessType ?? 'Business Type',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16.w,
                        color: Colors.grey[500],
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '${controller.businessAccount.value?.city ?? ''}, ${controller.businessAccount.value?.state ?? ''}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                      Spacer(),
                      if (controller.businessAccount.value?.isVerified == true)
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12.h),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.verified,
                                size: 12.w,
                                color: Colors.green,
                              ),
                              SizedBox(width: 4.w),
                              Text(
                                'Verified',
                                style: TextStyle(
                                  color: Colors.green,
                                  fontSize: 10.fSize,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'Total Sales',
            value: '\$${controller.totalSales.value.toStringAsFixed(2)}',
            icon: Icons.attach_money,
            color: Colors.green,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: _buildStatCard(
            title: 'Products',
            value: controller.totalProducts.value.toString(),
            icon: Icons.inventory,
            color: Colors.blue,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: _buildStatCard(
            title: 'Orders',
            value: controller.totalOrders.value.toString(),
            icon: Icons.shopping_cart,
            color: Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24.w,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    title: 'Add Product',
                    icon: Icons.add_shopping_cart,
                    color: Colors.green,
                    onTap: () => controller.addProduct(),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildActionButton(
                    title: 'View Orders',
                    icon: Icons.shopping_bag,
                    color: Colors.blue,
                    onTap: () => controller.viewOrders(),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    title: 'Analytics',
                    icon: Icons.analytics,
                    color: Colors.purple,
                    onTap: () => controller.viewAnalytics(),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: _buildActionButton(
                    title: 'Settings',
                    icon: Icons.settings,
                    color: Colors.grey,
                    onTap: () => controller.openBusinessSettings(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.h),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.h),
          border: Border.all(
            color: color.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 24.w,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentProducts() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Recent Products',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Spacer(),
                TextButton(
                  onPressed: () => controller.viewAllProducts(),
                  child: Text('View All'),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Obx(() => controller.recentProducts.isEmpty
              ? _buildEmptyState('No products yet', 'Add your first product to start selling')
              : ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: controller.recentProducts.length,
                  itemBuilder: (context, index) {
                    final product = controller.recentProducts[index];
                    return _buildProductItem(product);
                  },
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildProductItem(Product product) {
    return ListTile(
      leading: Container(
        width: 50.w,
        height: 50.w,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8.h),
        ),
        child: product.images.isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8.h),
              child: Image.network(
                product.images.first,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Icon(
                  Icons.image,
                  size: 24.w,
                  color: Colors.grey[400],
                ),
              ),
            )
          : Icon(
              Icons.image,
              size: 24.w,
              color: Colors.grey[400],
            ),
      ),
      title: Text(
        product.name,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        '\$${product.price.toStringAsFixed(2)} • ${product.category}',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey[600],
        ),
      ),
      trailing: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: product.isAvailable 
            ? Colors.green.withOpacity(0.1)
            : Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.h),
        ),
        child: Text(
          product.isAvailable ? 'Active' : 'Inactive',
          style: TextStyle(
            color: product.isAvailable ? Colors.green : Colors.red,
            fontSize: 10.fSize,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      onTap: () => controller.editProduct(product),
    );
  }

  Widget _buildRecentTransactions() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Recent Transactions',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                Spacer(),
                TextButton(
                  onPressed: () => controller.viewAllTransactions(),
                  child: Text('View All'),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Obx(() => controller.recentTransactions.isEmpty
              ? _buildEmptyState('No transactions yet', 'Your recent transactions will appear here')
              : ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: controller.recentTransactions.length,
                  itemBuilder: (context, index) {
                    final transaction = controller.recentTransactions[index];
                    return _buildTransactionItem(transaction);
                  },
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(dynamic transaction) {
    return ListTile(
      leading: Container(
        width: 40.w,
        height: 40.w,
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20.h),
        ),
        child: Icon(
          Icons.payment,
          size: 20.w,
          color: Colors.green,
        ),
      ),
      title: Text(
        'Order #${transaction['id'] ?? 'N/A'}',
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        '\$${transaction['amount']?.toStringAsFixed(2) ?? '0.00'} • ${transaction['status'] ?? 'Pending'}',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey[600],
        ),
      ),
      trailing: Text(
        transaction['date'] ?? 'Today',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey[500],
        ),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 32.h),
        child: Column(
          children: [
            Icon(
              Icons.inbox,
              size: 48.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
} 