// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/presentation/login_screen/controller/login_controller.dart';

import '../../core/Constant/constants.dart';
import '../../widgets/custom_elevated_button.dart';
import '../../widgets/custom_text_form_field.dart';

// ignore_for_file: must_be_immutable
// class LoginScreen extends GetWidget<LoginController> {
//   LoginScreen({Key? key}) : super(key: key);
//
//   GlobalKey<FormState> _formKey = GlobalKey<FormState>();
//
//   TabController tabController = TabController(length: 2,vsync: this);
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       resizeToAvoidBottomInset: false,
//       body: SafeArea(
//         child: SizedBox(
//           width: SizeUtils.width,
//           child: SingleChildScrollView(
//             padding: EdgeInsets.only(
//                 bottom: MediaQuery.of(context).viewInsets.bottom),
//             child: Form(
//               key: _formKey,
//               child: Container(
//                 width: double.maxFinite,
//                 padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 24.v),
//                 child: Column(
//                   children: [
//                     CustomImageView(
//                         imagePath: ImageConstant.imgThumbsUp,
//                         height: 55.v,
//                         width: 65.h),
//                     SizedBox(height: 20.v),
//                     Text("lbl_welcome_back".tr,
//                         style: CustomTextStyles.titleLargeBlack900),
//                     SizedBox(height: 49.v),
//                     TabBar(
//                         unselectedLabelStyle: TextStyle(
//                           fontSize: 16,
//                           fontWeight: FontWeight.w400,
//                           color: appTheme.black900,
//                         ),
//                         indicatorWeight: 4,
//                         indicatorSize: TabBarIndicatorSize.tab,
//                         controller: controller.tabBarController,
//                         tabs: [
//                           Tab(
//                             text: "lbl_log_in".tr,
//                           ),
//                           Tab(
//                             text: "lbl_sign_up".tr,
//                           )
//                         ]),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
//   /// Navigates to the signUpScreen when the action is triggered.
//   onTapTxtCompleted() {
//     Get.toNamed(
//       AppRoutes.signUpScreen,
//     );
//   }
//
//   /// Navigates to the forgotPasswordScreen when the action is triggered.
//   onTapTxtForgotPassword() {
//     Get.toNamed(
//       AppRoutes.forgotPasswordScreen,
//     );
//   }
// }

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

LoginController controller = Get.put(LoginController());

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    // TODO: implement initState
    controller.tabController = TabController(
      vsync: this,
      length: 2,
      initialIndex: 0,
    )..addListener(() {});
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Constant.closeApp();
        return false;
      },
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          body: SafeArea(
            child: SizedBox(
              width: SizeUtils.width,
              child: Form(
                key: _formKey,
                child: Container(
                  width: double.maxFinite,
                  padding:
                      EdgeInsets.symmetric(horizontal: 20.h, vertical: 24.v),
                  child: Column(
                    children: [
                      CustomImageView(
                          imagePath: ImageConstant.imgThumbsUp,
                          height: 55.v,
                          width: 65.h),
                      SizedBox(height: 20.v),
                      Text("lbl_welcome_back".tr,
                          style: CustomTextStyles.titleLargeBlack900),
                      SizedBox(height: 49.v),
                      TabBar(
                          unselectedLabelStyle: theme.textTheme.bodyLarge,
                          labelStyle: theme.textTheme.titleMedium!
                              .copyWith(color: theme.colorScheme.primary),
                          indicatorWeight: 4,
                          indicatorSize: TabBarIndicatorSize.tab,
                          labelPadding: EdgeInsets.zero,
                          labelColor: theme.colorScheme.primary,
                          controller: controller.tabController,
                          tabs: [
                            Tab(
                              text: "lbl_log_in".tr,
                            ),
                            Tab(
                              text: "lbl_sign_up".tr,
                            )
                          ]),
                      Expanded(
                        child: TabBarView(
                          controller: controller.tabController,
                          children: [
                            buildLogIn(),
                            buildSignUp(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Navigates to the signUpScreen when the action is triggered.
  onTapTxtCompleted() {
    Get.toNamed(
      AppRoutes.signUpScreen,
    );
  }

  /// Navigates to the forgotPasswordScreen when the action is triggered.
  onTapTxtForgotPassword() {
    Get.toNamed(
      AppRoutes.forgotPasswordScreen,
    );

    controller.clearText();
  }

  Widget buildLogIn() {
    return Padding(
        padding: EdgeInsets.only(top: 40.h),
        child: SingleChildScrollView(
          child: Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
            CustomTextFormField(
              controller: controller.emailController,
              hintText: "Email address".tr,
              textInputType: TextInputType.emailAddress,
              validator: controller.emailValidator,
            ),
            SizedBox(height: 24.v),
            Obx(() => CustomTextFormField(
                  controller: controller.passwordController,
                  hintText: "Password".tr,
                  textInputAction: TextInputAction.done,
                  textInputType: TextInputType.visiblePassword,
                  suffix: InkWell(
                      onTap: () {
                        controller.isShowPassword.value =
                            !controller.isShowPassword.value;
                      },
                      child: Container(
                          margin: EdgeInsets.fromLTRB(30.h, 18.v, 16.h, 18.v),
                          child: CustomImageView(
                              imagePath: ImageConstant.imgEye21,
                              height: 20.adaptSize,
                              width: 20.adaptSize))),
                  suffixConstraints: BoxConstraints(maxHeight: 56.v),
                  validator: controller.passwordValidator,
                  obscureText: controller.isShowPassword.value,
                )),
            SizedBox(height: 18.v),
            GestureDetector(
                onTap: () {
                  onTapTxtForgotPassword();
                },
                child: Text("msg_forgot_password".tr,
                    style: CustomTextStyles.bodyLargePrimary)),
            SizedBox(height: 46.v),
            CustomElevatedButton(
              text: "lbl_log_in".tr,
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  onTapLogin();
                }
              },
            )
          ]),
        ));
  }

  onTapLogin() {
    PrefUtils.setLogin(false);
    Get.toNamed(
      AppRoutes.contryOfResidenceScreen,
    );
  }

  Widget buildSignUp() {
    return Padding(
        padding: EdgeInsets.only(top: 40.h),
        child: SingleChildScrollView(
          child: Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
            CustomTextFormField(
              controller: controller.firstNameController,
              hintText: "lbl_first_name".tr,
              hintStyle: theme.textTheme.bodyLarge!,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "Please enter first name";
                }
                return null;
              },
            ),
            SizedBox(height: 24.v),
            CustomTextFormField(
              controller: controller.lastNameController,
              hintText: "lbl_last_name".tr,
              hintStyle: theme.textTheme.bodyLarge!,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "Please enter last name";
                }
                return null;
              },
            ),
            SizedBox(height: 24.v),
            CustomTextFormField(
              controller: controller.emailController,
              hintText: "lbl_email_address".tr,
              hintStyle: theme.textTheme.bodyLarge!,
              textInputType: TextInputType.emailAddress,
              validator: (value) {
                if (value!.isNotEmpty) {
                  if (!RegExp(r'^.+@[a-zA-Z]+\.{1}[a-zA-Z]+(\.{0,1}[a-zA-Z]+)$')
                      .hasMatch(value)) {
                    return "Please enter a valid email address";
                  }
                  return null;
                }
                return "Please enter email address.";
              },
            ),
            SizedBox(height: 24.v),
            Obx(() => CustomTextFormField(
                  controller: controller.passwordController,
                  hintText: "lbl_password".tr,
                  hintStyle: theme.textTheme.bodyLarge!,
                  textInputAction: TextInputAction.done,
                  textInputType: TextInputType.visiblePassword,
                  suffix: InkWell(
                      onTap: () {
                        controller.isShowPassword.value =
                            !controller.isShowPassword.value;
                      },
                      child: Container(
                          margin: EdgeInsets.fromLTRB(30.h, 18.v, 16.h, 18.v),
                          child: CustomImageView(
                              imagePath: controller.isShowPassword.value == true
                                  ? ImageConstant.imgEye21
                                  : ImageConstant.imgEye22,
                              height: 20.adaptSize,
                              width: 20.adaptSize))),
                  suffixConstraints: BoxConstraints(maxHeight: 56.v),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Please enter password";
                    }
                    return null;
                  },
                  obscureText: controller.isShowPassword.value,
                )),
            SizedBox(height: 48.v),
            CustomElevatedButton(
              text: "Sign up".tr,
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  PrefUtils.setLogin(false);
                  Get.toNamed(
                    AppRoutes.contryOfResidenceScreen,
                  );
                }
              },
            )
          ]),
        ));
  }
}
