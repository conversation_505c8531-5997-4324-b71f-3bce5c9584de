import '../models/proofofresidency_item_model.dart';
import '../controller/proof_of_residency_controller.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class ProofofresidencyItemWidget extends StatelessWidget {
  ProofofresidencyItemWidget(
    this.proofofresidencyItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  ProofofresidencyItemModel proofofresidencyItemModelObj;

  var controller = Get.find<ProofOfResidencyController>();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.h),
      decoration: AppDecoration.outlineBlack.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder12,
      ),
      child: Row(
        children: [
          CustomImageView(
            imagePath: proofofresidencyItemModelObj.passport!.value,
            height: 24.v,
            width: 25.h,
            margin: EdgeInsets.symmetric(vertical: 11.v),
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  proofofresidencyItemModelObj.passport1!.value,
                  style: theme.textTheme.titleMedium,
                ),
                SizedBox(height: 3.v),
                Text(
                  proofofresidencyItemModelObj.issuedInIndia!.value,
                  style: theme.textTheme.bodyLarge,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
