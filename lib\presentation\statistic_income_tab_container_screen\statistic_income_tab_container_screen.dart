import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/presentation/statistic_income_page/statistic_income_page.dart';
import 'package:payway/presentation/statistic_expenses_page/statistic_expenses_page.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/statistic_income_tab_container_controller.dart';

// ignore_for_file: must_be_immutable
class StatisticIncomeTabContainerScreen extends StatefulWidget {
  const StatisticIncomeTabContainerScreen({Key? key})
      : super(
          key: key,
        );

  @override
  State<StatisticIncomeTabContainerScreen> createState() =>
      _StatisticIncomeTabContainerScreenState();
}

class _StatisticIncomeTabContainerScreenState
    extends State<StatisticIncomeTabContainerScreen> {
  StatisticIncomeTabContainerController controller =
      Get.put(StatisticIncomeTabContainerController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: SizedBox(
        width: double.maxFinite,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 15.v),
            Container(
              height: 30.v,
              width: 388.h,
              margin: EdgeInsets.only(left: 20.h, right: 20.h),
              child: TabBar(
                unselectedLabelStyle: theme.textTheme.bodyLarge,
                labelStyle: theme.textTheme.titleMedium!
                    .copyWith(color: theme.colorScheme.primary),
                indicatorWeight: 4,
                indicatorSize: TabBarIndicatorSize.tab,
                labelPadding: EdgeInsets.zero,
                labelColor: theme.colorScheme.primary,
                controller: controller.tabviewController,
                tabs: [
                  Tab(
                    child: Text(
                      "lbl_income".tr,
                    ),
                  ),
                  Tab(
                    child: Text(
                      "lbl_expenses".tr,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: controller.tabviewController,
                children: [
                  StatisticIncomePage(),
                  StatisticExpensesPage(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
      title: AppbarSubtitle(
        text: "lbl_statistic".tr,
        margin: EdgeInsets.only(left: 20.h),
      ),
    );
  }
}
