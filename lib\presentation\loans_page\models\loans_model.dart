import '../../../core/app_export.dart';
import 'loans_item_model.dart';

/// This class defines the variables used in the [loans_page],
/// and is typically used to hold data that is passed between different parts of the application.
class LoansModel {
  Rx<List<LoansItemModel>> loansItemList = Rx([
    LoansItemModel(
        price: "\$2000.00".obs,
        repay: "Repay".obs,
        rate: "Rate".obs,
        fifteen: "15%".obs,
        perioud: "Period".obs,
        duration: "20 Month".obs,
        monthlyPayment: "Monthly payment".obs,
        price1: "\$2000.00".obs,
        totalPeriod: "Total period".obs,
        price2: "\$2500.00".obs),
    LoansItemModel(
        price: "\$3000.00".obs,
        repay: "Repay".obs,
        rate: "Rate".obs,
        fifteen: "10%".obs,
        perioud: "Period".obs,
        duration: "24 Month".obs,
        monthlyPayment: "Monthly payment".obs,
        price1: "\$3000.00".obs,
        totalPeriod: "Total period".obs,
        price2: "\$3500.00".obs),
    LoansItemModel(
        price: "\$4000.00".obs,
        repay: "Repay".obs,
        rate: "Rate".obs,
        fifteen: "21%".obs,
        perioud: "Period".obs,
        duration: "25 Month".obs,
        monthlyPayment: "Monthly payment".obs,
        price1: "\$3000.00".obs,
        totalPeriod: "Total period".obs,
        price2: "\$2500.00".obs)
  ]);
}
