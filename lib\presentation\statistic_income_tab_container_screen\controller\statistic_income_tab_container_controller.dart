import '../../../core/app_export.dart';
import '../models/statistic_income_tab_container_model.dart';
import 'package:flutter/material.dart';

/// A controller class for the StatisticIncomeTabContainerScreen.
///
/// This class manages the state of the StatisticIncomeTabContainerScreen, including the
/// current statisticIncomeTabContainerModelObj
class StatisticIncomeTabContainerController extends GetxController
    with GetSingleTickerProviderStateMixin {
  Rx<StatisticIncomeTabContainerModel> statisticIncomeTabContainerModelObj =
      StatisticIncomeTabContainerModel().obs;

  late TabController tabviewController =
      Get.put(TabController(vsync: this, length: 2));
}
