import '../../../core/app_export.dart';
import 'welcome1_item_model.dart';
import 'card_item_model.dart';
import 'homecardslider_item_model.dart';

/// This class defines the variables used in the [home_card_slider_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class HomeCardSliderModel {
  Rx<List<Welcome1ItemModel>> welcome1ItemList = Rx([
    Welcome1ItemModel(
        welcome: "Welcome".obs,
        johnAbram: "<PERSON> abram".obs,
        bellSixtyOne: ImageConstant.imgBell61.obs)
  ]);

  Rx<List<CardItemModel>> cardItemList =
      Rx(List.generate(1, (index) => CardItemModel()));

  Rx<List<HomecardsliderItemModel>> homecardsliderItemList = Rx([
    HomecardsliderItemModel(
      financialtransaction: "Financial transaction".obs,
      duration: "Today, 1:20 Pm".obs,
      price: "\$52.00".obs,
      image: ImageConstant.imgCallReceivedF.obs,
    ),
    HomecardsliderItemModel(
        financialtransaction: "Loan deposite".obs,
        duration: "20 March, 2:10 Am".obs,
        image: ImageConstant.imgCallDialingF.obs,
        price: "\$120.00".obs),
    HomecardsliderItemModel(
        financialtransaction: "Withdraw ATM".obs,
        duration: "4 January, 4:00 Am".obs,
        image: ImageConstant.imgCallDialingF.obs,
        price: "\$100.00".obs)
  ]);
}
