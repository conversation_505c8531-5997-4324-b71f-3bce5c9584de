import '../../../core/app_export.dart';
import '../models/confirm_payment_six_model.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter/material.dart';

/// A controller class for the ConfirmPaymentSixScreen.
///
/// This class manages the state of the ConfirmPaymentSixScreen, including the
/// current confirmPaymentSixModelObj
class ConfirmPaymentSixController extends GetxController with CodeAutoFill {
 Rx<TextEditingController> otpController = TextEditingController().obs;

 Rx<ConfirmPaymentSixModel> confirmPaymentSixModelObj =
     ConfirmPaymentSixModel().obs;

 @override
 void codeUpdated() {
  otpController.value.text = code ?? '';
 }

 void clearText() {
  otpController = TextEditingController(text: "").obs;
 }

 @override
 void onInit() {
  super.onInit();
  listenForCode();
 }
}
