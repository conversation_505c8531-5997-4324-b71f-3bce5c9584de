import '../../../core/app_export.dart';/// This class is used in the [notification_item_widget] screen.
class NotificationItemModel {NotificationItemModel({this.airplaneModeIs, this.justNow, this.id, }) { airplaneModeIs = airplaneModeIs  ?? Rx("Airplane mode is on either system or lo.");justNow = justNow  ?? Rx("Just now");id = id  ?? Rx(""); }

Rx<String>? airplaneModeIs;

Rx<String>? justNow;

Rx<String>? id;

 }
