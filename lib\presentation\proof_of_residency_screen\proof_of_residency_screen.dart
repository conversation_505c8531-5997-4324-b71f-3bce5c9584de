import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'models/proofofresidency_item_model.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/proof_of_residency_controller.dart';

class ProofOfResidencyScreen extends GetWidget<ProofOfResidencyController> {
  const ProofOfResidencyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _buildAppBar(),
        body: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 24.v),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              _buildIndonesia(),
              Sized<PERSON><PERSON>(height: 26.v),
              Text("msg_method_of_verification".tr,
                  style: CustomTextStyles.titleLargeBlack900_1),
              SizedBox(height: 19.v),
              _buildProofOfResidency()
            ])),
        bottomNavigationBar: _buildButtons());
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "msg_proof_of_residency".tr,
            margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildIndonesia() {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 17.v),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(children: [
          CustomImageView(
              imagePath: ImageConstant.imgIndonesia, height: 30.v, width: 41.h),
          Padding(
              padding: EdgeInsets.only(left: 12.h, top: 4.v, bottom: 4.v),
              child: Text("lbl_indonesia".tr,
                  style: CustomTextStyles.bodyLargeBlack900)),
          Spacer(),
          Padding(
              padding: EdgeInsets.only(top: 6.v, bottom: 2.v),
              child: Text("lbl_change".tr,
                  style: CustomTextStyles.bodyLargePrimary))
        ]));
  }

  /// Section Widget
  Widget _buildProofOfResidency() {
    return GetBuilder<ProofOfResidencyController>(
        init: ProofOfResidencyController(),
        builder: (controller) {
          return ListView.separated(
              physics: NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              separatorBuilder: (context, index) {
                return SizedBox(height: 16.v);
              },
              itemCount: controller.proofOfResidencyModelObj.value
                  .proofofresidencyItemList.value.length,
              itemBuilder: (context, index) {
                ProofofresidencyItemModel model = controller
                    .proofOfResidencyModelObj
                    .value
                    .proofofresidencyItemList
                    .value[index];
                return GestureDetector(
                  onTap: () {
                    controller.selected.value = index;
                    controller.update();
                  },
                  child: Container(
                    padding: EdgeInsets.all(16.h),
                    decoration: AppDecoration.outlineBlack.copyWith(
                      borderRadius: BorderRadiusStyle.roundedBorder12,
                    ),
                    child: Row(
                      children: [
                        CustomImageView(
                          imagePath: controller.selected.value == index
                              ? ImageConstant.selectedRadio
                              : ImageConstant.unSelectedRadio,
                          height: 24.v,
                          width: 25.h,
                          margin: EdgeInsets.symmetric(vertical: 11.v),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 16.h),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                model.passport1!.value,
                                style: theme.textTheme.titleMedium,
                              ),
                              SizedBox(height: 3.v),
                              Text(
                                model.issuedInIndia!.value,
                                style: theme.textTheme.bodyLarge,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              });
        });
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
        margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
        decoration: AppDecoration.fillWhiteA,
        child: CustomElevatedButton(
            text: "lbl_continue".tr,
            onPressed: () {
              onTapContinue();
            }));
  }

  /// Navigates to the createPinScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the photoIdCardScreen when the action is triggered.
  onTapContinue() {
    Get.toNamed(
      AppRoutes.photoIdCardScreen,
    );
  }
}
