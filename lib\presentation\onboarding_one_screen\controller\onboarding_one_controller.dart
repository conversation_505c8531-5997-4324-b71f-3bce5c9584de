import '../../../core/app_export.dart';
import '../models/onboarding_one_model.dart';

/// A controller class for the OnboardingOneScreen.
///
/// This class manages the state of the OnboardingOneScreen, including the
/// current onboardingOneModelObj
class OnboardingOneController extends GetxController {
  Rx<OnboardingOneModel> onboardingOneModelObj = OnboardingOneModel().obs;

  Rx<int> sliderIndex = 0.obs;

  RxInt selectedPos = 0.obs;

  changeIndex(int i) {
    selectedPos.value = i;
    update();
  }

  int currentPage = 0;

  void setCurrentPage(int value) {
    currentPage = value;
    update();
  }
}
