class BusinessAccount {
  final String id;
  final String businessName;
  final String businessType;
  final String ownerName;
  final String email;
  final String phone;
  final String address;
  final String city;
  final String state;
  final String country;
  final String postalCode;
  final double latitude;
  final double longitude;
  final String businessLicense;
  final String taxId;
  final String bankAccountNumber;
  final String routingNumber;
  final double balance;
  final List<String> categories;
  final List<String> products;
  final List<String> childAccounts;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime updatedAt;

  BusinessAccount({
    required this.id,
    required this.businessName,
    required this.businessType,
    required this.ownerName,
    required this.email,
    required this.phone,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    required this.postalCode,
    required this.latitude,
    required this.longitude,
    required this.businessLicense,
    required this.taxId,
    required this.bankAccountNumber,
    required this.routingNumber,
    required this.balance,
    required this.categories,
    required this.products,
    required this.childAccounts,
    required this.isVerified,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BusinessAccount.fromJson(Map<String, dynamic> json) {
    return BusinessAccount(
      id: json['id'] ?? '',
      businessName: json['businessName'] ?? '',
      businessType: json['businessType'] ?? '',
      ownerName: json['ownerName'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      city: json['city'] ?? '',
      state: json['state'] ?? '',
      country: json['country'] ?? '',
      postalCode: json['postalCode'] ?? '',
      latitude: json['latitude']?.toDouble() ?? 0.0,
      longitude: json['longitude']?.toDouble() ?? 0.0,
      businessLicense: json['businessLicense'] ?? '',
      taxId: json['taxId'] ?? '',
      bankAccountNumber: json['bankAccountNumber'] ?? '',
      routingNumber: json['routingNumber'] ?? '',
      balance: json['balance']?.toDouble() ?? 0.0,
      categories: List<String>.from(json['categories'] ?? []),
      products: List<String>.from(json['products'] ?? []),
      childAccounts: List<String>.from(json['childAccounts'] ?? []),
      isVerified: json['isVerified'] ?? false,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'businessName': businessName,
      'businessType': businessType,
      'ownerName': ownerName,
      'email': email,
      'phone': phone,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postalCode': postalCode,
      'latitude': latitude,
      'longitude': longitude,
      'businessLicense': businessLicense,
      'taxId': taxId,
      'bankAccountNumber': bankAccountNumber,
      'routingNumber': routingNumber,
      'balance': balance,
      'categories': categories,
      'products': products,
      'childAccounts': childAccounts,
      'isVerified': isVerified,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  BusinessAccount copyWith({
    String? id,
    String? businessName,
    String? businessType,
    String? ownerName,
    String? email,
    String? phone,
    String? address,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    double? latitude,
    double? longitude,
    String? businessLicense,
    String? taxId,
    String? bankAccountNumber,
    String? routingNumber,
    double? balance,
    List<String>? categories,
    List<String>? products,
    List<String>? childAccounts,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BusinessAccount(
      id: id ?? this.id,
      businessName: businessName ?? this.businessName,
      businessType: businessType ?? this.businessType,
      ownerName: ownerName ?? this.ownerName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      businessLicense: businessLicense ?? this.businessLicense,
      taxId: taxId ?? this.taxId,
      bankAccountNumber: bankAccountNumber ?? this.bankAccountNumber,
      routingNumber: routingNumber ?? this.routingNumber,
      balance: balance ?? this.balance,
      categories: categories ?? this.categories,
      products: products ?? this.products,
      childAccounts: childAccounts ?? this.childAccounts,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
} 