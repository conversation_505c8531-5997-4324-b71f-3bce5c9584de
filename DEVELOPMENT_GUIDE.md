# KojaPay Development Guide

## 🚀 Quick Start

### Prerequisites
- Flutter SDK 3.1.0+
- Android Studio / VS Code
- Git
- Android SDK (API 21+)

### Setup
1. Clone the repository
2. Run `flutter pub get`
3. Run `flutter run`

## 🏗️ Architecture Overview

### GetX Pattern
The app follows GetX architecture with:
- **Controllers**: Business logic and state management
- **Views**: UI screens
- **Models**: Data structures
- **Services**: API and external integrations

### Project Structure
```
lib/
├── core/           # Core utilities, constants, errors
├── data/           # Data layer (API, models, repositories)
├── presentation/   # UI screens and controllers
├── routes/         # Navigation
├── theme/          # App theming and styling
└── widgets/        # Reusable components
```

## 🎨 Theme System

### Enhanced Theme Helper
Located at `lib/theme/enhanced_theme_helper.dart`

#### Features:
- 8 color schemes (Primary, Ocean, Sunset, Forest, Purple, Golden, Coral, Emerald)
- Light/Dark/Auto theme modes
- Real-time theme switching
- Persistent theme storage

#### Usage:
```dart
// Change theme
enhancedThemeHelper.changeTheme('dark');

// Change color scheme
enhancedThemeHelper.changeColorScheme('ocean');

// Get current theme data
ThemeData theme = enhancedThemeHelper.getThemeData();
```

#### Adding New Color Schemes:
1. Add scheme name to `availableColorSchemes`
2. Add color scheme in `_getColorScheme()` method
3. Add gradient colors in `_getSchemeColors()`

## 📱 Screen Development

### Creating New Screens
1. Create folder in `lib/presentation/`
2. Add screen file, controller, and binding
3. Update routes in `app_routes.dart`

### Example Screen Structure:
```dart
// screen.dart
class MyScreen extends GetWidget<MyController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('My Screen')),
      body: Container(),
    );
  }
}

// controller.dart
class MyController extends GetxController {
  // State variables
  var isLoading = false.obs;
  
  // Methods
  void loadData() {
    // Implementation
  }
}

// binding.dart
class MyBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => MyController());
  }
}
```

## 🔧 State Management

### GetX Controllers
```dart
class ExampleController extends GetxController {
  // Observable variables
  var count = 0.obs;
  var isLoading = false.obs;
  var data = <String>[].obs;
  
  // Computed values
  String get displayText => 'Count: $count';
  
  // Methods
  void increment() => count++;
  
  void loadData() async {
    isLoading.value = true;
    try {
      // API call
      data.value = await apiService.getData();
    } finally {
      isLoading.value = false;
    }
  }
}
```

### Reactive UI
```dart
Obx(() => Text('${controller.count}')),
Obx(() => controller.isLoading.value 
  ? CircularProgressIndicator() 
  : Text('Data loaded')),
```

## 🌐 API Integration

### API Client
Located at `lib/data/apiClient/api_client.dart`

### Example API Call:
```dart
class ApiService {
  final ApiClient _client = Get.find<ApiClient>();
  
  Future<List<Transaction>> getTransactions() async {
    try {
      final response = await _client.get('/transactions');
      return (response.data as List)
          .map((json) => Transaction.fromJson(json))
          .toList();
    } catch (e) {
      throw ApiException('Failed to load transactions');
    }
  }
}
```

## 🎯 UI Components

### Custom Widgets
Located in `lib/widgets/`

### Common Patterns:
```dart
// Custom button
class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      child: isLoading 
        ? CircularProgressIndicator() 
        : Text(text),
    );
  }
}

// Custom card
class CustomCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: padding ?? EdgeInsets.all(16),
        child: child,
      ),
    );
  }
}
```

## 🔒 Security

### Authentication
- PIN-based authentication
- Biometric support
- Session management
- Secure storage

### Data Protection
- Encrypted local storage
- Secure API communication
- Input validation
- Error handling

## 📊 Performance Optimization

### Best Practices:
1. **Lazy Loading**: Use `Get.lazyPut()` for controllers
2. **Image Caching**: Use `CachedNetworkImage`
3. **List Optimization**: Use `ListView.builder` for large lists
4. **Memory Management**: Dispose controllers properly
5. **Network Caching**: Implement offline support

### Memory Management:
```dart
class MyController extends GetxController {
  @override
  void onClose() {
    // Clean up resources
    super.onClose();
  }
}
```

## 🧪 Testing

### Unit Tests
```dart
// test/controllers/my_controller_test.dart
void main() {
  group('MyController', () {
    late MyController controller;
    
    setUp(() {
      controller = MyController();
    });
    
    test('should increment count', () {
      controller.increment();
      expect(controller.count.value, 1);
    });
  });
}
```

### Widget Tests
```dart
// test/widgets/my_widget_test.dart
void main() {
  testWidgets('should display correct text', (tester) async {
    await tester.pumpWidget(MyWidget());
    expect(find.text('Expected Text'), findsOneWidget);
  });
}
```

## 🚀 Deployment

### Android
```bash
# Build APK
flutter build apk --release

# Build App Bundle
flutter build appbundle --release
```

### iOS
```bash
# Build for iOS
flutter build ios --release
```

## 🔧 Development Tools

### VS Code Extensions:
- Flutter
- Dart
- GetX Snippets
- Flutter Widget Snippets

### Android Studio Plugins:
- Flutter Plugin
- Dart Plugin

## 📝 Code Style

### Naming Conventions:
- **Files**: snake_case (e.g., `my_screen.dart`)
- **Classes**: PascalCase (e.g., `MyScreen`)
- **Variables**: camelCase (e.g., `myVariable`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_BASE_URL`)

### Code Formatting:
```bash
# Format code
dart format .

# Analyze code
flutter analyze
```

## 🐛 Debugging

### Common Issues:
1. **Hot Reload Not Working**: Restart the app
2. **Dependencies Issues**: Run `flutter clean && flutter pub get`
3. **Build Errors**: Check Android SDK configuration
4. **Performance Issues**: Use Flutter Inspector

### Debug Tools:
- Flutter Inspector
- Performance Overlay
- Debug Console
- Network Inspector

## 📚 Resources

### Documentation:
- [Flutter Docs](https://flutter.dev/docs)
- [GetX Documentation](https://pub.dev/packages/get)
- [Dart Language Tour](https://dart.dev/guides/language/language-tour)

### Community:
- [Flutter Community](https://flutter.dev/community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/flutter)
- [Reddit r/FlutterDev](https://reddit.com/r/FlutterDev)

## 🤝 Contributing

### Guidelines:
1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Create meaningful commit messages
5. Test on multiple devices

### Pull Request Process:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

---

**Happy Coding! 🎉** 