import 'package:get/get.dart';

import '../../../core/utils/image_constant.dart';
import '../../../routes/app_routes.dart';

class CategorieModel {
  String? image;
  String? title;
  void Function()? action;

  CategorieModel(
    this.image,
    this.title,
    this.action,
  );
}

class CategoriesModel {
  static List<CategorieModel> getCategories() {
    return [
      CategorieModel(
        ImageConstant.category1st,
        "Scan",
        () {
          Get.toNamed(
            AppRoutes.scanQrToRideScreen,
          );
        },
      ),
      CategorieModel(
        ImageConstant.category2nd,
        "Top up",
        () {
          Get.toNamed(
            AppRoutes.topUpScreen,
          );
        },
      ),
      CategorieModel(
        ImageConstant.category3rd,
        "Withdraw",
        () {
          Get.toNamed(
            AppRoutes.withdrawScreen,
          );
        },
      ),
      CategorieModel(
        ImageConstant.category4th,
        "Transfer",
        () {
          Get.toNamed(
            AppRoutes.transferScreen,
          );
        },
      ),
    ];
  }
}
