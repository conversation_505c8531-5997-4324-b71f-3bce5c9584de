import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/data/models/business_account_model.dart';

class BusinessAccountController extends GetxController {
  // Form controllers
  final businessNameController = TextEditingController();
  final ownerNameController = TextEditingController();
  final businessTypeController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final addressController = TextEditingController();
  final cityController = TextEditingController();
  final stateController = TextEditingController();
  final countryController = TextEditingController();
  final postalCodeController = TextEditingController();
  final bankAccountController = TextEditingController();
  final routingNumberController = TextEditingController();
  final businessLicenseController = TextEditingController();
  final taxIdController = TextEditingController();

  // Observable variables
  var isLoading = false.obs;
  var currentLatitude = 0.0.obs;
  var currentLongitude = 0.0.obs;
  var isLocationLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    // Initialize with default values
    countryController.text = 'United States';
  }

  @override
  void onClose() {
    // Dispose controllers
    businessNameController.dispose();
    ownerNameController.dispose();
    businessTypeController.dispose();
    emailController.dispose();
    phoneController.dispose();
    addressController.dispose();
    cityController.dispose();
    stateController.dispose();
    countryController.dispose();
    postalCodeController.dispose();
    bankAccountController.dispose();
    routingNumberController.dispose();
    businessLicenseController.dispose();
    taxIdController.dispose();
    super.onClose();
  }

  /// Get current location and populate address fields
  Future<void> getCurrentLocation() async {
    try {
      isLocationLoading.value = true;
      
      // Check permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          Get.snackbar(
            'Location Permission',
            'Location permission is required to get your current location',
            snackPosition: SnackPosition.BOTTOM,
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        Get.snackbar(
          'Location Permission',
          'Location permissions are permanently denied. Please enable in settings.',
          snackPosition: SnackPosition.BOTTOM,
        );
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      currentLatitude.value = position.latitude;
      currentLongitude.value = position.longitude;

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        addressController.text = '${place.street ?? ''} ${place.subThoroughfare ?? ''}'.trim();
        cityController.text = place.locality ?? '';
        stateController.text = place.administrativeArea ?? '';
        countryController.text = place.country ?? '';
        postalCodeController.text = place.postalCode ?? '';
      }

      Get.snackbar(
        'Location Updated',
        'Your current location has been set',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Location Error',
        'Failed to get current location: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLocationLoading.value = false;
    }
  }

  /// Upload business license document
  Future<void> uploadBusinessLicense() async {
    try {
      // TODO: Implement file picker for document upload
      Get.snackbar(
        'Upload License',
        'Business license upload feature will be implemented',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Upload Error',
        'Failed to upload business license: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Upload tax document
  Future<void> uploadTaxDocument() async {
    try {
      // TODO: Implement file picker for document upload
      Get.snackbar(
        'Upload Tax Document',
        'Tax document upload feature will be implemented',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Upload Error',
        'Failed to upload tax document: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Validate form fields
  bool _validateForm() {
    if (businessNameController.text.isEmpty) {
      Get.snackbar('Error', 'Business name is required');
      return false;
    }
    if (ownerNameController.text.isEmpty) {
      Get.snackbar('Error', 'Owner name is required');
      return false;
    }
    if (businessTypeController.text.isEmpty) {
      Get.snackbar('Error', 'Business type is required');
      return false;
    }
    if (emailController.text.isEmpty) {
      Get.snackbar('Error', 'Business email is required');
      return false;
    }
    if (phoneController.text.isEmpty) {
      Get.snackbar('Error', 'Business phone is required');
      return false;
    }
    if (addressController.text.isEmpty) {
      Get.snackbar('Error', 'Business address is required');
      return false;
    }
    if (cityController.text.isEmpty) {
      Get.snackbar('Error', 'City is required');
      return false;
    }
    if (stateController.text.isEmpty) {
      Get.snackbar('Error', 'State is required');
      return false;
    }
    if (countryController.text.isEmpty) {
      Get.snackbar('Error', 'Country is required');
      return false;
    }
    if (bankAccountController.text.isEmpty) {
      Get.snackbar('Error', 'Bank account number is required');
      return false;
    }
    if (routingNumberController.text.isEmpty) {
      Get.snackbar('Error', 'Routing number is required');
      return false;
    }
    return true;
  }

  /// Submit business account creation
  Future<void> submitBusinessAccount() async {
    if (!_validateForm()) return;

    try {
      isLoading.value = true;

      // Create business account object
      final businessAccount = BusinessAccount(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        businessName: businessNameController.text,
        businessType: businessTypeController.text,
        ownerName: ownerNameController.text,
        email: emailController.text,
        phone: phoneController.text,
        address: addressController.text,
        city: cityController.text,
        state: stateController.text,
        country: countryController.text,
        postalCode: postalCodeController.text,
        latitude: currentLatitude.value,
        longitude: currentLongitude.value,
        businessLicense: businessLicenseController.text,
        taxId: taxIdController.text,
        bankAccountNumber: bankAccountController.text,
        routingNumber: routingNumberController.text,
        balance: 0.0,
        categories: [],
        products: [],
        childAccounts: [],
        isVerified: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // TODO: Send to API
      await Future.delayed(Duration(seconds: 2)); // Simulate API call

      Get.snackbar(
        'Success',
        'Business account created successfully!',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );

      // Navigate to business dashboard
      Get.offAllNamed('/business_dashboard');
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to create business account: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }
} 