import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/data/models/account_tier_model.dart';
import 'package:payway/data/models/child_account_model.dart';
import 'controller/account_switcher_controller.dart';

class AccountSwitcherScreen extends GetWidget<AccountSwitcherController> {
  const AccountSwitcherScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Switch Account',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrentAccount(),
            Sized<PERSON>ox(height: 24.h),
            _buildAccountOptions(),
            SizedBox(height: 24.h),
            _buildQuickActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentAccount() {
    return Obx(() => Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_circle,
                  color: Theme.of(context).colorScheme.primary,
                  size: 32.w,
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Account',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        controller.currentAccount.value?.tierDisplayName ?? 'Basic',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12.h),
                  ),
                  child: Text(
                    'Active',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12.fSize,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Text(
              'Account Type: ${controller.currentAccount.value?.type.toString().split('.').last ?? 'Personal'}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildAccountOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Accounts',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        SizedBox(height: 16.h),
        _buildPersonalAccount(),
        SizedBox(height: 12.h),
        _buildBusinessAccount(),
        SizedBox(height: 12.h),
        _buildChildAccounts(),
      ],
    );
  }

  Widget _buildPersonalAccount() {
    return Obx(() => Card(
      child: InkWell(
        onTap: () => controller.switchToAccount(AccountType.personal),
        child: Container(
          padding: EdgeInsets.all(16.h),
          decoration: BoxDecoration(
            color: controller.currentAccount.value?.type == AccountType.personal
              ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
              : null,
            borderRadius: BorderRadius.circular(12.h),
            border: controller.currentAccount.value?.type == AccountType.personal
              ? Border.all(color: Theme.of(context).colorScheme.primary, width: 2)
              : null,
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12.h),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.h),
                ),
                child: Icon(
                  Icons.person,
                  color: Colors.blue,
                  size: 24.w,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Personal Account',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      'Individual account for personal use',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'Balance: ₦${controller.personalBalance.value.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
              if (controller.currentAccount.value?.type == AccountType.personal)
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).colorScheme.primary,
                ),
            ],
          ),
        ),
      ),
    ));
  }

  Widget _buildBusinessAccount() {
    return Obx(() => Card(
      child: InkWell(
        onTap: controller.hasBusinessAccount.value 
          ? () => controller.switchToAccount(AccountType.business)
          : () => controller.createBusinessAccount(),
        child: Container(
          padding: EdgeInsets.all(16.h),
          decoration: BoxDecoration(
            color: controller.currentAccount.value?.type == AccountType.business
              ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
              : null,
            borderRadius: BorderRadius.circular(12.h),
            border: controller.currentAccount.value?.type == AccountType.business
              ? Border.all(color: Theme.of(context).colorScheme.primary, width: 2)
              : null,
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(12.h),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.h),
                ),
                child: Icon(
                  Icons.business,
                  color: Colors.green,
                  size: 24.w,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Business Account',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      controller.hasBusinessAccount.value
                        ? 'Business account with advanced features'
                        : 'Create a business account',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    if (controller.hasBusinessAccount.value) ...[
                      SizedBox(height: 8.h),
                      Text(
                        'Balance: ₦${controller.businessBalance.value.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (controller.currentAccount.value?.type == AccountType.business)
                Icon(
                  Icons.check_circle,
                  color: Theme.of(context).colorScheme.primary,
                )
              else if (!controller.hasBusinessAccount.value)
                Icon(
                  Icons.add_circle_outline,
                  color: Colors.grey[400],
                ),
            ],
          ),
        ),
      ),
    ));
  }

  Widget _buildChildAccounts() {
    return Obx(() => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Child Accounts',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            IconButton(
              onPressed: () => controller.createChildAccount(),
              icon: Icon(Icons.add_circle_outline),
              tooltip: 'Add Child Account',
            ),
          ],
        ),
        SizedBox(height: 8.h),
        if (controller.childAccounts.isEmpty)
          Card(
            child: Padding(
              padding: EdgeInsets.all(16.h),
              child: Column(
                children: [
                  Icon(
                    Icons.child_care,
                    size: 48.w,
                    color: Colors.grey[400],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'No child accounts yet',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'Create a child account with parental controls',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          )
        else
          ...controller.childAccounts.map((childAccount) => Card(
            margin: EdgeInsets.only(bottom: 8.h),
            child: InkWell(
              onTap: () => controller.switchToChildAccount(childAccount),
              child: Container(
                padding: EdgeInsets.all(16.h),
                decoration: BoxDecoration(
                  color: controller.currentChildAccount.value?.id == childAccount.id
                    ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                    : null,
                  borderRadius: BorderRadius.circular(12.h),
                  border: controller.currentChildAccount.value?.id == childAccount.id
                    ? Border.all(color: Theme.of(context).colorScheme.primary, width: 2)
                    : null,
                ),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(12.h),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8.h),
                      ),
                      child: Icon(
                        Icons.child_care,
                        color: Colors.orange,
                        size: 24.w,
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            childAccount.childName,
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            'Age: ${childAccount.age} years • ${childAccount.statusText}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            'Balance: ₦${childAccount.balance.toStringAsFixed(2)}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (controller.currentChildAccount.value?.id == childAccount.id)
                      Icon(
                        Icons.check_circle,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                  ],
                ),
              ),
            ),
          )),
      ],
    ));
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        SizedBox(height: 16.h),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                title: 'Upgrade Account',
                description: 'Upgrade to premium or business',
                icon: Icons.upgrade,
                color: Colors.blue,
                onTap: () => controller.upgradeAccount(),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildActionCard(
                title: 'Create Business',
                description: 'Set up business account',
                icon: Icons.business,
                color: Colors.green,
                onTap: () => controller.createBusinessAccount(),
              ),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                title: 'Add Child',
                description: 'Create child account',
                icon: Icons.child_care,
                color: Colors.orange,
                onTap: () => controller.createChildAccount(),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildActionCard(
                title: 'Account Settings',
                description: 'Manage account preferences',
                icon: Icons.settings,
                color: Colors.purple,
                onTap: () => controller.openAccountSettings(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.all(16.h),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(12.h),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8.h),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24.w,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4.h),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
} 