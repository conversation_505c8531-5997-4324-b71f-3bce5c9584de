import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/confirm_four_controller.dart';

class ConfirmFourScreen extends GetWidget<ConfirmFourController> {
  const ConfirmFourScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 19.v),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("lbl_tranfer_to".tr,
                  style: CustomTextStyles.titleLargeBlack900_1),
              Sized<PERSON>ox(height: 19.v),
              _buildMasterCard(),
              SizedBox(height: 24.v),
              _buildTopupAmout(),
              SizedBox(height: 5.v),
            ],
          ),
        ),
        bottomNavigationBar: _buildButtons());
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_confirm".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildMasterCard() {
    return Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(children: [
          CustomIconButton(
              height: 55.adaptSize,
              width: 55.adaptSize,
              padding: EdgeInsets.all(7.h),
              decoration: IconButtonStyleHelper.fillGrayTL12,
              child: CustomImageView(imagePath: ImageConstant.imgMasterCard)),
          Padding(
              padding: EdgeInsets.only(left: 12.h, top: 16.v, bottom: 17.v),
              child: Text("msg_2541".tr, style: theme.textTheme.bodyLarge))
        ]));
  }

  /// Section Widget
  Widget _buildTopupAmout() {
    return Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Padding(
              padding: EdgeInsets.only(top: 3.v, bottom: 1.v),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("lbl_topup_amout".tr,
                        style: CustomTextStyles.titleSmallBlack900),
                    SizedBox(height: 19.v),
                    Text("lbl_total".tr,
                        style: CustomTextStyles.titleSmallBlack900)
                  ])),
          Column(children: [
            Text("lbl_500_00".tr, style: CustomTextStyles.bodyLargeBlack900),
            SizedBox(height: 18.v),
            Text("lbl_500_00".tr, style: CustomTextStyles.bodyLargeBlack900)
          ])
        ]));
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
        margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
        decoration: AppDecoration.fillWhiteA,
        child: CustomElevatedButton(
            text: "lbl_continue".tr,
            onPressed: () {
              onTapContinue();
            }));
  }

  /// Navigates to the selectBankPopupFourScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the confirmPaymentFourScreen when the action is triggered.
  onTapContinue() {
    Get.toNamed(
      AppRoutes.confirmPaymentFourScreen,
    );
  }
}
