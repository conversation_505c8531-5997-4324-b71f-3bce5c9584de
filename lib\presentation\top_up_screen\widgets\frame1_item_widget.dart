import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class Frame1ItemWidget extends StatelessWidget {
  Frame1ItemWidget({
    Key? key,
    required this.text,
    required this.color,
    required this.bgColor,
  }) : super(
          key: key,
        );

  final String text;
  final Color color;
  final Color bgColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 53.h,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12.h),
        border: Border.all(
          color: color,
          width: 2.h,
        ),
      ),
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            color: Color(0xFF000000),
            fontSize: 15.fSize,
            fontFamily: 'Satoshi',
            fontWeight: FontWeight.w400,
          ),
        ),
      ),
    );
  }
}

// import '../models/frame2_item_model.dart';
// import 'package:flutter/material.dart';
// import 'package:quickrent_app/core/app_export.dart';
//
// // ignore: must_be_immutable
// class Frame2ItemWidget extends StatelessWidget {
//   Frame2ItemWidget(
//     {
//     Key? key, required this.text, required this.color,
//   }) : super(
//           key: key,
//         );
// final String text;
// final Color color;
//
//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       width: double.infinity,
//       height: 53.h,
//       decoration: BoxDecoration(
//         color: Color(0xFFF8F8F8),
//         borderRadius: BorderRadius.circular(12.h),
//         border: Border.all(
//           color: color,
//           width: 1.h,
//         ),
//       ),
//       child: Center(
//         child: Text(
//           text,
//           style: TextStyle(
//             color: Color(0xFF000000),
//             fontSize: 15.fSize,
//             fontFamily: 'Satoshi',
//             fontWeight: FontWeight.w400,
//           ),
//
//         ),
//       ),
//     );
//   }
// }
