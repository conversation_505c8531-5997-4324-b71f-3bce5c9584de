import '../models/card_item_model.dart';
import '../controller/home_card_slider_controller.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class CardItemWidget extends StatelessWidget {
  CardItemWidget(
    this.cardItemModelObj, {
    Key? key,
    this.onTapTxtAddNewCard,
  }) : super(
          key: key,
        );

  CardItemModel cardItemModelObj;

  var controller = Get.find<HomeCardSliderController>();

  VoidCallback? onTapTxtAddNewCard;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomLeft,
      child: Container(
        margin: EdgeInsets.only(
          left: 350.h,
          right: 20.h,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 101.h,
          vertical: 41.v,
        ),
        decoration: AppDecoration.fillWhiteA.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder12,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "lbl_no_cards_saved".tr,
              style: theme.textTheme.titleMedium,
            ),
            SizedBox(height: 11.v),
            GestureDetector(
              onTap: () {
                onTapTxtAddNewCard!.call();
              },
              child: Text(
                "lbl_add_new_card".tr,
                style: CustomTextStyles.bodyLargePrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
