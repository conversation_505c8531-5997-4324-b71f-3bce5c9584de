// ignore_for_file: must_be_immutable

import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'package:pinput/pinput.dart';
import 'controller/enter_your_pin_controller.dart';
import 'package:payway/presentation/withdrawal_success_dialog/withdrawal_success_dialog.dart';
import 'package:payway/presentation/withdrawal_success_dialog/controller/withdrawal_success_controller.dart';

class EnterYourPinScreen extends GetWidget<EnterYourPinController> {
  EnterYourPinScreen({Key? key}) : super(key: key);

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: _buildAppBar(),
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 18.v),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("msg_please_enter_pin".tr,
                    style: CustomTextStyles.bodyLargeBlack900),
                SizedBox(height: 39.v),
                Pinput(
                  errorText: "Please enter OTP",
                  errorTextStyle: TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 16.fSize,
                    color: appTheme.error,
                  ),
                  textInputAction: TextInputAction.done,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  disabledPinTheme: PinTheme(
                      textStyle: TextStyle(
                        fontWeight: FontWeight.w400,
                        fontSize: 24.fSize,
                        color: appTheme.black900,
                      ),
                      padding: EdgeInsets.only(left: 9.h, right: 9.h),
                      decoration: BoxDecoration(color: appTheme.error)),
                  controller: controller.otpController.value,
                  length: 4,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Please enter a valid code";
                    }
                    return null;
                  },
                  errorPinTheme: PinTheme(
                    padding: EdgeInsets.only(left: 12.h, right: 12.h),
                    decoration: BoxDecoration(
                      color: appTheme.textfeild,
                      // border: Border.all(color: appTheme.textfeild),
                      borderRadius: BorderRadius.circular(16.h),
                    ),
                    textStyle: TextStyle(
                      color: appTheme.error,
                      fontSize: 14.fSize,
                      fontWeight: FontWeight.w400,
                    ),
                    width: 71.h,
                    height: 71.h,
                  ),
                  defaultPinTheme: PinTheme(
                    padding: EdgeInsets.only(left: 12.h, right: 12.h),
                    width: 71.h,
                    height: 71.h,
                    textStyle: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 24.fSize,
                      color: appTheme.black900,
                    ),
                    decoration: BoxDecoration(
                      color: appTheme.textfeild,
                      borderRadius: BorderRadius.circular(16.h),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: _buildButtons());
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_enter_your_pin".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
      margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
      decoration: AppDecoration.fillWhiteA,
      child: CustomElevatedButton(
        text: "lbl_confirm_payment".tr,
        onPressed: () {
          if (_formKey.currentState!.validate()) {
            onTapConfirmPayment();
          }
        },
      ),
    );
  }

  /// Navigates to the confirmScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Displays a dialog with the [WithdrawalSuccessDialog] content.
  onTapConfirmPayment() {
    controller.clearText();
    controller.update();
    Get.dialog(AlertDialog(
      backgroundColor: Colors.transparent,
      contentPadding: EdgeInsets.zero,
      insetPadding: const EdgeInsets.only(left: 0),
      content: WithdrawalSuccessDialog(
        Get.put(
          WithdrawalSuccessController(),
        ),
      ),
    ));
  }
}
