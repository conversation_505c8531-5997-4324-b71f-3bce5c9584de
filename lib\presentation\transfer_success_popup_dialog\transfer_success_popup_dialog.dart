import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/transfer_success_popup_controller.dart';

// ignore_for_file: must_be_immutable
class TransferSuccessPopupDialog extends StatelessWidget {
  TransferSuccessPopupDialog(this.controller, {Key? key}) : super(key: key);

  TransferSuccessPopupController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 388.h,
      padding: EdgeInsets.symmetric(horizontal: 24.h, vertical: 32.v),
      decoration: AppDecoration.fillWhiteA
          .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomImageView(
              imagePath: ImageConstant.passwordChanged,
              height: 116.v,
              width: 116.h,
              fit: BoxFit.contain,
              alignment: Alignment.center),
          SizedBox(height: 19.v),
          Text("msg_transfer_success".tr,
              style: CustomTextStyles.titleLargeBlack900),
          Sized<PERSON><PERSON>(height: 8.v),
          SizedBox(
              // width: 229.h,
              child: Text("msg_your_money_has_been".tr,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
          SizedBox(height: 37.v),
          CustomElevatedButton(
            text: "lbl_see_details".tr,
            onPressed: () {
              onTapSeeDetails();
            },
          ),
        ],
      ),
    );
  }

  /// Navigates to the detailsOneScreen when the action is triggered.
  onTapSeeDetails() {
    Get.toNamed(
      AppRoutes.detailsOneScreen,
    );
  }
}
