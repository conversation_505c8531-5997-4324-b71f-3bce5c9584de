import '../../../core/app_export.dart';
import '../models/recent_transactions_model.dart';
import '../models/transfer_model.dart';
import 'package:flutter/material.dart';

/// A controller class for the TransferScreen.
///
/// This class manages the state of the TransferScreen, including the
/// current transferModelObj
class TransferController extends GetxController {
  TextEditingController searchController = TextEditingController();

  Rx<TransferModel> transferModelObj = TransferModel().obs;

  List<RecentTransactionModel> recentTransactionList =
      AllRecentTransactionsModel.getCategories();

  @override
  void onClose() {
    super.onClose();
    searchController.dispose();
  }
}
