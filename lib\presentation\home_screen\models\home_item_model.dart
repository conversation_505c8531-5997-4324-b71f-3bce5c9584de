import '../../../core/app_export.dart';/// This class is used in the [home_item_widget] screen.
class HomeItemModel {HomeItemModel({this.financialtransaction, this.duration, this.price, this.id, }) { financialtransaction = financialtransaction  ?? Rx("Financial transaction");duration = duration  ?? Rx("Today, 1:20 Pm");price = price  ?? Rx("52.00");id = id  ?? Rx(""); }

Rx<String>? financialtransaction;

Rx<String>? duration;

Rx<String>? price;

Rx<String>? id;

 }
