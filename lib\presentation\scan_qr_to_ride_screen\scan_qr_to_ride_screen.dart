import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_title.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/scan_qr_to_ride_controller.dart';

class ScanQrToRideScreen extends GetWidget<ScanQrToRideController> {
  const ScanQrToRideScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: true,
      appBar: _buildAppBar(),
      body: Container(
        width: SizeUtils.width,
        height: SizeUtils.height,
        padding: EdgeInsets.only(top: 56.v),
        decoration: BoxDecoration(
            color: appTheme.whiteA700,
            image: DecorationImage(
                image: AssetImage(ImageConstant.imgScanQrToRide),
                fit: BoxFit.cover)),
        child: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(horizontal: 8.h, vertical: 72.v),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              SizedBox(height: 73.v),
              _buildScan(),
              Spacer(),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 74.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                        padding: EdgeInsets.symmetric(vertical: 4.v),
                        child: CustomIconButton(
                            height: 64.adaptSize,
                            width: 64.adaptSize,
                            padding: EdgeInsets.all(15.h),
                            decoration: IconButtonStyleHelper.fillBlack,
                            child: CustomImageView(
                                imagePath: ImageConstant.imgFlashOffFill0))),
                    Spacer(flex: 50),
                    CustomIconButton(
                        height: 72.adaptSize,
                        width: 72.adaptSize,
                        padding: EdgeInsets.all(16.h),
                        decoration: IconButtonStyleHelper.fillWhiteATL36,
                        onTap: () {
                          onTapBtnQrCodeScanner();
                        },
                        child: CustomImageView(
                            imagePath: ImageConstant.imgQrCodeScanner)),
                    Spacer(flex: 50),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 4.v),
                      child: CustomIconButton(
                        height: 64.adaptSize,
                        width: 64.adaptSize,
                        padding: EdgeInsets.all(15.h),
                        decoration: IconButtonStyleHelper.fillBlack,
                        child: CustomImageView(
                          imagePath: ImageConstant.imgAutorenewFill0WhiteA700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        height: 61.v,
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFilWhiteA700,
            margin: EdgeInsets.only(left: 20.h, top: 11.v, bottom: 12.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarTitle(
            text: "lbl_scan_qr_code".tr, margin: EdgeInsets.only(left: 16.h)));
  }

  /// Section Widget
  Widget _buildScan() {
    return SizedBox(
      height: 425.v,
      width: 412.h,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CustomImageView(
              imagePath: ImageConstant.imgFullShotBlogg,
              height: 324.adaptSize,
              width: 324.adaptSize,
              radius: BorderRadius.circular(16.h),
              alignment: Alignment.center),
          CustomImageView(
            imagePath: ImageConstant.imgCardScan,
            height: 425.v,
            width: 412.h,
            fit: BoxFit.fill,
            alignment: Alignment.center,
          ),
        ],
      ),
    );
  }

  /// Navigates to the homeCardSliderScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the transferDetailsTwoScreen when the action is triggered.
  onTapBtnQrCodeScanner() {
    Get.toNamed(
      AppRoutes.transferDetailsTwoScreen,
    );
  }
}
