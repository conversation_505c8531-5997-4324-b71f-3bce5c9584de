import '../../../core/app_export.dart';
import '../models/create_pin_model.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter/material.dart';

/// A controller class for the CreatePinScreen.
///
/// This class manages the state of the CreatePinScreen, including the
/// current createPinModelObj
class CreatePinController extends GetxController with CodeAutoFill {
  Rx<TextEditingController> otpController = TextEditingController().obs;

  Rx<CreatePinModel> createPinModelObj = CreatePinModel().obs;

  @override
  void codeUpdated() {
    otpController.value.text = code ?? '';
  }

  void clearValue() {
    otpController = TextEditingController(text: "").obs;
  }

  @override
  void onInit() {
    super.onInit();
    listenForCode();
  }
}
