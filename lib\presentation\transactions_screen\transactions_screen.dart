import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'widgets/transactions_item_widget.dart';
import 'models/transactions_item_model.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/transactions_controller.dart';

class TransactionsScreen extends GetWidget<TransactionsController> {
  const TransactionsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Padding(
        padding: EdgeInsets.only(left: 20.h, top: 16.v, right: 20.h),
        child: Obx(
          () => ListView.separated(
            physics: BouncingScrollPhysics(),
            shrinkWrap: true,
            separatorBuilder: (context, index) {
              return SizedBox(height: 16.v);
            },
            itemCount: controller
                .transactionsModelObj.value.transactionsItemList.value.length,
            itemBuilder: (context, index) {
              TransactionsItemModel model = controller
                  .transactionsModelObj.value.transactionsItemList.value[index];
              return TransactionsItemWidget(
                model,
                onTapTransactions: () {
                  onTapTransactions();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
          text: "lbl_transactions2".tr,
          margin: EdgeInsets.only(left: 16.h),
        ),
        styleType: Style.bgFill);
  }

  /// Navigates to the homeCardSliderScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the transactionsDetailsScreen when the action is triggered.
  onTapTransactions() {
    Get.toNamed(
      AppRoutes.transactionsDetailsScreen,
    );
  }
}
