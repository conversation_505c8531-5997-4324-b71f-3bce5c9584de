import '../../../core/app_export.dart';
import '../models/confirm_payment_one_model.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:flutter/material.dart';

/// A controller class for the ConfirmPaymentOneScreen.
///
/// This class manages the state of the ConfirmPaymentOneScreen, including the
/// current confirmPaymentOneModelObj
class ConfirmPaymentOneController extends GetxController with CodeAutoFill {
 // Rx<TextEditingController> otpController = TextEditingController().obs;

 Rx<ConfirmPaymentOneModel> confirmPaymentOneModelObj =
     ConfirmPaymentOneModel().obs;

 TextEditingController otpController = TextEditingController();

 void clearText() {
  otpController = TextEditingController(text: "");
 }

 @override
 void codeUpdated() {
  otpController.text = code ?? '';
 }

 @override
 void onInit() {
  super.onInit();
  listenForCode();
 }
}
