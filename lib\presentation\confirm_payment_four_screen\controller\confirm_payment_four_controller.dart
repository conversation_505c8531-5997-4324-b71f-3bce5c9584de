import '../../../core/app_export.dart';import '../models/confirm_payment_four_model.dart';import 'package:sms_autofill/sms_autofill.dart';import 'package:flutter/material.dart';/// A controller class for the ConfirmPaymentFourScreen.
///
/// This class manages the state of the ConfirmPaymentFourScreen, including the
/// current confirmPaymentFourModelObj
class ConfirmPaymentFourController extends GetxController with  CodeAutoFill {Rx<TextEditingController> otpController = TextEditingController().obs;

Rx<ConfirmPaymentFourModel> confirmPaymentFourModelObj = ConfirmPaymentFourModel().obs;

@override void codeUpdated() { otpController.value.text = code ?? ''; } 
@override void onInit() { super.onInit(); listenForCode(); } 
 }
