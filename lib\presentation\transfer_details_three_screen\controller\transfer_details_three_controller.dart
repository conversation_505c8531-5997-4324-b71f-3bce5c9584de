import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../models/transfer_details_three_model.dart';

/// A controller class for the TransferDetailsThreeScreen.
///
/// This class manages the state of the TransferDetailsThreeScreen, including the
/// current transferDetailsThreeModelObj
class TransferDetailsThreeController extends GetxController {
  Rx<TransferDetailsThreeModel> transferDetailsThreeModelObj =
      TransferDetailsThreeModel().obs;

  SelectionPopupModel? selectedDropDownValue;

  TextEditingController priceController = TextEditingController();

  onSelected(dynamic value) {
    for (var element
        in transferDetailsThreeModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    transferDetailsThreeModelObj.value.dropdownItemList.refresh();
  }
}
