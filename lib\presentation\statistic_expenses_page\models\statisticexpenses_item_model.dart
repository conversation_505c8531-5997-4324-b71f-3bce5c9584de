import '../../../core/app_export.dart';

/// This class is used in the [statisticexpenses_item_widget] screen.
class StatisticexpensesItemModel {
  StatisticexpensesItemModel({
    this.financialtransaction,
    this.duration,
    this.price,
    this.image,
    this.id,
  }) {
    financialtransaction = financialtransaction ?? Rx("Financial transaction");
    duration = duration ?? Rx("Today, 1:20 Pm");
    price = price ?? Rx("52.00");
    image = image ?? Rx(ImageConstant.imgCallReceivedF);
    id = id ?? Rx("");
  }

  Rx<String>? financialtransaction;

  Rx<String>? duration;

  Rx<String>? price;
  Rx<String>? image;

  Rx<String>? id;
}
