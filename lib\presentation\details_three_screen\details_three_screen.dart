import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:payway/widgets/app_bar/appbar_trailing_image.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'package:share_plus/share_plus.dart';
import 'controller/details_three_controller.dart';

class DetailsThreeScreen extends GetWidget<DetailsThreeController> {
  const DetailsThreeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildWajihTaysirHandal(),
            SizedBox(height: 28.v),
            Text("lbl_payment_with".tr,
                style: CustomTextStyles.titleLargeBlack900_1),
            SizedBox(height: 17.v),
            _buildMasterCard(),
            SizedBox(height: 24.v),
            _buildSummary(),
            SizedBox(height: 5.v),
          ],
        ),
      ),
      bottomNavigationBar: _buildButtons(),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_details".tr, margin: EdgeInsets.only(left: 16.h)),
        actions: [
          AppbarTrailingImage(
              imagePath: ImageConstant.imgShareFill0Wgh,
              margin: EdgeInsets.fromLTRB(20.h, 21.v, 20.h, 29.v),
              onTap: () {
                onTapShareFILLWgh();
              })
        ],
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildWajihTaysirHandal() {
    return Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(children: [
          CustomImageView(
              imagePath: ImageConstant.imgEllipse1355,
              height: 56.adaptSize,
              width: 56.adaptSize,
              radius: BorderRadius.circular(28.h)),
          Padding(
              padding: EdgeInsets.only(left: 12.h, top: 3.v, bottom: 2.v),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("lbl_jane_cooper".tr,
                        style: theme.textTheme.titleMedium),
                    SizedBox(height: 7.v),
                    Text("lbl_0333_050_4358".tr,
                        style: theme.textTheme.bodyLarge)
                  ])),
          Spacer(),
          Padding(
              padding: EdgeInsets.symmetric(vertical: 14.v),
              child: Text("lbl_52_00".tr, style: CustomTextStyles.titleLarge22))
        ]));
  }

  /// Section Widget
  Widget _buildMasterCard() {
    return Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(children: [
          CustomIconButton(
              height: 55.adaptSize,
              width: 55.adaptSize,
              padding: EdgeInsets.all(7.h),
              decoration: IconButtonStyleHelper.fillGrayTL12,
              child: CustomImageView(imagePath: ImageConstant.imgMasterCard)),
          Padding(
              padding: EdgeInsets.only(left: 12.h, top: 16.v, bottom: 17.v),
              child: Text("msg_2541".tr, style: theme.textTheme.bodyLarge))
        ]));
  }

  /// Section Widget
  Widget _buildSummary() {
    return Container(
        padding: EdgeInsets.all(16.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          _buildToWajihTaysir(
              toWajihTaysir: "msg_upi_transaction".tr,
              email: "lbl_256365214214".tr),
          SizedBox(height: 26.v),
          _buildToWajihTaysir(
              toWajihTaysir: "msg_to_wajih_taysir".tr,
              email: "msg_wajihtaysir_gmail_com".tr),
          SizedBox(height: 23.v),
          _buildToWajihTaysir(
              toWajihTaysir: "msg_from_john_abram".tr,
              email: "msg_johnabram_gmail_com".tr),
          SizedBox(height: 23.v),
          _buildToWajihTaysir(
              toWajihTaysir: "lbl_transaction_id".tr,
              email: "lbl_145214521452".tr)
        ]));
  }

  /// Section Widget
  Widget _buildButtons() {
    return Container(
        margin: EdgeInsets.only(left: 20.h, right: 20.h, bottom: 32.v),
        decoration: AppDecoration.fillWhiteA,
        child: CustomElevatedButton(
          text: "lbl_download_print".tr,
          onPressed: () {
            Get.back();
          },
        ));
  }

  /// Common widget
  Widget _buildToWajihTaysir({
    required String toWajihTaysir,
    required String email,
  }) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Text(toWajihTaysir,
          style: theme.textTheme.bodyLarge!.copyWith(color: appTheme.gray700)),
      Text(email,
          style: CustomTextStyles.bodyLargeBlack900
              .copyWith(color: appTheme.black900))
    ]);
  }

  /// Navigates to the thisWeekScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the sharePopupScreen when the action is triggered.
  onTapShareFILLWgh() {
    Share.share('Statistics this week details');
  }
}
