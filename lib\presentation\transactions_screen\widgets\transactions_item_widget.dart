import '../models/transactions_item_model.dart';
import '../controller/transactions_controller.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class TransactionsItemWidget extends StatelessWidget {
  TransactionsItemWidget(
    this.transactionsItemModelObj, {
    Key? key,
    this.onTapTransactions,
  }) : super(
          key: key,
        );

  TransactionsItemModel transactionsItemModelObj;

  var controller = Get.find<TransactionsController>();

  VoidCallback? onTapTransactions;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTapTransactions!.call();
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 16.h,
          vertical: 14.v,
        ),
        decoration: AppDecoration.outlineBlack.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder12,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(bottom: 1.v),
                  child: CustomIconButton(
                    height: 48.adaptSize,
                    width: 48.adaptSize,
                    padding: EdgeInsets.all(12.h),
                    child: CustomImageView(
                      imagePath: transactionsItemModelObj.image!.value,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 12.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Obx(
                        () => Text(
                          transactionsItemModelObj.financialtransaction!.value,
                          style: theme.textTheme.titleMedium,
                        ),
                      ),
                      SizedBox(height: 6.v),
                      Obx(
                        () => Text(
                          transactionsItemModelObj.duration!.value,
                          style: theme.textTheme.bodyLarge,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(
                top: 12.v,
                bottom: 13.v,
              ),
              child: Obx(
                () => Text(
                  transactionsItemModelObj.price!.value,
                  style: theme.textTheme.titleLarge,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
