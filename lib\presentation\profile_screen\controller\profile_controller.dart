import '../../../core/app_export.dart';import '../models/profile_model.dart';import 'package:flutter/material.dart';/// A controller class for the ProfileScreen.
///
/// This class manages the state of the ProfileScreen, including the
/// current profileModelObj
class ProfileController extends GetxController {TextEditingController shieldkeyholeOneController = TextEditingController();

TextEditingController settingsOneController = TextEditingController();

TextEditingController shieldcheckOneController = TextEditingController();

TextEditingController termAndConditionController = TextEditingController();

TextEditingController signoutaltOneController = TextEditingController();

Rx<ProfileModel> profileModelObj = ProfileModel().obs;

@override void onClose() { super.onClose(); shieldkeyholeOneController.dispose(); settingsOneController.dispose(); shieldcheckOneController.dispose(); termAndConditionController.dispose(); signoutaltOneController.dispose(); } 
 }
