import '../../../core/app_export.dart';
import '../models/edit_profile_model.dart';
import 'package:flutter/material.dart';

/// A controller class for the EditProfileScreen.
///
/// This class manages the state of the EditProfileScreen, including the
/// current editProfileModelObj
class EditProfileController extends GetxController {
  TextEditingController filledController = TextEditingController(text: 'John');

  TextEditingController filledController1 =
      TextEditingController(text: "Abram");

  TextEditingController emailController =
      TextEditingController(text: '<EMAIL>');

  Rx<EditProfileModel> editProfileModelObj = EditProfileModel().obs;

  @override
  void onClose() {
    super.onClose();
    filledController.dispose();
    filledController1.dispose();
    emailController.dispose();
  }
}
