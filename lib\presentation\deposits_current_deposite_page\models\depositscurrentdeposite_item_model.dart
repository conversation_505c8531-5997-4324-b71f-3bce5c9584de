import '../../../core/app_export.dart';

/// This class is used in the [depositscurrentdeposite_item_widget] screen.
class DepositscurrentdepositeItemModel {
  DepositscurrentdepositeItemModel({
    this.eight,
    this.subtitle,
    this.price,
    this.id,
  }) {
    eight = eight ?? Rx("8%");
    subtitle = subtitle ?? Rx("Sep 30, 2022 - Mar 23, 2010");
    price = price ?? Rx("\$2000.00");
    id = id ?? Rx("");
  }

  Rx<String>? eight;

  Rx<String>? subtitle;

  Rx<String>? price;

  Rx<String>? id;
}
