import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_title.dart';
import 'package:payway/widgets/custom_icon_button.dart';
import 'package:flutter_svg_provider/flutter_svg_provider.dart' as fs;
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/photo_id_card_controller.dart';

class PhotoIdCardScreen extends GetWidget<PhotoIdCardController> {
  const PhotoIdCardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBody: true,
        extendBodyBehindAppBar: true,
        appBar: _buildAppBar(),
        body: Container(
            width: SizeUtils.width,
            height: SizeUtils.height,
            padding: EdgeInsets.only(top: 56.v),
            decoration: BoxDecoration(
                color: appTheme.whiteA700,
                image: DecorationImage(
                    image: AssetImage(ImageConstant.imgPhotoIdCard),
                    fit: BoxFit.cover)),
            child: Container(
                width: double.maxFinite,
                padding: EdgeInsets.symmetric(horizontal: 36.h, vertical: 80.v),
                child:
                    Column(mainAxisAlignment: MainAxisAlignment.end, children: [
                  Spacer(flex: 55),
                  _buildCardScan(),
                  Spacer(flex: 55),
                  Padding(
                      padding: EdgeInsets.symmetric(horizontal: 54.h),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                                padding: EdgeInsets.symmetric(vertical: 12.v),
                                child: CustomIconButton(
                                    height: 48.adaptSize,
                                    width: 48.adaptSize,
                                    padding: EdgeInsets.all(14.h),
                                    decoration:
                                        IconButtonStyleHelper.fillWhiteA,
                                    child: CustomImageView(
                                        imagePath: ImageConstant.imgBolt1))),
                            Spacer(flex: 50),
                            CustomImageView(
                                imagePath:
                                    ImageConstant.imgContrastWhiteA70072x72,
                                height: 72.adaptSize,
                                width: 72.adaptSize,
                                onTap: () {
                                  onTapImgContrast();
                                }),
                            Spacer(flex: 50),
                            Padding(
                                padding: EdgeInsets.symmetric(vertical: 12.v),
                                child: CustomIconButton(
                                    height: 48.adaptSize,
                                    width: 48.adaptSize,
                                    padding: EdgeInsets.all(14.h),
                                    decoration:
                                        IconButtonStyleHelper.fillWhiteA,
                                    child: CustomImageView(
                                        imagePath:
                                            ImageConstant.imgAutorenewFill0)))
                          ]))
                ]))));
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        height: 61.v,
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFilWhiteA700,
            margin: EdgeInsets.only(left: 20.h, top: 11.v, bottom: 10.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarTitle(
            text: "lbl_photo_id_card".tr, margin: EdgeInsets.only(left: 16.h)));
  }

  /// Section Widget
  Widget _buildCardScan() {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 32.h, vertical: 40.v),
        decoration: BoxDecoration(
            // color: Colors.red,
            image: DecorationImage(
                image: fs.Svg(ImageConstant.imgCardScan), fit: BoxFit.fill)),
        child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
          // SizedBox(height: 3.v),
          CustomImageView(
              imagePath: ImageConstant.imgImage57,
              // height: 194.v,
              width: 291.h,
              fit: BoxFit.fill,
              radius: BorderRadius.circular(12.h))
        ]));
  }

  /// Navigates to the proofOfResidencyScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the selfieWithIdCardScreen when the action is triggered.
  onTapImgContrast() {
    Get.toNamed(
      AppRoutes.selfieWithIdCardScreen,
    );
  }
}
