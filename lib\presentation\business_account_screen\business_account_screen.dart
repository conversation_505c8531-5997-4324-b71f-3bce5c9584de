import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/data/models/business_account_model.dart';
import 'controller/business_account_controller.dart';

class BusinessAccountScreen extends GetWidget<BusinessAccountController> {
  const BusinessAccountScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Business Account',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 24.h),
            _buildBusinessInfoForm(),
            <PERSON><PERSON><PERSON><PERSON>(height: 24.h),
            _buildLocationSection(),
            Sized<PERSON>ox(height: 24.h),
            _buildBankingSection(),
            SizedBox(height: 24.h),
            _buildVerificationSection(),
            SizedBox(height: 32.h),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.business, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 12.w),
                Expanded(
                  child: Text(
                    'Business Account Setup',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              'Create your business account to start selling products and services with secure escrow payments.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessInfoForm() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Business Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.businessNameController,
              decoration: InputDecoration(
                labelText: 'Business Name',
                prefixIcon: Icon(Icons.business),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.ownerNameController,
              decoration: InputDecoration(
                labelText: 'Owner Name',
                prefixIcon: Icon(Icons.person),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.businessTypeController,
              decoration: InputDecoration(
                labelText: 'Business Type',
                prefixIcon: Icon(Icons.category),
                hintText: 'e.g., Retail, Restaurant, Service',
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.emailController,
              decoration: InputDecoration(
                labelText: 'Business Email',
                prefixIcon: Icon(Icons.email),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.phoneController,
              decoration: InputDecoration(
                labelText: 'Business Phone',
                prefixIcon: Icon(Icons.phone),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Location Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.addressController,
              decoration: InputDecoration(
                labelText: 'Business Address',
                prefixIcon: Icon(Icons.location_on),
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller.cityController,
                    decoration: InputDecoration(
                      labelText: 'City',
                      prefixIcon: Icon(Icons.location_city),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: TextField(
                    controller: controller.stateController,
                    decoration: InputDecoration(
                      labelText: 'State',
                      prefixIcon: Icon(Icons.map),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller.countryController,
                    decoration: InputDecoration(
                      labelText: 'Country',
                      prefixIcon: Icon(Icons.public),
                    ),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: TextField(
                    controller: controller.postalCodeController,
                    decoration: InputDecoration(
                      labelText: 'Postal Code',
                      prefixIcon: Icon(Icons.markunread_mailbox),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            ElevatedButton.icon(
              onPressed: () => controller.getCurrentLocation(),
              icon: Icon(Icons.my_location),
              label: Text('Use Current Location'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.secondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankingSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Banking Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.bankAccountController,
              decoration: InputDecoration(
                labelText: 'Bank Account Number',
                prefixIcon: Icon(Icons.account_balance),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.routingNumberController,
              decoration: InputDecoration(
                labelText: 'Routing Number',
                prefixIcon: Icon(Icons.account_balance_wallet),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verification Documents',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.businessLicenseController,
              decoration: InputDecoration(
                labelText: 'Business License Number',
                prefixIcon: Icon(Icons.verified),
              ),
            ),
            SizedBox(height: 16.h),
            TextField(
              controller: controller.taxIdController,
              decoration: InputDecoration(
                labelText: 'Tax ID Number',
                prefixIcon: Icon(Icons.receipt),
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => controller.uploadBusinessLicense(),
                    icon: Icon(Icons.upload_file),
                    label: Text('Upload License'),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => controller.uploadTaxDocument(),
                    icon: Icon(Icons.upload_file),
                    label: Text('Upload Tax Doc'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Obx(() => SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: controller.isLoading.value ? null : () => controller.submitBusinessAccount(),
        child: controller.isLoading.value
          ? CircularProgressIndicator(color: Colors.white)
          : Text(
              'Create Business Account',
              style: TextStyle(fontSize: 16.fSize, fontWeight: FontWeight.w600),
            ),
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 16.h),
        ),
      ),
    ));
  }
} 