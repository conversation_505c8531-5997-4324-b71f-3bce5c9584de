import '../../../core/app_export.dart';import '../models/guest_profile_model.dart';import 'package:flutter/material.dart';/// A controller class for the GuestProfilePage.
///
/// This class manages the state of the GuestProfilePage, including the
/// current guestProfileModelObj
class GuestProfileController extends GetxController {GuestProfileController(this.guestProfileModelObj);

TextEditingController shieldkeyholeOneController = TextEditingController();

TextEditingController settingsOneController = TextEditingController();

TextEditingController shieldcheckOneController = TextEditingController();

TextEditingController termAndConditionController = TextEditingController();

Rx<GuestProfileModel> guestProfileModelObj;

@override void onClose() { super.onClose(); shieldkeyholeOneController.dispose(); settingsOneController.dispose(); shieldcheckOneController.dispose(); termAndConditionController.dispose(); } 
 }
