import '../../../core/app_export.dart';
import 'selectbankpopupone_item_model.dart';

/// This class defines the variables used in the [select_bank_popup_one_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class SelectBankPopupOneModel {
  Rx<List<SelectbankpopuponeItemModel>> selectbankpopuponeItemList = Rx(
    [
      SelectbankpopuponeItemModel(
        masterCard: ImageConstant.imgMasterCard.obs,
        twoThousandFiveHundredFortyOne: "**** **** **** 2541".obs,
        price: "\$20,000.00".obs,
        image: ImageConstant.imgContrastWhiteA700.obs,
      ),
      SelectbankpopuponeItemModel(
        masterCard: ImageConstant.imgPaypal.obs,
        twoThousandFiveHundredFortyOne: "**** **** **** 2564".obs,
        price: "\$10,000.00".obs,
        image: ImageConstant.imgContrastWhiteA700.obs,
      ),
      SelectbankpopuponeItemModel(
        masterCard: ImageConstant.imgStripe.obs,
        twoThousandFiveHundredFortyOne: "**** **** **** 2541".obs,
        price: "\$50,000.00".obs,
        image: ImageConstant.imgContrastWhiteA700.obs,
      ),
    ],
  );
}
