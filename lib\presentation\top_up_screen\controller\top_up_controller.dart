import '../../../core/app_export.dart';
import '../models/top_up_model.dart';

/// A controller class for the TopUpScreen.
///
/// This class manages the state of the TopUpScreen, including the
/// current topUpModelObj
class TopUpController extends GetxController {
  Rx<TopUpModel> topUpModelObj = TopUpModel().obs;

  SelectionPopupModel? selectedDropDownValue;

  int isSelectPrice = 0;

  String selectedPrice = "\$100.00";

  onSelected(dynamic value) {
    for (var element in topUpModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    topUpModelObj.value.dropdownItemList.refresh();
  }
}
