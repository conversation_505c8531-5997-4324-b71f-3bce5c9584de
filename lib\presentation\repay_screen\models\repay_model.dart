import 'package:payway/core/app_export.dart';import 'frame5_item_model.dart';/// This class defines the variables used in the [repay_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class RepayModel {Rx<List<SelectionPopupModel>> dropdownItemList = Rx([SelectionPopupModel(id:1,title:"Item One",isSelected:true,),SelectionPopupModel(id:2,title:"Item Two",),SelectionPopupModel(id:3,title:"Item Three",)]);

Rx<List<Frame5ItemModel>> frame5ItemList = Rx(List.generate(6,(index) =>Frame5ItemModel()));

 }
