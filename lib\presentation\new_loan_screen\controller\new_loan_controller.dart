import '../../../core/app_export.dart';import '../models/new_loan_model.dart';import 'package:flutter/material.dart';/// A controller class for the NewLoanScreen.
///
/// This class manages the state of the NewLoanScreen, including the
/// current newLoanModelObj
class NewLoanController extends GetxController {TextEditingController defaultController = TextEditingController();

TextEditingController defaultController1 = TextEditingController();

Rx<NewLoanModel> newLoanModelObj = NewLoanModel().obs;

Rx<bool> isSelectedSwitch = false.obs;

SelectionPopupModel? selectedDropDownValue;

SelectionPopupModel? selectedDropDownValue1;

@override void onClose() { super.onClose(); defaultController.dispose(); defaultController1.dispose(); } 
onSelected(dynamic value) { for (var element in newLoanModelObj.value.dropdownItemList.value) {element.isSelected = false; if (element.id == value.id) {element.isSelected = true;}} newLoanModelObj.value.dropdownItemList.refresh(); } 
onSelected1(dynamic value) { for (var element in newLoanModelObj.value.dropdownItemList1.value) {element.isSelected = false; if (element.id == value.id) {element.isSelected = true;}} newLoanModelObj.value.dropdownItemList1.refresh(); } 
 }
