import 'package:payway/presentation/proof_of_residency_screen/controller/proof_of_residency_controller.dart';
import 'package:get/get.dart';

/// A binding class for the ProofOfResidencyScreen.
///
/// This class ensures that the ProofOfResidencyController is created when the
/// ProofOfResidencyScreen is first loaded.
class ProofOfResidencyBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ProofOfResidencyController());
  }
}
