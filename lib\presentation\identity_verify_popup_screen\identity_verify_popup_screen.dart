import 'package:payway/widgets/custom_elevated_button.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

class IdentityVerifyPopupScreen extends StatefulWidget {
  const IdentityVerifyPopupScreen({Key? key}) : super(key: key);

  @override
  State<IdentityVerifyPopupScreen> createState() =>
      _IdentityVerifyPopupScreenState();
}

class _IdentityVerifyPopupScreenState extends State<IdentityVerifyPopupScreen> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 388.h,
      padding: EdgeInsets.symmetric(horizontal: 24.h, vertical: 32.v),
      decoration: AppDecoration.fillWhiteA
          .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomImageView(
              imagePath: ImageConstant.passwordChanged,
              height: 116.v,
              width: 116.h,
              fit: BoxFit.contain,
              alignment: Alignment.center),
          SizedBox(height: 16.v),
          Text("Let’s verify your identity!".tr,
              style: CustomTextStyles.titleLargeBlack900),
          SizedBox(height: 8.v),
          Container(
              width: 332.h,
              margin: EdgeInsets.symmetric(horizontal: 3.h),
              child: Text(
                  "We are required to verify your identity before you can use the service. Your information will be encrypted and stored securely."
                      .tr,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
          SizedBox(height: 40.v),
          CustomElevatedButton(
            text: "Verify identity".tr,
            onPressed: () {
              Get.toNamed(
                AppRoutes.proofOfResidencyScreen,
              );
            },
          ),
        ],
      ),
    );
  }
}
