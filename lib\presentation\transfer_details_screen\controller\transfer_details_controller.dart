import '../../../core/app_export.dart';import '../models/transfer_details_model.dart';/// A controller class for the TransferDetailsScreen.
///
/// This class manages the state of the TransferDetailsScreen, including the
/// current transferDetailsModelObj
class TransferDetailsController extends GetxController {Rx<TransferDetailsModel> transferDetailsModelObj = TransferDetailsModel().obs;

SelectionPopupModel? selectedDropDownValue;

onSelected(dynamic value) { for (var element in transferDetailsModelObj.value.dropdownItemList.value) {element.isSelected = false; if (element.id == value.id) {element.isSelected = true;}} transferDetailsModelObj.value.dropdownItemList.refresh(); } 
 }
