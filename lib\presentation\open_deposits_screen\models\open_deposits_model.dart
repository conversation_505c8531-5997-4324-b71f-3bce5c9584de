import 'package:payway/core/app_export.dart';

/// This class defines the variables used in the [open_deposits_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class OpenDepositsModel {
  Rx<List<SelectionPopupModel>> dropdownItemList = Rx([
    SelectionPopupModel(
      id: 1,
      title: "USD",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "Rupee",
    ),
    SelectionPopupModel(
      id: 3,
      title: "Pound",
    )
  ]);

  Rx<List<SelectionPopupModel>> dropdownItemList1 = Rx([
    SelectionPopupModel(
      id: 1,
      title: "1 Month",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "3 Month",
    ),
    SelectionPopupModel(
      id: 3,
      title: "6 Month",
    )
  ]);

  Rx<List<SelectionPopupModel>> dropdownItemList2 = Rx([
    SelectionPopupModel(
      id: 1,
      title: "Item One",
      isSelected: true,
    ),
    SelectionPopupModel(
      id: 2,
      title: "Item Two",
    ),
    SelectionPopupModel(
      id: 3,
      title: "Item Three",
    )
  ]);
}
