import '../models/seventythree_item_model.dart';
import '../controller/onboarding_two_controller.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class SeventythreeItemWidget extends StatelessWidget {
  SeventythreeItemWidget(
    this.seventythreeItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  SeventythreeItemModel seventythreeItemModelObj;

  var controller = Get.find<OnboardingTwoController>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          "lbl_new_banking".tr,
          style: theme.textTheme.displaySmall,
        ),
        Sized<PERSON><PERSON>(height: 13.v),
        <PERSON><PERSON><PERSON><PERSON>(
          width: 369.h,
          child: Text(
            "msg_trends_in_this_report".tr,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: CustomTextStyles.bodyLargeBlack900.copyWith(
              height: 1.29,
            ),
          ),
        ),
      ],
    );
  }
}
