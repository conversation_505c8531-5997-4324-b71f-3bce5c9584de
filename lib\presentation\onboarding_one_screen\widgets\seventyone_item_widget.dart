import '../models/seventyone_item_model.dart';
import '../controller/onboarding_one_controller.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';

// ignore: must_be_immutable
class SeventyoneItemWidget extends StatelessWidget {
  SeventyoneItemWidget(
    this.seventyoneItemModelObj, {
    Key? key,
  }) : super(
          key: key,
        );

  SeventyoneItemModel seventyoneItemModelObj;

  var controller = Get.find<OnboardingOneController>();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          "lbl_make_it_simple".tr,
          style: theme.textTheme.displaySmall,
        ),
        Sized<PERSON><PERSON>(height: 13.v),
        <PERSON><PERSON><PERSON><PERSON>(
          width: 377.h,
          child: Text(
            "msg_money_transfer_generally".tr,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: CustomTextStyles.bodyLargeBlack900.copyWith(
              height: 1.29,
            ),
          ),
        ),
      ],
    );
  }
}
