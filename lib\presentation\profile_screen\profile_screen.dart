import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import '../logout_popup_dialog/controller/logout_popup_controller.dart';
import '../logout_popup_dialog/logout_popup_dialog.dart';
import 'controller/profile_controller.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  ProfileController controller = Get.put(ProfileController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 24.v),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Row(children: [
              CustomImageView(
                  imagePath: ImageConstant.imgAvtar1,
                  height: 80.adaptSize,
                  width: 80.adaptSize),
              Padding(
                  padding: EdgeInsets.only(left: 15.h, top: 15.v, bottom: 15.v),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text("lbl_john_abram2".tr,
                            style: theme.textTheme.titleMedium),
                        SizedBox(height: 6.v),
                        Text("msg_johnabram_gmail_com".tr,
                            style: theme.textTheme.bodyLarge)
                      ]))
            ]),
            SizedBox(height: 32.v),
            _buildPersonFILLWg(),
            SizedBox(height: 24.v),
            _buildAccountUpgrade(),
            SizedBox(height: 24.v),
            _buildAccountSwitcher(),
            SizedBox(height: 24.v),
            _buildShieldkeyholeOne(),
            SizedBox(height: 24.v),
            _buildSettingsOne(),
            SizedBox(height: 24.v),
            _buildShieldcheckOne(),
            SizedBox(height: 24.v),
            _buildTermAndCondition(),
            SizedBox(height: 24.v),
            _buildSignoutaltOne(),
          ])),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        title: AppbarSubtitle(
            text: "lbl_profile".tr, margin: EdgeInsets.only(left: 20.h)),
        styleType: Style.bgFill);
  }

  /// Section Widget
  Widget _buildPersonFILLWg() {
    return GestureDetector(
      onTap: () {
        onTapPersonFILLWg();
      },
      child: Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(
          children: [
            Container(
                height: 48.adaptSize,
                width: 48.adaptSize,
                padding: EdgeInsets.all(12.h),
                decoration: AppDecoration.fillIndigo
                    .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
                child: CustomImageView(
                    imagePath: ImageConstant.imgPersonFill0Wg,
                    height: 24.adaptSize,
                    width: 24.adaptSize,
                    alignment: Alignment.center)),
            Expanded(
              child: Padding(
                  padding: EdgeInsets.only(
                    left: 12.h,
                  ),
                  child: Text("lbl_my_profile".tr,
                      style: CustomTextStyles.bodyLargeBlack900)),
            ),
            CustomImageView(
              imagePath: ImageConstant.arrowRightGray,
              height: 24.adaptSize,
              width: 24.adaptSize,
              margin: EdgeInsets.only(
                top: 12.v,
                right: 8.h,
                bottom: 11.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildShieldkeyholeOne() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(
          AppRoutes.securityScreen,
        );
      },
      child: Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(
          children: [
            Container(
                height: 48.adaptSize,
                width: 48.adaptSize,
                padding: EdgeInsets.all(12.h),
                decoration: AppDecoration.fillIndigo
                    .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
                child: CustomImageView(
                    imagePath: ImageConstant.security,
                    height: 24.adaptSize,
                    width: 24.adaptSize,
                    alignment: Alignment.center)),
            Expanded(
              child: Padding(
                  padding: EdgeInsets.only(
                    left: 12.h,
                  ),
                  child: Text("Security".tr,
                      style: CustomTextStyles.bodyLargeBlack900)),
            ),
            CustomImageView(
              imagePath: ImageConstant.arrowRightGray,
              height: 24.adaptSize,
              width: 24.adaptSize,
              margin: EdgeInsets.only(
                top: 12.v,
                right: 8.h,
                bottom: 11.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildSettingsOne() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(
          AppRoutes.settingsScreen,
        );
      },
      child: Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(
          children: [
            Container(
                height: 48.adaptSize,
                width: 48.adaptSize,
                padding: EdgeInsets.all(12.h),
                decoration: AppDecoration.fillIndigo
                    .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
                child: CustomImageView(
                    imagePath: ImageConstant.settings,
                    height: 24.adaptSize,
                    width: 24.adaptSize,
                    alignment: Alignment.center)),
            Expanded(
              child: Padding(
                  padding: EdgeInsets.only(
                    left: 12.h,
                  ),
                  child: Text("Settings".tr,
                      style: CustomTextStyles.bodyLargeBlack900)),
            ),
            CustomImageView(
              imagePath: ImageConstant.arrowRightGray,
              height: 24.adaptSize,
              width: 24.adaptSize,
              margin: EdgeInsets.only(
                top: 12.v,
                right: 8.h,
                bottom: 11.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildShieldcheckOne() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(
          AppRoutes.privacyPolicyScreen,
        );
      },
      child: Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(
          children: [
            Container(
                height: 48.adaptSize,
                width: 48.adaptSize,
                padding: EdgeInsets.all(12.h),
                decoration: AppDecoration.fillIndigo
                    .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
                child: CustomImageView(
                    imagePath: ImageConstant.privacyPolicy,
                    height: 24.adaptSize,
                    width: 24.adaptSize,
                    alignment: Alignment.center)),
            Expanded(
              child: Padding(
                  padding: EdgeInsets.only(
                    left: 12.h,
                  ),
                  child: Text("Privacy policy".tr,
                      style: CustomTextStyles.bodyLargeBlack900)),
            ),
            CustomImageView(
              imagePath: ImageConstant.arrowRightGray,
              height: 24.adaptSize,
              width: 24.adaptSize,
              margin: EdgeInsets.only(
                top: 12.v,
                right: 8.h,
                bottom: 11.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildTermAndCondition() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(
          AppRoutes.termsConditionsScreen,
        );
      },
      child: Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(
          children: [
            Container(
                height: 48.adaptSize,
                width: 48.adaptSize,
                padding: EdgeInsets.all(12.h),
                decoration: AppDecoration.fillIndigo
                    .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
                child: CustomImageView(
                    imagePath: ImageConstant.termsAndCondition,
                    height: 24.adaptSize,
                    width: 24.adaptSize,
                    alignment: Alignment.center)),
            Expanded(
              child: Padding(
                  padding: EdgeInsets.only(
                    left: 12.h,
                  ),
                  child: Text("Terms & conditions".tr,
                      style: CustomTextStyles.bodyLargeBlack900)),
            ),
            CustomImageView(
              imagePath: ImageConstant.arrowRightGray,
              height: 24.adaptSize,
              width: 24.adaptSize,
              margin: EdgeInsets.only(
                top: 12.v,
                right: 8.h,
                bottom: 11.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildSignoutaltOne() {
    return GestureDetector(
      onTap: () {
        Get.dialog(AlertDialog(
          backgroundColor: Colors.transparent,
          contentPadding: EdgeInsets.zero,
          insetPadding: const EdgeInsets.only(left: 0),
          content: LogoutPopupDialog(
            Get.put(
              LogoutPopupController(),
            ),
          ),
        ));
      },
      child: Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(
          children: [
            Container(
                height: 48.adaptSize,
                width: 48.adaptSize,
                padding: EdgeInsets.all(12.h),
                decoration: AppDecoration.fillIndigo
                    .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
                child: CustomImageView(
                    imagePath: ImageConstant.logout,
                    height: 24.adaptSize,
                    width: 24.adaptSize,
                    alignment: Alignment.center)),
            Expanded(
              child: Padding(
                  padding: EdgeInsets.only(
                    left: 12.h,
                  ),
                  child: Text("Logout".tr,
                      style: CustomTextStyles.bodyLargeBlack900)),
            ),
            CustomImageView(
              imagePath: ImageConstant.arrowRightGray,
              height: 24.adaptSize,
              width: 24.adaptSize,
              margin: EdgeInsets.only(
                top: 12.v,
                right: 8.h,
                bottom: 11.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildAccountUpgrade() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(
          AppRoutes.accountUpgradeScreen,
        );
      },
      child: Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(
          children: [
            Container(
                height: 48.adaptSize,
                width: 48.adaptSize,
                padding: EdgeInsets.all(12.h),
                decoration: AppDecoration.fillIndigo
                    .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
                child: Icon(
                    Icons.upgrade,
                    color: Colors.white,
                    size: 24.adaptSize)),
            Expanded(
              child: Padding(
                  padding: EdgeInsets.only(
                    left: 12.h,
                  ),
                  child: Text("Upgrade Account".tr,
                      style: CustomTextStyles.bodyLargeBlack900)),
            ),
            CustomImageView(
              imagePath: ImageConstant.arrowRightGray,
              height: 24.adaptSize,
              width: 24.adaptSize,
              margin: EdgeInsets.only(
                top: 12.v,
                right: 8.h,
                bottom: 11.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildAccountSwitcher() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(
          AppRoutes.accountSwitcherScreen,
        );
      },
      child: Container(
        padding: EdgeInsets.all(8.h),
        decoration: AppDecoration.outlineBlack
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
        child: Row(
          children: [
            Container(
                height: 48.adaptSize,
                width: 48.adaptSize,
                padding: EdgeInsets.all(12.h),
                decoration: AppDecoration.fillIndigo
                    .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
                child: Icon(
                    Icons.swap_horiz,
                    color: Colors.white,
                    size: 24.adaptSize)),
            Expanded(
              child: Padding(
                  padding: EdgeInsets.only(
                    left: 12.h,
                  ),
                  child: Text("Switch Account".tr,
                      style: CustomTextStyles.bodyLargeBlack900)),
            ),
            CustomImageView(
              imagePath: ImageConstant.arrowRightGray,
              height: 24.adaptSize,
              width: 24.adaptSize,
              margin: EdgeInsets.only(
                top: 12.v,
                right: 8.h,
                bottom: 11.v,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Navigates to the myProfileScreen when the action is triggered.
  onTapPersonFILLWg() {
    Get.toNamed(
      AppRoutes.myProfileScreen,
    );
  }
}
