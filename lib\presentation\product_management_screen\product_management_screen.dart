import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/data/models/product_model.dart';
import 'controller/product_management_controller.dart';

class ProductManagementScreen extends GetWidget<ProductManagementController> {
  const ProductManagementScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Product Management',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () => controller.showAddProductDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(),
          Expanded(
            child: _buildProductList(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => controller.showAddProductDialog(),
        child: Icon(Icons.add),
        tooltip: 'Add Product',
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: EdgeInsets.all(16.h),
      child: Column(
        children: [
          TextField(
            controller: controller.searchController,
            decoration: InputDecoration(
              hintText: 'Search products...',
              prefixIcon: Icon(Icons.search),
              suffixIcon: IconButton(
                icon: Icon(Icons.clear),
                onPressed: () => controller.searchController.clear(),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.h),
              ),
            ),
            onChanged: (value) => controller.searchProducts(value),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: controller.selectedCategory.value,
                  decoration: InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                  ),
                  items: ['All', 'Electronics', 'Clothing', 'Food', 'Books', 'Home']
                      .map((category) => DropdownMenuItem(
                            value: category,
                            child: Text(category),
                          ))
                      .toList(),
                  onChanged: (value) {
                    controller.selectedCategory.value = value!;
                    controller.filterProducts();
                  },
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: controller.selectedStatus.value,
                  decoration: InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(),
                  ),
                  items: ['All', 'Available', 'Out of Stock', 'Featured']
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status),
                          ))
                      .toList(),
                  onChanged: (value) {
                    controller.selectedStatus.value = value!;
                    controller.filterProducts();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductList() {
    return Obx(() => controller.isLoading.value
      ? Center(child: CircularProgressIndicator())
      : controller.filteredProducts.isEmpty
        ? _buildEmptyState()
        : ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16.h),
            itemCount: controller.filteredProducts.length,
            itemBuilder: (context, index) {
              final product = controller.filteredProducts[index];
              return _buildProductCard(product);
            },
          ));
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2,
            size: 64.w,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'No products found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'Add your first product to start selling',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: () => controller.showAddProductDialog(),
            icon: Icon(Icons.add),
            label: Text('Add Product'),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(Product product) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: () => controller.editProduct(product),
        child: Padding(
          padding: EdgeInsets.all(16.h),
          child: Row(
            children: [
              _buildProductImage(product),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      product.category,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Text(
                          '\$${product.price.toStringAsFixed(2)}',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        if (product.hasDiscount) ...[
                          SizedBox(width: 8.w),
                          Text(
                            '\$${product.originalPrice.toStringAsFixed(2)}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              decoration: TextDecoration.lineThrough,
                              color: Colors.grey[500],
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(4.h),
                            ),
                            child: Text(
                              '-${product.discountPercentage.toInt()}%',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10.fSize,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(
                          Icons.inventory,
                          size: 16.w,
                          color: Colors.grey[500],
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          'Stock: ${product.stockQuantity}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        Spacer(),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                          decoration: BoxDecoration(
                            color: product.isAvailable 
                              ? Colors.green.withOpacity(0.1)
                              : Colors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12.h),
                          ),
                          child: Text(
                            product.isAvailable ? 'Available' : 'Out of Stock',
                            style: TextStyle(
                              color: product.isAvailable ? Colors.green : Colors.red,
                              fontSize: 10.fSize,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          size: 16.w,
                          color: Colors.amber,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          '${product.rating} (${product.reviewCount} reviews)',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        Spacer(),
                        if (product.isFeatured)
                          Icon(
                            Icons.featured_play_list,
                            size: 16.w,
                            color: Colors.blue,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) => controller.handleProductAction(value, product),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16.w),
                        SizedBox(width: 8.w),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'toggle_status',
                    child: Row(
                      children: [
                        Icon(
                          product.isAvailable ? Icons.visibility_off : Icons.visibility,
                          size: 16.w,
                        ),
                        SizedBox(width: 8.w),
                        Text(product.isAvailable ? 'Hide' : 'Show'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'toggle_featured',
                    child: Row(
                      children: [
                        Icon(
                          product.isFeatured ? Icons.star_border : Icons.star,
                          size: 16.w,
                        ),
                        SizedBox(width: 8.w),
                        Text(product.isFeatured ? 'Unfeature' : 'Feature'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16.w, color: Colors.red),
                        SizedBox(width: 8.w),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductImage(Product product) {
    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8.h),
      ),
      child: product.images.isNotEmpty
        ? ClipRRect(
            borderRadius: BorderRadius.circular(8.h),
            child: Image.network(
              product.images.first,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Icon(
                Icons.image,
                size: 32.w,
                color: Colors.grey[400],
              ),
            ),
          )
        : Icon(
            Icons.image,
            size: 32.w,
            color: Colors.grey[400],
          ),
    );
  }
} 