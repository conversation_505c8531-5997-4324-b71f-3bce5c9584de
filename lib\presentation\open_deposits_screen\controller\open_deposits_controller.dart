import '../../../core/app_export.dart';
import '../models/open_deposits_model.dart';
import 'package:flutter/material.dart';

/// A controller class for the OpenDepositsScreen.
///
/// This class manages the state of the OpenDepositsScreen, including the
/// current openDepositsModelObj
class OpenDepositsController extends GetxController {
  TextEditingController defaultController = TextEditingController();

  Rx<OpenDepositsModel> openDepositsModelObj = OpenDepositsModel().obs;

  Rx<bool> isSelectedSwitch = false.obs;

  SelectionPopupModel? selectedDropDownValue;

  SelectionPopupModel? selectedDropDownValue1;

  SelectionPopupModel? selectedDropDownValue2;

  @override
  void onClose() {
    super.onClose();
    defaultController.dispose();
  }

  void clearText() {
    defaultController = TextEditingController(text: "");
  }

  onSelected(dynamic value) {
    for (var element in openDepositsModelObj.value.dropdownItemList.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    openDepositsModelObj.value.dropdownItemList.refresh();
  }

  onSelected1(dynamic value) {
    for (var element in openDepositsModelObj.value.dropdownItemList1.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    openDepositsModelObj.value.dropdownItemList1.refresh();
  }

  onSelected2(dynamic value) {
    for (var element in openDepositsModelObj.value.dropdownItemList2.value) {
      element.isSelected = false;
      if (element.id == value.id) {
        element.isSelected = true;
      }
    }
    openDepositsModelObj.value.dropdownItemList2.refresh();
  }
}
