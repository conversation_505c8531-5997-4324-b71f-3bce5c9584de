enum EscrowStatus {
  pending,
  funded,
  inTransit,
  delivered,
  completed,
  disputed,
  refunded,
  cancelled
}

enum EscrowType {
  product,
  service,
  milestone
}

class EscrowTransaction {
  final String id;
  final String buyerId;
  final String sellerId;
  final String productId;
  final double amount;
  final double escrowFee;
  final double totalAmount;
  final EscrowStatus status;
  final EscrowType type;
  final String description;
  final DateTime expectedDeliveryDate;
  final DateTime? actualDeliveryDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic> metadata;

  EscrowTransaction({
    required this.id,
    required this.buyerId,
    required this.sellerId,
    required this.productId,
    required this.amount,
    required this.escrowFee,
    required this.totalAmount,
    required this.status,
    required this.type,
    required this.description,
    required this.expectedDeliveryDate,
    this.actualDeliveryDate,
    required this.createdAt,
    required this.updatedAt,
    required this.metadata,
  });

  factory EscrowTransaction.fromJson(Map<String, dynamic> json) {
    return EscrowTransaction(
      id: json['id'] ?? '',
      buyerId: json['buyerId'] ?? '',
      sellerId: json['sellerId'] ?? '',
      productId: json['productId'] ?? '',
      amount: json['amount']?.toDouble() ?? 0.0,
      escrowFee: json['escrowFee']?.toDouble() ?? 0.0,
      totalAmount: json['totalAmount']?.toDouble() ?? 0.0,
      status: EscrowStatus.values.firstWhere(
        (e) => e.toString() == 'EscrowStatus.${json['status']}',
        orElse: () => EscrowStatus.pending,
      ),
      type: EscrowType.values.firstWhere(
        (e) => e.toString() == 'EscrowType.${json['type']}',
        orElse: () => EscrowType.product,
      ),
      description: json['description'] ?? '',
      expectedDeliveryDate: DateTime.parse(json['expectedDeliveryDate'] ?? DateTime.now().toIso8601String()),
      actualDeliveryDate: json['actualDeliveryDate'] != null 
        ? DateTime.parse(json['actualDeliveryDate']) 
        : null,
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'buyerId': buyerId,
      'sellerId': sellerId,
      'productId': productId,
      'amount': amount,
      'escrowFee': escrowFee,
      'totalAmount': totalAmount,
      'status': status.toString().split('.').last,
      'type': type.toString().split('.').last,
      'description': description,
      'expectedDeliveryDate': expectedDeliveryDate.toIso8601String(),
      'actualDeliveryDate': actualDeliveryDate?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  EscrowTransaction copyWith({
    String? id,
    String? buyerId,
    String? sellerId,
    String? productId,
    double? amount,
    double? escrowFee,
    double? totalAmount,
    EscrowStatus? status,
    EscrowType? type,
    String? description,
    DateTime? expectedDeliveryDate,
    DateTime? actualDeliveryDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return EscrowTransaction(
      id: id ?? this.id,
      buyerId: buyerId ?? this.buyerId,
      sellerId: sellerId ?? this.sellerId,
      productId: productId ?? this.productId,
      amount: amount ?? this.amount,
      escrowFee: escrowFee ?? this.escrowFee,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      type: type ?? this.type,
      description: description ?? this.description,
      expectedDeliveryDate: expectedDeliveryDate ?? this.expectedDeliveryDate,
      actualDeliveryDate: actualDeliveryDate ?? this.actualDeliveryDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  bool get isPending => status == EscrowStatus.pending;
  bool get isFunded => status == EscrowStatus.funded;
  bool get isInTransit => status == EscrowStatus.inTransit;
  bool get isDelivered => status == EscrowStatus.delivered;
  bool get isCompleted => status == EscrowStatus.completed;
  bool get isDisputed => status == EscrowStatus.disputed;
  bool get isRefunded => status == EscrowStatus.refunded;
  bool get isCancelled => status == EscrowStatus.cancelled;

  String get statusText {
    switch (status) {
      case EscrowStatus.pending:
        return 'Pending';
      case EscrowStatus.funded:
        return 'Funded';
      case EscrowStatus.inTransit:
        return 'In Transit';
      case EscrowStatus.delivered:
        return 'Delivered';
      case EscrowStatus.completed:
        return 'Completed';
      case EscrowStatus.disputed:
        return 'Disputed';
      case EscrowStatus.refunded:
        return 'Refunded';
      case EscrowStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get typeText {
    switch (type) {
      case EscrowType.product:
        return 'Product';
      case EscrowType.service:
        return 'Service';
      case EscrowType.milestone:
        return 'Milestone';
    }
  }
} 