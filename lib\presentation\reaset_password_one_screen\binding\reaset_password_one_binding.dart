import 'package:payway/presentation/reaset_password_one_screen/controller/reaset_password_one_controller.dart';
import 'package:get/get.dart';

/// A binding class for the ReasetPasswordOneScreen.
///
/// This class ensures that the ReasetPasswordOneController is created when the
/// ReasetPasswordOneScreen is first loaded.
class ReasetPasswordOneBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ReasetPasswordOneController());
  }
}
