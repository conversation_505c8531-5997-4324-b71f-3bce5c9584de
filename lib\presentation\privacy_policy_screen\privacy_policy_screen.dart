import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/privacy_policy_controller.dart';

class PrivacyPolicyScreen extends GetWidget<PrivacyPolicyController> {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _buildAppBar(),
        body: Container(
            width: double.maxFinite,
            padding: EdgeInsets.all(20.h),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text("msg_types_of_data_we".tr,
                  style: CustomTextStyles.titleLargeBlack900_1),
              <PERSON><PERSON><PERSON><PERSON>(height: 9.v),
              SizedBox(
                  width: 386.h,
                  child: Text("msg_torem_ipsum_dolor".tr,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style:
                          theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
              SizedBox(height: 27.v),
              Text("msg_use_of_your_personal".tr,
                  style: CustomTextStyles.titleLargeBlack900_1),
              SizedBox(height: 9.v),
              SizedBox(
                  width: 386.h,
                  child: Text("msg_torem_ipsum_dolor".tr,
                      maxLines: 4,
                      overflow: TextOverflow.ellipsis,
                      style:
                          theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
              SizedBox(height: 27.v),
              Text("msg_disclosure_of_your".tr,
                  style: CustomTextStyles.titleLargeBlack900_1),
              SizedBox(height: 10.v),
              SizedBox(
                  width: 388.h,
                  child: Text("msg_lorem_ipsum_dolor3".tr,
                      maxLines: 10,
                      overflow: TextOverflow.ellipsis,
                      style:
                          theme.textTheme.bodyLarge!.copyWith(height: 1.29))),
              SizedBox(height: 5.v)
            ])));
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_privacy_policy".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Navigates to the profileScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }
}
