import '../../../core/app_export.dart';
import 'selectbankpopuptwo_item_model.dart';

/// This class defines the variables used in the [select_bank_popup_two_screen],
/// and is typically used to hold data that is passed between different parts of the application.
class SelectBankPopupTwoModel {
  Rx<List<SelectbankpopuptwoItemModel>> selectbankpopuptwoItemList = Rx([
    SelectbankpopuptwoItemModel(
      masterCard: ImageConstant.imgMasterCard.obs,
      twoThousandFiveHundredFortyOne: "**** **** **** 2541".obs,
      price: "\$20,000.00".obs,
      image: ImageConstant.imgContrastWhiteA700.obs,
    ),
    SelectbankpopuptwoItemModel(
      masterCard: ImageConstant.imgPaypal.obs,
      twoThousandFiveHundredFortyOne: "**** **** **** 2564".obs,
      price: "\$10,000.00".obs,
      image: ImageConstant.imgContrastWhiteA700.obs,
    ),
    SelectbankpopuptwoItemModel(
      masterCard: ImageConstant.imgStripe.obs,
      twoThousandFiveHundredFortyOne: "**** **** **** 2541".obs,
      price: "\$50,000.00".obs,
      image: ImageConstant.imgContrastWhiteA700.obs,
    ),
  ]);
}
