import '../../../core/app_export.dart';import '../models/confirm_payment_three_model.dart';import 'package:sms_autofill/sms_autofill.dart';import 'package:flutter/material.dart';/// A controller class for the ConfirmPaymentThreeScreen.
///
/// This class manages the state of the ConfirmPaymentThreeScreen, including the
/// current confirmPaymentThreeModelObj
class ConfirmPaymentThreeController extends GetxController with  CodeAutoFill {Rx<TextEditingController> otpController = TextEditingController().obs;

Rx<ConfirmPaymentThreeModel> confirmPaymentThreeModelObj = ConfirmPaymentThreeModel().obs;

@override void codeUpdated() { otpController.value.text = code ?? ''; } 
@override void onInit() { super.onInit(); listenForCode(); } 
 }
