import 'package:payway/widgets/app_bar/custom_app_bar.dart';
import 'package:payway/widgets/app_bar/appbar_leading_image.dart';
import 'package:payway/widgets/app_bar/appbar_subtitle.dart';
import 'package:flutter/material.dart';
import 'package:payway/core/app_export.dart';
import 'controller/settings_controller.dart';

class SettingsScreen extends GetWidget<SettingsController> {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Container(
        width: double.maxFinite,
        padding: EdgeInsets.symmetric(horizontal: 20.h, vertical: 16.v),
        child: Column(
          children: [
            _buildInterrogationTwo(
                interrogationTwo: ImageConstant.imgInfo41,
                title: "lbl_about_us".tr,
                onTapInterrogationTwo: () {
                  onTapInfoFortyOne();
                }),
            SizedBox(height: 24.v),
            _buildInterrogationTwo(
                interrogationTwo: ImageConstant.imgInterrogation2,
                title: "lbl_help".tr,
                onTapInterrogationTwo: () {
                  onTapInterrogationTwo();
                }),
            SizedBox(height: 24.v),
            _buildInterrogationTwo(
                interrogationTwo: ImageConstant.imgCommentAltMiddle2,
                title: "lbl_feedback".tr,
                onTapInterrogationTwo: () {
                  onTapCommentAltMiddleTwo();
                }),
            SizedBox(height: 24.v),
            _buildInterrogationTwo(
                interrogationTwo: ImageConstant.imgPalette,
                title: "Theme Settings",
                onTapInterrogationTwo: () {
                  onTapThemeSettings();
                }),
            SizedBox(height: 5.v),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAppBar() {
    return CustomAppBar(
        leadingWidth: 52.h,
        leading: AppbarLeadingImage(
            imagePath: ImageConstant.imgExpandMoreFil,
            margin: EdgeInsets.only(left: 20.h, top: 20.v, bottom: 18.v),
            onTap: () {
              onTapExpandMoreFIL();
            }),
        title: AppbarSubtitle(
            text: "lbl_settings".tr, margin: EdgeInsets.only(left: 16.h)),
        styleType: Style.bgFill);
  }

  /// Common widget
  Widget _buildInterrogationTwo({
    required String interrogationTwo,
    required String title,
    Function? onTapInterrogationTwo,
  }) {
    return GestureDetector(
        onTap: () {
          onTapInterrogationTwo!.call();
        },
        child: Container(
            padding: EdgeInsets.all(8.h),
            decoration: AppDecoration.outlineBlack
                .copyWith(borderRadius: BorderRadiusStyle.roundedBorder12),
            child: Row(children: [
              Container(
                  height: 48.adaptSize,
                  width: 48.adaptSize,
                  padding: EdgeInsets.all(12.h),
                  decoration: AppDecoration.fillIndigo.copyWith(
                      borderRadius: BorderRadiusStyle.roundedBorder12),
                  child: CustomImageView(
                      imagePath: interrogationTwo,
                      height: 24.adaptSize,
                      width: 24.adaptSize,
                      alignment: Alignment.center)),
              Padding(
                  padding: EdgeInsets.only(left: 12.h, top: 14.v, bottom: 12.v),
                  child: Text(title,
                      style: CustomTextStyles.bodyLargeBlack900
                          .copyWith(color: appTheme.black900))),
              Spacer(),
              CustomImageView(
                  imagePath: ImageConstant.arrowRightGray,
                  height: 24.adaptSize,
                  width: 24.adaptSize,
                  margin: EdgeInsets.only(top: 12.v, right: 8.h, bottom: 12.v))
            ])));
  }

  /// Navigates to the profileScreen when the action is triggered.
  onTapExpandMoreFIL() {
    Get.back();
  }

  /// Navigates to the aboutUsScreen when the action is triggered.
  onTapInfoFortyOne() {
    Get.toNamed(
      AppRoutes.aboutUsScreen,
    );
  }

  /// Navigates to the helpScreen when the action is triggered.
  onTapInterrogationTwo() {
    Get.toNamed(
      AppRoutes.helpScreen,
    );
  }

  /// Navigates to the feedbackScreen when the action is triggered.
  onTapCommentAltMiddleTwo() {
    Get.toNamed(
      AppRoutes.feedbackScreen,
    );
  }

  /// Navigates to the theme settings screen when the action is triggered.
  onTapThemeSettings() {
    Get.toNamed(
      AppRoutes.themeSettingsScreen,
    );
  }
}
