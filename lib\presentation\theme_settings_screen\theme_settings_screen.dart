import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:payway/core/app_export.dart';
import 'package:payway/theme/enhanced_theme_helper.dart';

class ThemeSettingsScreen extends StatefulWidget {
  const ThemeSettingsScreen({Key? key}) : super(key: key);

  @override
  State<ThemeSettingsScreen> createState() => _ThemeSettingsScreenState();
}

class _ThemeSettingsScreenState extends State<ThemeSettingsScreen> {
  String selectedTheme = 'light';
  String selectedColorScheme = 'primary';

  @override
  void initState() {
    super.initState();
    selectedTheme = enhancedThemeHelper.currentTheme;
    selectedColorScheme = enhancedThemeHelper.currentColorScheme;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Theme Settings',
          style: Theme.of(context).textTheme.headlineMedium,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('App Theme'),
            SizedBox(height: 16.h),
            _buildThemeSelector(),
            SizedBox(height: 32.h),
            _buildSectionTitle('Color Scheme'),
            SizedBox(height: 16.h),
            _buildColorSchemeSelector(),
            SizedBox(height: 32.h),
            _buildSectionTitle('Preview'),
            SizedBox(height: 16.h),
            _buildThemePreview(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildThemeSelector() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          children: EnhancedThemeHelper.availableThemes.map((theme) {
            return RadioListTile<String>(
              title: Text(
                _getThemeDisplayName(theme),
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              subtitle: Text(
                _getThemeDescription(theme),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              value: theme,
              groupValue: selectedTheme,
              onChanged: (value) {
                setState(() {
                  selectedTheme = value!;
                });
                enhancedThemeHelper.changeTheme(value!);
              },
              activeColor: Theme.of(context).colorScheme.primary,
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildColorSchemeSelector() {
    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
      ),
      itemCount: EnhancedThemeHelper.availableColorSchemes.length,
      itemBuilder: (context, index) {
        final scheme = EnhancedThemeHelper.availableColorSchemes[index];
        final isSelected = selectedColorScheme == scheme;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              selectedColorScheme = scheme;
            });
            enhancedThemeHelper.changeColorScheme(scheme);
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12.h),
              border: Border.all(
                color: isSelected 
                  ? Theme.of(context).colorScheme.primary 
                  : Colors.transparent,
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.h),
              child: Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: _getSchemeColors(scheme),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 8.h,
                    left: 8.w,
                    child: Text(
                      _getSchemeDisplayName(scheme),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12.fSize,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  if (isSelected)
                    Positioned(
                      top: 8.h,
                      right: 8.w,
                      child: Container(
                        padding: EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.check,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildThemePreview() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Preview',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {},
                    child: Text('Primary Button'),
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {},
                    child: Text('Secondary'),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            TextField(
              decoration: InputDecoration(
                labelText: 'Sample Input',
                hintText: 'Enter text here',
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Icon(Icons.favorite, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 8.w),
                Text(
                  'Sample text with primary color',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getThemeDisplayName(String theme) {
    switch (theme) {
      case 'light':
        return 'Light Theme';
      case 'dark':
        return 'Dark Theme';
      case 'auto':
        return 'Auto (System)';
      default:
        return theme;
    }
  }

  String _getThemeDescription(String theme) {
    switch (theme) {
      case 'light':
        return 'Clean and bright interface';
      case 'dark':
        return 'Easy on the eyes in low light';
      case 'auto':
        return 'Follows system settings';
      default:
        return '';
    }
  }

  String _getSchemeDisplayName(String scheme) {
    switch (scheme) {
      case 'primary':
        return 'Primary';
      case 'ocean':
        return 'Ocean';
      case 'sunset':
        return 'Sunset';
      case 'forest':
        return 'Forest';
      case 'purple':
        return 'Purple';
      case 'golden':
        return 'Golden';
      case 'coral':
        return 'Coral';
      case 'emerald':
        return 'Emerald';
      default:
        return scheme;
    }
  }

  List<Color> _getSchemeColors(String scheme) {
    switch (scheme) {
      case 'ocean':
        return [Color(0xFF1E88E5), Color(0xFF26C6DA)];
      case 'sunset':
        return [Color(0xFFFF7043), Color(0xFFFFB74D)];
      case 'forest':
        return [Color(0xFF4CAF50), Color(0xFF8BC34A)];
      case 'purple':
        return [Color(0xFF9C27B0), Color(0xFFE1BEE7)];
      case 'golden':
        return [Color(0xFFFFC107), Color(0xFFFFD54F)];
      case 'coral':
        return [Color(0xFFFF5722), Color(0xFFFF8A65)];
      case 'emerald':
        return [Color(0xFF00BCD4), Color(0xFF80DEEA)];
      default: // primary
        return [Color(0xFF5486E9), Color(0xFF58B15C)];
    }
  }
} 